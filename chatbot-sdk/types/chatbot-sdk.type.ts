import { ChatMessage } from '../../src/modules/Preview/types/types';

export enum ChatbotTheme {
  Light = 'light',
  Dark = 'dark',
}

export interface ChatbotConfig {
  // Theming
  primaryColor?: string;
  secondaryColor?: string;
  tertiaryColor?: string;
  botBubbleColor?: string;
  userBubbleColor?: string;
  fontFamily?: string;
  fontSize?: string; // Optional font size (e.g., '14px', '1rem')
  theme?: ChatbotTheme; // Optional theme setting

  // Functionality
  initialMessage?: string;
  placeholderText?: string;
  useWebSocket?: boolean;
  webSocketUrl?: string;

  // Branding
  botName?: string;
  botAvatarUrl?: string;
  userAvatarUrl?: string;
  token?: string;
  sessionId?: string;
  botId: string;
  botDescription?: string;

  // Event Hooks
  onMessageReceived?: (message: ChatMessage) => void;
  onMessageSent?: (message: ChatMessage) => void;
  onTyping?: (isTyping: boolean) => void;
  onBotTypingStart?: () => void;
  onBotTypingEnd?: () => void;
  onSessionStart?: (sessionId: string) => void;
  onSessionEnd?: (sessionId: string) => void;
}
