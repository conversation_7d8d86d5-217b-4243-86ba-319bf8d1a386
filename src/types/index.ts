export * from './api.type';
export * from './language.type';
export * from './faq.type';
export * from './intent.type';
export * from './entity.type';
export * from './ui.type';
export * from './pagination.type';
export * from './botInteraction.type';
export * from './flow.type';
export * from './whatsapp.type';
export * from './channel.type';
export * from './build.type';
import { StencilNodesType } from '@/modules/editor/utils/constants';
import { DateFilter, StatusFilter } from '@/types/enums/enums';

// Core Types
export interface UserDetails {
  realm_access: {
    roles: string[];
  };
  scope: string;
}

export interface Template {
  id: string;
  name: string;
  desc: string;
  type: string;
  svg: string;
}

export interface DropdownOption {
  value: string;
  label: React.ReactNode;
  icon?: React.ReactNode;
}

export interface ModalTypeDetails {
  id: string;
  type: StencilNodesType;
  top: number;
  left: number;
}

export interface AutoSuggestionPosition {
  top: number;
  left: number;
  element: any;
}

// Error Types
export interface ApiError {
  message: string;
  status: number;
  data?: any;
}

export interface ValidationError {
  field: string;
  message: string;
}

// Node Types
export type NodeType =
  | 'appStart'
  | 'appEnd'
  | 'whatsapp'
  | 'voice'
  | 'email'
  | 'sms'
  | 'webhook'
  | 'http'
  | 'choice'
  | 'repeat'
  | 'script'
  | 'addcontact';

export interface NodeConfig {
  icon: string;
  text: string;
  description: string;
  tags: string[];
  enabled: boolean;
  type: 'Channels' | 'Utilities' | 'Marketplace';
}

export interface UIState {
  viewType: 'grid' | 'list';
  isMyFlows: boolean;
  showFilterDialog: boolean;
  loader: boolean;
}

export interface Bot {
  id: string;
  name: string;
  description?: string;
  status?: string;
  domain?: string;
  image?: string;
  lastUpdated?: string;
  createdAt?: string;
  updatedAt?: string;
  success: boolean;
  timestamp: string;
}

export interface DomainOption {
  value: string;
  label: string;
}

export interface FilterOptions {
  dateFilter: DateFilter;
  statusFilter: StatusFilter;
  searchTerm: string;
}

export interface FlowVersion {
  appid: string;
  version: string;
  comment: string;
  owner: number;
  createdBy: number;
  createdAt: string;
  type: string;
  version_name: string | null;
}
