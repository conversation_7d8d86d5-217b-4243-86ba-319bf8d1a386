@import 'rappid/rappid.css';

.studio-root {
  height: calc(100vh - 85px);
  display: block;
  flex-direction: column;
  overflow: hidden;
  position: relative;
  z-index: 1;
}

.full-screen {
  height: calc(100% - 80px);
  position: relative;
}

#root {
  height: 100%;
  width: 100%;
}

/* * {
  font-family: "Nunito";
} */

/* Style Pallete */
.Pallete {
  width: 200px;
  /* Set width to desired size */
  overflow-y: auto;
  /* Enable vertical scrolling */
}

.joint-stencil.joint-theme-default {
  border: none;
}

.canvas {
  /* position: absolute; */
  right: 0;
  top: 80px;
  left: 0;
  bottom: 0;
  height: 100%;
  overflow: scroll;
}

.canvasVersion {
  /* position: absolute; */
  right: 0;
  left: 0;
  bottom: 0;
  height: calc(120%);
  overflow: scroll;
}

.group-label {
  display: none;
}

.joint-stencil .group {
  padding: 0;
}

.joint-stencil:after {
  display: none;
}

.joint-stencil.joint-theme-default .group {
  border-top: none !important;
  border-bottom: 1px solid #e9e9e9;
  padding: 15px 25px;
}

.animate-link {
  animation: animateLink 2s linear infinite;
}

@keyframes animateLink {
  to {
    stroke-dashoffset: -10;
  }
}

.preview-root {
  display: block;
  flex-direction: column;
  overflow: hidden;
  position: relative;
  z-index: 1;
  height: 100%;
  width: 100%;
}
.preview-root .studio-root {
  height: 100%;
  width: 100%;
}
.preview-root .studio-root .canvas-wrapper {
  height: 100% !important;
}
