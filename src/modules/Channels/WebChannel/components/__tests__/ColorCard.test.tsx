import { render, screen, fireEvent } from '@testing-library/react';
import { describe, expect, it, vi } from 'vitest';
import ColorCard from '../ColorCard';

describe('ColorCard', () => {
  const defaultProps = {
    color: '#FF0000',
    onColorChange: vi.fn(),
    label: 'Choose Color',
  };

  it('renders with the correct color and label', () => {
    render(<ColorCard {...defaultProps} />);

    // Check if label is rendered
    expect(screen.getByText('Choose Color')).toBeInTheDocument();

    // Check if color div is rendered with correct background color
    const colorDiv = screen.getByTestId('color-preview');
    expect(colorDiv).toHaveStyle({ backgroundColor: '#FF0000' });
  });

  it('calls onColorChange when color is changed', () => {
    render(<ColorCard {...defaultProps} />);

    const colorInput = screen.getByTestId('color-input') as HTMLInputElement;
    expect(colorInput).toBeInTheDocument();

    fireEvent.change(colorInput, { target: { value: '#00ff00' } });
    expect(defaultProps.onColorChange).toHaveBeenCalledWith('#00ff00');
  });

  it('opens color picker when clicking on the card', () => {
    render(<ColorCard {...defaultProps} />);

    const mockClick = vi.fn();
    const colorInput = screen.getByTestId('color-input') as HTMLInputElement;
    colorInput.click = mockClick;

    const card = screen.getByTestId('color-preview');
    fireEvent.click(card.parentElement!);

    expect(mockClick).toHaveBeenCalled();
  });

  it('has accessible clickable area', () => {
    render(<ColorCard {...defaultProps} />);

    const clickableArea = screen.getByTestId('color-preview').parentElement;
    expect(clickableArea).toHaveClass(
      'flex gap-2 border-dashed relative border-spacing-2 border border-tertiary-400 p-1 rounded'
    );
  });
});
