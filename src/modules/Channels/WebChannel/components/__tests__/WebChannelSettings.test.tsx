import { render, screen, fireEvent } from '@testing-library/react';
import { describe, expect, it, vi } from 'vitest';
import WebChannelSettings from '../WebChannelSettings';
import { WebChannelConfig } from '../../types';

// Mock the translations
vi.mock('react-i18next', () => ({
  useTranslation: () => ({
    t: (key: string) =>
      ({
        'channels.chatWidget': 'Chat Widget',
        'channels.widgetSettings': 'Widget Settings',
        'channels.deploy': 'Deploy',
      })[key],
  }),
}));

// Mock the tab persistence hook
vi.mock('@/hooks/useTabPersistence', () => {
  let currentTab = 'widget-settings';
  return {
    useTabPersistence: ({ defaultTab }: { defaultTab: string }) => {
      currentTab = defaultTab;
      const setTab = (newTab: string) => {
        currentTab = newTab;
      };
      return [currentTab, setTab];
    },
  };
});

// Mock the config
vi.mock('../config', () => ({
  webChannelConfigurationTab: [
    { id: 'widget-settings', labelKey: 'channels.widgetSettings', icon: vi.fn() },
    { id: 'deploy', labelKey: 'channels.deploy', icon: vi.fn() },
  ],
}));

describe('WebChannelSettings', () => {
  const mockConfig: WebChannelConfig = {
    botId: 'test-bot-id',
    botName: 'Test Bot',
    botDescription: 'A test bot',
    primaryColor: '#000000',
    secondaryColor: '#ffffff',
    tertiaryColor: '#cccccc',
    fontFamily: 'Arial',
    fontSize: '16px',
    botAvatarUrl: 'https://example.com/avatar.png',
  };

  const mockOnConfigChange = vi.fn();

  const renderComponent = () => {
    return render(<WebChannelSettings config={mockConfig} onConfigChange={mockOnConfigChange} />);
  };

  it('renders the chat widget title', () => {
    renderComponent();
    expect(screen.getByText('Chat Widget')).toBeInTheDocument();
  });

  it('renders tab navigation with correct initial tab', () => {
    renderComponent();
    expect(screen.getByRole('tablist')).toBeInTheDocument();
    expect(screen.getByTestId('tab-widget-settings')).toBeInTheDocument();
    expect(screen.getByTestId('tab-deploy')).toBeInTheDocument();
  });

  it('shows widget settings content by default', () => {
    renderComponent();
    const widgetSettingsTab = screen.getByTestId('tab-widget-settings');
    expect(widgetSettingsTab).toHaveAttribute('data-state', 'active');
    const widgetContent = screen.getByTestId('widget-settings-content');
    expect(widgetContent).toBeInTheDocument();
  });

  it('switches content when clicking deploy tab', () => {
    render(<WebChannelSettings config={mockConfig} onConfigChange={mockOnConfigChange} />);

    const deployTab = screen.getByTestId('tab-deploy');
    fireEvent.click(deployTab);

    // Verify the content change
    expect(screen.getByRole('tabpanel')).toBeInTheDocument();
  });

  it('passes config to widget settings content', () => {
    renderComponent();

    // Check if WidgetSettingsContent receives the correct props
    const widgetContent = screen.getByTestId('widget-settings-content');
    expect(widgetContent).toBeInTheDocument();
    expect(widgetContent).toHaveAttribute('data-config', JSON.stringify(mockConfig));
  });

  it('renders widget settings content with config', () => {
    renderComponent();

    // Check if WidgetSettingsContent receives the correct props
    const widgetContent = screen.getByTestId('widget-settings-content');
    expect(widgetContent).toBeInTheDocument();
    expect(widgetContent).toHaveAttribute('data-config', JSON.stringify(mockConfig));
  });

  it('handles configuration changes', () => {
    renderComponent();

    // Trigger a config change from widget settings
    const nameInput = screen.getByTestId('bot-name-input');
    fireEvent.change(nameInput, { target: { value: 'New Bot Name' } });

    expect(mockOnConfigChange).toHaveBeenCalledWith('botName', 'New Bot Name');
  });
});
