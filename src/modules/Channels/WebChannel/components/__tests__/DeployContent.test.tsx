import { render, screen, fireEvent } from '@testing-library/react';
import { describe, expect, it, vi, beforeEach } from 'vitest';
import DeployContent from '../DeployContent';
import { WebChannelConfig } from '../../types';

// Mock translations
vi.mock('react-i18next', () => ({
  useTranslation: () => ({
    t: (key: string) =>
      ({
        'channels.launchTheBot': 'Launch the Bot',
        'channels.web': 'Web',
        'channels.copyCodeInstructions': 'Copy the code below and paste it into your website.',
        'channels.experienceOnWebsite': 'Experience on Website',
        'channels.copyLinkToShareBot': 'Copy Link to Share Bot',
      })[key],
  }),
}));

// Mock router param hook
vi.mock('@/hooks/useRouterParam', () => ({
  useBotIdParam: () => ({ botId: 'test-bot-id' }),
}));

describe('DeployContent', () => {
  const mockConfig: WebChannelConfig = {
    botId: 'test-bot-id',
    botName: 'Test Bot',
    botDescription: 'A test bot',
    primaryColor: '#000000',
    secondaryColor: '#ffffff',
    tertiaryColor: '#cccccc',
    fontFamily: 'Arial',
    fontSize: '16px',
    botAvatarUrl: 'https://example.com/avatar.png',
  };

  const mockClipboard = {
    writeText: vi.fn(),
  };

  beforeEach(() => {
    // Mock clipboard API
    vi.stubGlobal('navigator', {
      clipboard: mockClipboard,
    });
  });

  it('renders with correct title and buttons', () => {
    render(<DeployContent config={mockConfig} />);

    expect(screen.getByText('Launch the Bot')).toBeInTheDocument();
    expect(screen.getByText('Web')).toBeInTheDocument();
    expect(screen.getByText('Mobile SDK')).toBeInTheDocument();
  });

  it('displays embed code with correct configuration', () => {
    render(<DeployContent config={mockConfig} />);

    const codeElement = screen.getByText(/ChatbotSDK\.init/);
    expect(codeElement).toBeInTheDocument();

    // Verify config values are in the embed code
    const embedCode = codeElement.textContent;
    expect(embedCode).toContain(`botName: '${mockConfig.botName}'`);
    expect(embedCode).toContain(`botDescription: '${mockConfig.botDescription}'`);
    expect(embedCode).toContain(`primaryColor: '${mockConfig.primaryColor}'`);
  });

  it('copies embed code when copy button is clicked', async () => {
    render(<DeployContent config={mockConfig} />);

    const copyButton = screen.getByRole('button', { name: '' }); // Copy button has no text, only icon
    fireEvent.click(copyButton);

    expect(mockClipboard.writeText).toHaveBeenCalledTimes(1);
    const expectedCode = expect.stringContaining('ChatbotSDK.init');
    expect(mockClipboard.writeText).toHaveBeenCalledWith(expectedCode);
  });

  it('renders action buttons with correct text and icons', () => {
    render(<DeployContent config={mockConfig} />);

    expect(screen.getByText('Experience on Website')).toBeInTheDocument();
    expect(screen.getByText('Copy Link to Share Bot')).toBeInTheDocument();
  });

  it('displays instructions text', () => {
    render(<DeployContent config={mockConfig} />);

    expect(
      screen.getByText('Copy the code below and paste it into your website.')
    ).toBeInTheDocument();
  });

  it('includes bot ID from router params in embed code', () => {
    render(<DeployContent config={mockConfig} />);

    const codeElement = screen.getByText(/ChatbotSDK\.init/);
    expect(codeElement.textContent).toContain(`botId: 'test-bot-id'`);
  });
});
