import { PlusCircle } from 'lucide-react';
import React from 'react';

interface ColorCardProps {
  color: string;
  onColorChange: (color: string) => void;
  label: string;
}

const ColorCard: React.FC<ColorCardProps> = props => {
  const { color, onColorChange, label } = props;
  const inputRef = React.useRef<HTMLInputElement>(null);

  const handleColorDivClick = () => {
    inputRef.current?.click();
  };

  return (
    <div
      className="flex gap-2 border-dashed relative border-spacing-2 border border-tertiary-400 p-1 rounded"
      onClick={handleColorDivClick}
      data-testid={'color-card'}
    >
      <div
        data-testid="color-preview"
        className="w-16 h-16 rounded-md cursor-pointer"
        style={{ backgroundColor: color }}
      ></div>

      <div className="flex flex-col gap-2">
        <label className="text-sm text-tertiary-600 flex items-center gap-1 flex-1">
          <PlusCircle className="border-tertiary-300 w-5 h-5" />

          <span className="text-xs">{label}</span>
        </label>
        <input
          ref={inputRef}
          data-testid="color-input"
          type="color"
          value={color}
          onChange={e => onColorChange(e.target.value)}
          className="w-0 h-0 absolute"
        />
      </div>
    </div>
  );
};

export default ColorCard;
