import React from 'react';
import GenericTabbedModule from '@/modules/common/GenericTabbedModule';
import GenericMainContent from '@/modules/common/GenericMainContent';
import { AgentTransferFilterType, AgentTransferMainTab } from './enums';
import { availableAgentTransfers, filters, mainAgentTransferTab, myAgentTransfers } from './config';
import MainContent from './MainContent';
import { GenericItem } from '../common/types';

const AgentTransfersTab: React.FC = () => {
  const renderAgentTransferContent = (item: GenericItem) => {
    const selectedAgentTransfer = availableAgentTransfers.find(transfer => transfer.id === item.id);

    if (!selectedAgentTransfer) {
      return (
        <GenericMainContent
          selectedItem={null}
          renderContent={() => null}
          emptyStateTitleKey="agentTransfer.selectAgentTransfer"
          emptyStateDescriptionKey="agentTransfer.nothingSelected"
        />
      );
    }

    return (
      <GenericMainContent
        selectedItem={selectedAgentTransfer}
        renderContent={() => <MainContent selectedAgentTransfer={selectedAgentTransfer} />}
        emptyStateTitleKey="agentTransfer.selectAgentTransfer"
        emptyStateDescriptionKey="agentTransfer.nothingSelected"
      />
    );
  };

  return (
    <GenericTabbedModule
      mainTabEnum={AgentTransferMainTab}
      filterTypeEnum={AgentTransferFilterType}
      availableItems={availableAgentTransfers}
      myItems={myAgentTransfers}
      filters={filters}
      mainTabsConfig={mainAgentTransferTab}
      emptyStateTitleKey="agentTransfer.selectAgentTransfer"
      emptyStateDescriptionKey="agentTransfer.nothingSelected"
      renderMainContent={renderAgentTransferContent}
      hideTabNavigation={true}
    />
  );
};

export default AgentTransfersTab;
