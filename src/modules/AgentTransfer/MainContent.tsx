import React, { useEffect, useState } from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';
import { AgentTransfer } from './types';
import { agentTransferPlatformConfigs } from './config';
import { Copy, Share2 } from 'lucide-react';
import { useTranslation } from 'react-i18next';
import { useAddChannelMutation, useGetChannelDetailsQuery } from '@/store/api/chatBotApi';
import { useBotIdParam } from '@/hooks/useRouterParam';
import { Loader } from '@/components/Loader';

interface MainContentProps {
  selectedAgentTransfer: AgentTransfer;
}

const MainContent: React.FC<MainContentProps> = ({ selectedAgentTransfer }) => {
  const { t } = useTranslation();
  const [showGeneratedTokenUI, setShowGeneratedTokenUI] = useState(false);
  const [accessToken] = useState('');
  const [addChannelMutation, { isLoading }] = useAddChannelMutation();
  const { botId } = useBotIdParam();
  const { data: channelDetails, isLoading: channelDetailsLoading } = useGetChannelDetailsQuery({
    botId,
  });

  const platformConfig = agentTransferPlatformConfigs.find(
    config => config.id === selectedAgentTransfer.id
  );

  useEffect(() => {
    if (channelDetails) {
      setShowGeneratedTokenUI(true);
    }
  }, [channelDetails]);

  // Define Zod schema dynamically based on platformConfig
  const formSchema = React.useMemo(() => {
    const schema: Record<string, z.ZodTypeAny> = {};
    platformConfig?.fields.forEach(field => {
      if (field.required) {
        schema[field.fieldName] = z.string().min(1, `${field.fieldLabel} is required`);
      } else {
        schema[field.fieldName] = z.string().optional().or(z.literal('')); // Allow empty string for optional fields
      }
    });
    return z.object(schema);
  }, [platformConfig]);

  type FormData = z.infer<typeof formSchema>;

  const {
    register,
    handleSubmit,
    formState: { errors, isValid },
    getValues,
  } = useForm<FormData>({
    resolver: zodResolver(formSchema),
    mode: 'onChange',
  });

  const handleGenerateToken = async (data: FormData) => {
    // Simulate API call and token generation
    const response = await addChannelMutation({
      botId: botId,
      channelId: data.channelId,
      clientId: data.clientId,
    });
    if (response?.data) {
      setShowGeneratedTokenUI(true);
    }
  };

  const handleCopyToken = () => {
    navigator.clipboard.writeText(accessToken);
    // Optionally, show a toast notification
  };

  const handleShareToken = () => {
    // Implement share functionality
  };

  const handleRemoveAgentTransfer = () => {
    // Implement remove functionality
  };

  if (isLoading || channelDetailsLoading) {
    return <Loader />;
  }

  return (
    <div className="w-full h-full bg-white opacity-100 px-10 py-10">
      {!showGeneratedTokenUI ? (
        <>
          {/* Heading */}
          <h2 className="w-48 h-6 text-left font-medium text-base leading-[25px] tracking-normal text-foreground">
            {t('agentTransfer.setupHeading')} {selectedAgentTransfer.name}
          </h2>

          {/* Description */}
          <p className="w-auto h-5 mt-3 text-left font-normal text-sm leading-[21px] tracking-normal text-tertiary-500">
            {t('agentTransfer.setupDescription', { agentTransferName: selectedAgentTransfer.name })}
          </p>

          {/* Input Fields */}
          <div className="flex flex-col gap-4 mt-10">
            {platformConfig?.fields.map(field => (
              <div key={field.fieldName} className="flex flex-col">
                <input
                  type={field.fieldType}
                  placeholder={field.fieldLabel}
                  {...register(field.fieldName as keyof FormData)}
                  className="border border-tertiary-300 rounded-md px-3 py-2 w-full text-sm focus:outline-none"
                />
                {errors[field.fieldName as keyof FormData] && (
                  <p className="text-error-500 text-xs mt-1">
                    {errors[field.fieldName as keyof FormData]?.message as string}
                  </p>
                )}
              </div>
            ))}
          </div>

          {/* Button */}
          <div className="flex justify-end">
            <button
              onClick={handleSubmit(handleGenerateToken)}
              className={`mt-6 px-6 py-2 rounded-md text-sm font-medium text-white ${
                isValid ? 'bg-primary-500' : 'bg-primary-100 cursor-not-allowed'
              }`}
              disabled={!isValid}
            >
              {t('agentTransfer.generateToken')}
            </button>
          </div>
        </>
      ) : (
        <>
          <div className="flex justify-between items-center mb-4">
            <h2 className="text-xl font-semibold text-foreground flex items-center">
              {t('agentTransfer.liveAgentPortalDetails')}
              <span className="ml-2 px-2 py-1 text-xs font-medium text-success bg-success-100 rounded-md">
                {t('agentTransfer.liveStatus')}
              </span>
            </h2>
            <button
              onClick={handleRemoveAgentTransfer}
              className="text-primary-500 text-sm font-normal uppercase"
            >
              {t('agentTransfer.remove')}
            </button>
          </div>

          <div className="bg-white mt-5 p-6 rounded-lg shadow-md border border-tertiary-200">
            {platformConfig?.fields.map(field => (
              <div className="mb-4" key={field.fieldName}>
                <p className="text-secondary-500 text-sm">
                  {field.fieldLabel}:{' '}
                  <span className="text-tertiary-600 font-medium">
                    {channelDetails
                      ? channelDetails?.data?.[field.fieldName]
                      : getValues(field.fieldName as keyof FormData)}
                  </span>
                </p>
              </div>
            ))}
          </div>

          <div className="flex justify-between items-center mt-6">
            <p className="text-secondary-500 text-sm">{t('agentTransfer.shareInstruction')}</p>
            <div className="flex gap-4">
              <button onClick={handleShareToken} className="text-primary-500">
                <Share2 size={16} />
              </button>
              <button onClick={handleCopyToken} className="text-primary-500">
                <Copy size={16} />
              </button>
            </div>
          </div>
        </>
      )}
    </div>
  );
};

export default MainContent;
