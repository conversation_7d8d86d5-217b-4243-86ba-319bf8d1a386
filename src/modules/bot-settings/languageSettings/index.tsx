import React, { useState, useEffect, useMemo } from 'react';
import { useTranslation } from 'react-i18next';
import { useSearchParams } from 'react-router-dom';

import {
  useGetLanguagesQuery,
  useGetBotLanguagesQuery,
  useCreateBulkBotLanguagesMutation,
  useDeleteBotLanguageMutation,
} from '@/store/api/languageApi';

import { Language, OrderDirection } from '@/types';
import { Button } from '@/components/ui/button';
import { useBotIdParam } from '@/hooks/useRouterParam';
import { LanguageNode } from '../types';
import { cn } from '@/lib/utils';

import AvailableLanguages from './AvailableLanguages';
import SelectedLanguages from './SelectedLanguages';
import { toast } from '@/hooks/use-toast';
import SuccessToastMessage from '@/components/SuccessToastMessage';
import DeleteConfirmationModal from '@/components/DeleteConfirmationModal';

const LanguageSettings: React.FC = () => {
  const { t } = useTranslation();
  const { botId } = useBotIdParam();
  const [_, setSearchParams] = useSearchParams();

  const { data: allLanguagesData } = useGetLanguagesQuery({
    order: [['name', OrderDirection.ASC]],
    limit: 100,
  });

  const { data: botLanguagesData } = useGetBotLanguagesQuery({
    filter: { botId: { eq: botId } },
    order: [['isDefault', OrderDirection.DESC]],
    limit: 100,
  });

  const [createBulkBotLanguages] = useCreateBulkBotLanguagesMutation();
  const [deleteBotLanguage, { isLoading: isDeletingBotLanguage }] = useDeleteBotLanguageMutation();

  const allLanguages = allLanguagesData?.data?.items || [];
  const botLanguages = botLanguagesData?.data?.items || [];

  const allLanguagesMap = useMemo(() => {
    return allLanguages.reduce<Record<string, Language>>((acc, lang) => {
      acc[lang.id] = lang;
      return acc;
    }, {});
  }, [allLanguages]);

  const initialSelectedLanguages = useMemo<LanguageNode[]>(() => {
    return botLanguages.map(bl => {
      const lang = allLanguagesMap[bl.langId];
      return {
        id: bl.langId,
        name: lang?.name || '',
        nativeName: lang?.nativeName || '',
        code: lang?.code || '',
        isDefault: bl.isDefault,
      };
    });
  }, [botLanguages, allLanguagesMap]);

  const [selectedLanguages, setSelectedLanguages] = useState<LanguageNode[]>([]);
  const [searchTerm, setSearchTerm] = useState('');
  const [isSaving, setIsSaving] = useState(false);
  const [languageToDelete, setLanguageToDelete] = useState<LanguageNode | null>(null);

  useEffect(() => {
    setSelectedLanguages(initialSelectedLanguages);
  }, [initialSelectedLanguages]);

  const handleAddLanguage = (language: LanguageNode) => {
    setSelectedLanguages(prev => [...prev, language]);
  };

  const handleRemoveLanguage = (languageToRemove: LanguageNode) => {
    setLanguageToDelete(languageToRemove);
  };

  const confirmRemoveLanguage = async () => {
    if (!languageToDelete) return;

    try {
      const botLanguageToDelete = botLanguages.find(bl => bl.langId === languageToDelete.id);
      if (botLanguageToDelete) {
        await deleteBotLanguage({ id: botLanguageToDelete.id }).unwrap();
        setSelectedLanguages(prev => prev.filter(lang => lang.id !== languageToDelete.id));
        toast({
          title: <SuccessToastMessage message={t('settings.languageDisabledSuccessfully')} />,
        });
      }
    } catch (error) {
      console.error('Failed to disable language:', error);
      toast({
        title: t('common.error'),
        description: 'Failed to disable language.',
        variant: 'destructive',
      });
    } finally {
      setLanguageToDelete(null);
    }
  };

  const handleSave = async () => {
    setIsSaving(true);
    const selectedLanguageIds = selectedLanguages.map(lang => lang.id);
    const initialIds = new Set(initialSelectedLanguages.map(lang => lang.id));
    const currentIds = new Set(selectedLanguageIds);

    const toAdd = [...currentIds].filter(id => !initialIds.has(id));

    try {
      if (toAdd.length > 0) {
        await createBulkBotLanguages({ botId, ids: toAdd }).unwrap();
      }

      toast({
        title: <SuccessToastMessage message={t('settings.languagesSaved')} />,
      });
    } catch (error: any) {
      console.error('Failed to save language settings:', error);
      toast({
        title: t('common.error'),
        description: error,
        variant: 'destructive',
      });
    } finally {
      setIsSaving(false);
    }
  };

  const handleGoBack = () => {
    setSearchParams(prev => {
      const params = new URLSearchParams(prev);
      params.delete('tab');
      return params;
    });
  };

  const hasChanges = useMemo(() => {
    if (selectedLanguages.length !== initialSelectedLanguages.length) {
      return true;
    }
    const selectedIds = new Set(selectedLanguages.map(lang => lang.id));
    const initialIds = new Set(initialSelectedLanguages.map(lang => lang.id));

    for (const id of selectedIds) {
      if (!initialIds.has(id)) return true;
    }
    for (const id of initialIds) {
      if (!selectedIds.has(id)) return true;
    }
    return false;
  }, [selectedLanguages, initialSelectedLanguages]);

  return (
    <div className="bg-secondary-50 h-full pb-16">
      <div className="h-full overflow-y-auto p-6">
        <div className="max-w-7xl mx-auto h-full">
          <h2 className="text-xl text-foreground px-6">{t('settings.languages')}</h2>

          <SelectedLanguages
            selectedLanguages={selectedLanguages}
            onRemoveLanguage={handleRemoveLanguage}
          />

          <AvailableLanguages
            allLanguages={allLanguages}
            selectedLanguages={selectedLanguages}
            searchTerm={searchTerm}
            onAddLanguage={handleAddLanguage}
            onSearchTermChange={setSearchTerm}
          />
        </div>
      </div>

      <div className={cn('absolute bottom-0 left-0 right-0 bg-background p-4 shadow-top z-10')}>
        <div className="max-w-7xl mx-auto flex justify-end">
          <Button variant="outline" className="mr-3 px-6" onClick={handleGoBack}>
            {t('common.goBack')}
          </Button>
          <Button className="px-8" onClick={handleSave} disabled={!hasChanges || isSaving}>
            {isSaving ? t('common.saving') : t('common.save')}
          </Button>
        </div>
      </div>

      <DeleteConfirmationModal
        isOpen={!!languageToDelete}
        onClose={() => setLanguageToDelete(null)}
        onConfirm={confirmRemoveLanguage}
        title={t('settings.disableLanguageConfirmationTitle')}
        description={t('settings.disableLanguageConfirmationMessage')}
        isDeleting={isDeletingBotLanguage}
      />
    </div>
  );
};

export default LanguageSettings;
