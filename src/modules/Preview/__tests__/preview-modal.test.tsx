import React from 'react';
import { render, screen } from '@testing-library/react';
import { vi, type Mock } from 'vitest';
import PreviewModal from '../preview-modal';
import * as reduxHooks from '@/hooks/useRedux';

// Mocks for hooks and dependencies
vi.mock('@/hooks/useRedux', () => ({
  useAppDispatch: () => vi.fn(),
  useAppSelector: vi.fn(fn => fn({ ui: { showPreview: true } })),
}));
vi.mock('@/hooks/useRouterParam', () => ({ useBotIdParam: () => ({ botId: 'test-bot' }) }));
vi.mock('react-i18next', () => ({ useTranslation: () => ({ t: (k: string) => k }) }));
vi.mock('@/store/slices/uiSlice', () => ({ togglePreview: vi.fn() }));
vi.mock('@/store/api', () => ({
  useResetConversationMutation: () => [vi.fn().mockResolvedValue({}), { isLoading: false }],
}));
vi.mock('@/hooks/useWebSocket', () => ({
  useWebSocket: () => ({
    emit: vi.fn(),
    on: () => () => {},
    userId: 'user-1',
  }),
}));
vi.mock('@/hooks/use-toast', () => ({ useToast: () => ({ toast: vi.fn() }) }));
vi.mock('../previewHeader', () => ({ default: () => <div data-testid="preview-header" /> }));

// Helper to update selector
const setShowPreview = (show: boolean) => {
  (reduxHooks.useAppSelector as unknown as Mock).mockImplementation((fn: any) =>
    fn({ ui: { showPreview: show } })
  );
};

describe('PreviewModal', () => {
  afterEach(() => {
    vi.clearAllMocks();
  });

  it('renders modal when showPreview is true', () => {
    setShowPreview(true);
    render(<PreviewModal />);
    expect(screen.getByTestId('preview-header')).toBeInTheDocument();
  });

  it('does not render modal when showPreview is false', () => {
    setShowPreview(false);
    render(<PreviewModal />);
    expect(screen.queryByTestId('preview-header')).not.toBeInTheDocument();
  });
});
