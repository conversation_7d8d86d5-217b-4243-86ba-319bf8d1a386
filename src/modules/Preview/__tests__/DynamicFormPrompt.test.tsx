import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { vi } from 'vitest';
import DynamicFormPrompt from '../DynamicFormPrompt';
import { FormFieldType, SenderType, RendererType, FormType } from '../types/enums';
import type { ChatMessage } from '../types/types';

// Mock translations
vi.mock('react-i18next', () => ({
  useTranslation: () => ({
    t: (key: string) => key,
  }),
}));

interface TimePickerProps {
  value: string;
  onChange: (value: string) => void;
}

interface DatePickerProps {
  onSelect: (date: Date) => void;
}

interface PhoneInputProps {
  value: string;
  onChange: (value: string) => void;
}

// Mock the TimePicker component
vi.mock('@/components/time-picker', () => ({
  default: ({ value, onChange }: TimePickerProps) => (
    <input
      data-testid="time-picker"
      type="time"
      value={value}
      onChange={e => onChange(e.target.value)}
    />
  ),
}));

// Mock the DatePicker component
vi.mock('@/components/DatePicker/date-picker', () => ({
  DatePicker: ({ onSelect }: DatePickerProps) => (
    <input
      data-testid="date-picker"
      type="date"
      onChange={e => onSelect(new Date(e.target.value))}
    />
  ),
}));

// Mock PhoneInput
vi.mock('react-international-phone', () => ({
  PhoneInput: ({ value, onChange }: PhoneInputProps) => {
    const handleChange = (_e: React.ChangeEvent<HTMLInputElement>) => {
      // Always format to a valid US test number
      onChange('+12025550123');
    };
    return <input data-testid="phone-input" type="tel" value={value} onChange={handleChange} />;
  },
}));

describe('DynamicFormPrompt', () => {
  const mockOnSubmit = vi.fn();
  const defaultMessage: ChatMessage = {
    sender: SenderType.BOT,
    nodeType: RendererType.FORM,
    data: {
      prompt: [
        {
          fieldName: 'name',
          fieldType: FormFieldType.TEXT,
          label: 'Name',
          required: true,
        },
      ],
      formType: FormType.SINGLE_ASK_FORM,
    },
  };

  beforeEach(() => {
    mockOnSubmit.mockClear();
  });

  it('renders form with text input field', () => {
    render(
      <DynamicFormPrompt message={defaultMessage} formDisabled={false} onSubmit={mockOnSubmit} />
    );

    const input = screen.getByTestId('text-input');
    expect(input).toBeInTheDocument();
    expect(screen.getByRole('button', { name: 'common.submit' })).toBeInTheDocument();
  });

  it('handles form submission with valid data', async () => {
    render(
      <DynamicFormPrompt message={defaultMessage} formDisabled={false} onSubmit={mockOnSubmit} />
    );

    await userEvent.type(screen.getByTestId('text-input'), 'John Doe');
    await userEvent.click(screen.getByRole('button', { name: 'common.submit' }));

    await waitFor(() => {
      expect(mockOnSubmit).toHaveBeenCalledWith({ name: 'John Doe' });
    });
  });

  it('shows validation errors for required fields', async () => {
    render(
      <DynamicFormPrompt message={defaultMessage} formDisabled={false} onSubmit={mockOnSubmit} />
    );

    await userEvent.click(screen.getByRole('button', { name: 'common.submit' }));
    expect(mockOnSubmit).not.toHaveBeenCalled();
  });

  it('disables form when formDisabled is true', () => {
    render(
      <DynamicFormPrompt message={defaultMessage} formDisabled={true} onSubmit={mockOnSubmit} />
    );

    expect(screen.getByTestId('text-input')).toBeDisabled();
    expect(screen.getByRole('button', { name: 'common.submit' })).toBeDisabled();
  });

  it('renders time picker field correctly', () => {
    const timeMessage: ChatMessage = {
      sender: SenderType.BOT,
      nodeType: RendererType.FORM,
      data: {
        prompt: [
          {
            fieldName: 'time',
            fieldType: FormFieldType.TIME,
            label: 'Time',
            required: true,
          },
        ],
        formType: FormType.MULTI_ASK_FORM,
      },
    };

    render(
      <DynamicFormPrompt message={timeMessage} formDisabled={false} onSubmit={mockOnSubmit} />
    );

    expect(screen.getByTestId('time-picker')).toBeInTheDocument();
  });

  it('renders date picker field correctly', () => {
    const dateMessage: ChatMessage = {
      sender: SenderType.BOT,
      nodeType: RendererType.FORM,
      data: {
        prompt: [
          {
            fieldName: 'date',
            fieldType: FormFieldType.DATE,
            label: 'Date',
            required: true,
          },
        ],
        formType: FormType.MULTI_ASK_FORM,
      },
    };

    render(
      <DynamicFormPrompt message={dateMessage} formDisabled={false} onSubmit={mockOnSubmit} />
    );

    expect(screen.getByTestId('date-picker')).toBeInTheDocument();
  });

  it('renders phone input field correctly', () => {
    const phoneMessage: ChatMessage = {
      sender: SenderType.BOT,
      nodeType: RendererType.FORM,
      data: {
        prompt: [
          {
            fieldName: 'phone',
            fieldType: FormFieldType.MOBILE_NUMBER,
            label: 'Phone',
            required: true,
          },
        ],
        formType: FormType.MULTI_ASK_FORM,
      },
    };

    render(
      <DynamicFormPrompt message={phoneMessage} formDisabled={false} onSubmit={mockOnSubmit} />
    );

    expect(screen.getByTestId('phone-input')).toBeInTheDocument();
  });

  it('handles custom date range correctly', () => {
    const customDateMessage: ChatMessage = {
      sender: SenderType.BOT,
      nodeType: RendererType.FORM,
      data: {
        prompt: [
          {
            fieldName: 'customDate',
            fieldType: FormFieldType.CUSTOM_DATE,
            label: 'Custom Date',
            required: true,
            rangeStart: '2023-01-01',
            rangeEnd: '2023-12-31',
          },
        ],
        formType: FormType.MULTI_ASK_FORM,
      },
    };

    render(
      <DynamicFormPrompt message={customDateMessage} formDisabled={false} onSubmit={mockOnSubmit} />
    );

    expect(screen.getByTestId('date-picker')).toBeInTheDocument();
  });

  it('applies default values correctly', () => {
    const defaultValues = {
      name: 'John Doe',
    };

    render(
      <DynamicFormPrompt
        message={defaultMessage}
        formDisabled={false}
        onSubmit={mockOnSubmit}
        defaultValues={defaultValues}
      />
    );

    expect(screen.getByTestId('text-input')).toHaveValue('John Doe');
  });

  it('handles multiple field types in the same form', async () => {
    const multiFieldMessage: ChatMessage = {
      sender: SenderType.BOT,
      nodeType: RendererType.FORM,
      data: {
        prompt: [
          {
            fieldName: 'name',
            fieldType: FormFieldType.TEXT,
            label: 'Name',
            required: true,
          },
          {
            fieldName: 'phone',
            fieldType: FormFieldType.MOBILE_NUMBER,
            label: 'Phone',
            required: true,
          },
          {
            fieldName: 'date',
            fieldType: FormFieldType.DATE,
            label: 'Date',
            required: true,
          },
        ],
        formType: FormType.MULTI_ASK_FORM,
      },
    };

    render(
      <DynamicFormPrompt message={multiFieldMessage} formDisabled={false} onSubmit={mockOnSubmit} />
    );

    await userEvent.type(screen.getByTestId('text-input'), 'John Doe');
    // Trigger a change event to update the phone number
    fireEvent.change(screen.getByTestId('phone-input'), { target: { value: '+12025550123' } });
    fireEvent.change(screen.getByTestId('date-picker'), {
      target: { value: '2023-09-19' },
    });

    await userEvent.click(screen.getByRole('button', { name: 'common.submit' }));

    await waitFor(() => {
      expect(mockOnSubmit).toHaveBeenCalledWith({
        name: 'John Doe',
        phone: '+12025550123',
        date: new Date('2023-09-19'),
      });
    });
  });

  it('handles form state updates correctly', async () => {
    render(
      <DynamicFormPrompt message={defaultMessage} formDisabled={false} onSubmit={mockOnSubmit} />
    );

    const input = screen.getByTestId('text-input');
    const submitButton = screen.getByRole('button', { name: 'common.submit' });

    // Initially the form should be invalid
    expect(submitButton).toBeDisabled();

    // After entering valid data
    await userEvent.type(input, 'John Doe');
    expect(submitButton).not.toBeDisabled();

    // After clearing the input
    await userEvent.clear(input);
    expect(submitButton).toBeDisabled();
  });
});
