import React from 'react';
import { render, screen, fireEvent } from '@testing-library/react';
import { describe, it, expect, vi } from 'vitest';
import PreviewHeader from '../previewHeader';
import { PlatformType } from '../types/enums';

// Mock i18next
vi.mock('react-i18next', () => ({
  useTranslation: () => ({
    t: (key: string) => key,
  }),
}));

describe('PreviewHeader', () => {
  const defaultProps = {
    platform: PlatformType.Web,
    setPlatform: vi.fn(),
    onDebuggerToggle: vi.fn(),
    onClose: vi.fn(),
  };

  it('renders correctly with default props', () => {
    render(<PreviewHeader {...defaultProps} />);
    expect(screen.getByText('common.preview')).toBeInTheDocument();
    const combobox = screen.getByRole('combobox');
    expect(combobox.textContent).toContain(PlatformType.Web);
  });

  it('renders all platform options in select', async () => {
    render(<PreviewHeader {...defaultProps} />);
    const selectButton = screen.getByRole('combobox');
    fireEvent.click(selectButton);

    Object.values(PlatformType).forEach(platform => {
      expect(screen.getByRole('option', { name: platform })).toBeInTheDocument();
    });
  });

  it('calls setPlatform when a new platform is selected', () => {
    render(<PreviewHeader {...defaultProps} />);
    const selectButton = screen.getByRole('combobox');
    fireEvent.click(selectButton);

    const mobileOption = screen.getByRole('option', { name: PlatformType.Mobile });
    fireEvent.click(mobileOption);

    expect(defaultProps.setPlatform).toHaveBeenCalledWith(PlatformType.Mobile);
  });

  it('calls onDebuggerToggle when debug button is clicked', () => {
    render(<PreviewHeader {...defaultProps} />);
    const debugButton = screen.getByTestId('debug-button');
    fireEvent.click(debugButton);
    expect(defaultProps.onDebuggerToggle).toHaveBeenCalledTimes(1);
  });

  it('calls onClose when close button is clicked', () => {
    render(<PreviewHeader {...defaultProps} />);
    const closeButton = screen.getByTestId('close-button');
    fireEvent.click(closeButton);
    expect(defaultProps.onClose).toHaveBeenCalledTimes(1);
  });

  it('renders external link button', () => {
    render(<PreviewHeader {...defaultProps} />);
    const externalLinkButton = screen.getByTestId('external-link-button');
    expect(externalLinkButton).toBeInTheDocument();
  });

  it('maintains button styles and classes', () => {
    render(<PreviewHeader {...defaultProps} />);
    const buttons = screen.getAllByRole('button');
    buttons.forEach(button => {
      expect(button).toHaveClass('!px-1');
      expect(button).toHaveClass('bg-transparent');
      expect(button).toHaveClass('text-tertiary-500');
    });
  });

  it('applies correct layout classes to container', () => {
    const { container } = render(<PreviewHeader {...defaultProps} />);
    const headerDiv = container.firstChild;
    expect(headerDiv).toHaveClass('p-4', 'border-b', 'border-secondary-200');
  });
});
