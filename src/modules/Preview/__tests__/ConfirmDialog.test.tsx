/// <reference types="vitest/globals" />
import { describe, it, expect, vi, beforeEach, afterEach } from 'vitest';
import { render, screen, fireEvent } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { useTranslation } from 'react-i18next';
import ConfirmDialog from '../ConfirmDialog';

// Mock dependencies
vi.mock('react-i18next', () => ({
  useTranslation: vi.fn(),
}));

// Mock UI components
vi.mock('@/components/ui/dialog', () => ({
  Dialog: ({ children, open, onOpenChange }: any) => (
    <div data-testid="dialog" data-open={open} onClick={() => onOpenChange?.(false)}>
      {open && children}
    </div>
  ),
  DialogContent: ({ children, className, container }: any) => (
    <div
      data-testid="dialog-content"
      className={className}
      data-container={container ? 'custom' : 'default'}
    >
      {children}
    </div>
  ),
  DialogHeader: ({ children }: any) => <div data-testid="dialog-header">{children}</div>,
  DialogTitle: ({ children }: any) => <h2 data-testid="dialog-title">{children}</h2>,
  DialogDescription: ({ children }: any) => <p data-testid="dialog-description">{children}</p>,
  DialogFooter: ({ children }: any) => <div data-testid="dialog-footer">{children}</div>,
}));

vi.mock('@/components/ui/button', () => ({
  Button: ({ children, onClick, variantColor, className, ...props }: any) => (
    <button
      data-testid={variantColor === 'success' ? 'yes-button' : 'no-button'}
      onClick={onClick}
      className={className}
      data-variant={variantColor}
      {...props}
    >
      {children}
    </button>
  ),
}));

describe('ConfirmDialog', () => {
  const mockT = vi.fn((key: string) => key);
  const mockOnOpenChange = vi.fn();
  const mockOnConfirm = vi.fn();

  const defaultProps = {
    open: false,
    onOpenChange: mockOnOpenChange,
    onConfirm: mockOnConfirm,
  };

  beforeEach(() => {
    vi.mocked(useTranslation).mockReturnValue({
      t: mockT as any,
      i18n: {} as any,
      ready: true,
    } as any);
  });

  afterEach(() => {
    vi.clearAllMocks();
  });

  describe('Rendering', () => {
    it('renders dialog structure correctly when closed', () => {
      render(<ConfirmDialog {...defaultProps} open={false} />);

      const dialog = screen.getByTestId('dialog');
      expect(dialog).toBeInTheDocument();
      expect(dialog).toHaveAttribute('data-open', 'false');

      // Content should not be rendered when closed
      expect(screen.queryByTestId('dialog-content')).not.toBeInTheDocument();
    });

    it('renders dialog structure correctly when open', () => {
      render(<ConfirmDialog {...defaultProps} open={true} />);

      const dialog = screen.getByTestId('dialog');
      expect(dialog).toBeInTheDocument();
      expect(dialog).toHaveAttribute('data-open', 'true');

      // All dialog elements should be present when open
      expect(screen.getByTestId('dialog-content')).toBeInTheDocument();
      expect(screen.getByTestId('dialog-header')).toBeInTheDocument();
      expect(screen.getByTestId('dialog-title')).toBeInTheDocument();
      expect(screen.getByTestId('dialog-description')).toBeInTheDocument();
      expect(screen.getByTestId('dialog-footer')).toBeInTheDocument();
    });

    it('renders dialog content with correct className', () => {
      render(<ConfirmDialog {...defaultProps} open={true} />);

      const dialogContent = screen.getByTestId('dialog-content');
      expect(dialogContent).toHaveClass('sm:max-w-80');
    });

    it('renders buttons with correct structure', () => {
      render(<ConfirmDialog {...defaultProps} open={true} />);

      const yesButton = screen.getByTestId('yes-button');
      const noButton = screen.getByTestId('no-button');

      expect(yesButton).toBeInTheDocument();
      expect(noButton).toBeInTheDocument();

      expect(yesButton).toHaveAttribute('data-variant', 'success');
      expect(noButton).toHaveAttribute('data-variant', 'error');

      expect(yesButton).toHaveClass('border-none', 'rounded-full', 'px-6', 'py-2');
      expect(noButton).toHaveClass('border-none', 'rounded-full', 'px-6', 'py-2');
    });
  });

  describe('Translation Integration', () => {
    it('uses correct translation keys', () => {
      render(<ConfirmDialog {...defaultProps} open={true} />);

      expect(mockT).toHaveBeenCalledWith('preview.confirmDialog');
      expect(mockT).toHaveBeenCalledWith('preview.confirmDialogDesc');
      expect(mockT).toHaveBeenCalledWith('common.yes');
      expect(mockT).toHaveBeenCalledWith('common.no');
    });

    it('displays translated content correctly', () => {
      mockT.mockImplementation((key: string) => {
        const translations: Record<string, string> = {
          'preview.confirmDialog': 'Confirm Action',
          'preview.confirmDialogDesc': 'Are you sure you want to proceed?',
          'common.yes': 'Yes',
          'common.no': 'No',
        };
        return translations[key] || key;
      });

      render(<ConfirmDialog {...defaultProps} open={true} />);

      expect(screen.getByText('Confirm Action')).toBeInTheDocument();
      expect(screen.getByText('Are you sure you want to proceed?')).toBeInTheDocument();
      expect(screen.getByText('Yes')).toBeInTheDocument();
      expect(screen.getByText('No')).toBeInTheDocument();
    });

    it('calls useTranslation hook', () => {
      render(<ConfirmDialog {...defaultProps} />);

      expect(useTranslation).toHaveBeenCalled();
    });
  });

  describe('Event Handling', () => {
    it('calls onConfirm with true when Yes button is clicked', () => {
      render(<ConfirmDialog {...defaultProps} open={true} />);

      const yesButton = screen.getByTestId('yes-button');
      fireEvent.click(yesButton);

      expect(mockOnConfirm).toHaveBeenCalledTimes(1);
      expect(mockOnConfirm).toHaveBeenCalledWith(true);
    });

    it('calls onConfirm with false when No button is clicked', () => {
      render(<ConfirmDialog {...defaultProps} open={true} />);

      const noButton = screen.getByTestId('no-button');
      fireEvent.click(noButton);

      expect(mockOnConfirm).toHaveBeenCalledTimes(1);
      expect(mockOnConfirm).toHaveBeenCalledWith(false);
    });

    it('calls onOpenChange when dialog is clicked (backdrop)', () => {
      render(<ConfirmDialog {...defaultProps} open={true} />);

      const dialog = screen.getByTestId('dialog');
      fireEvent.click(dialog);

      expect(mockOnOpenChange).toHaveBeenCalledTimes(1);
      expect(mockOnOpenChange).toHaveBeenCalledWith(false);
    });

    it('handles multiple clicks correctly', () => {
      render(<ConfirmDialog {...defaultProps} open={true} />);

      const yesButton = screen.getByTestId('yes-button');
      const noButton = screen.getByTestId('no-button');

      fireEvent.click(yesButton);
      fireEvent.click(noButton);
      fireEvent.click(yesButton);

      expect(mockOnConfirm).toHaveBeenCalledTimes(3);
      expect(mockOnConfirm).toHaveBeenNthCalledWith(1, true);
      expect(mockOnConfirm).toHaveBeenNthCalledWith(2, false);
      expect(mockOnConfirm).toHaveBeenNthCalledWith(3, true);
    });
  });

  describe('User Interactions', () => {
    it('handles user events with userEvent library', async () => {
      const user = userEvent.setup();
      render(<ConfirmDialog {...defaultProps} open={true} />);

      const yesButton = screen.getByTestId('yes-button');
      await user.click(yesButton);

      expect(mockOnConfirm).toHaveBeenCalledWith(true);
    });

    it('handles keyboard interactions on buttons', async () => {
      const user = userEvent.setup();
      render(<ConfirmDialog {...defaultProps} open={true} />);

      const yesButton = screen.getByTestId('yes-button');
      yesButton.focus();
      await user.keyboard('[Enter]');

      expect(mockOnConfirm).toHaveBeenCalledWith(true);
    });
  });

  describe('Container Prop', () => {
    it('passes container prop to DialogContent when provided', () => {
      const mockContainer = document.createElement('div');
      render(<ConfirmDialog {...defaultProps} open={true} container={mockContainer} />);

      const dialogContent = screen.getByTestId('dialog-content');
      expect(dialogContent).toHaveAttribute('data-container', 'custom');
    });

    it('uses default container when container prop is not provided', () => {
      render(<ConfirmDialog {...defaultProps} open={true} />);

      const dialogContent = screen.getByTestId('dialog-content');
      expect(dialogContent).toHaveAttribute('data-container', 'default');
    });

    it('handles null container prop', () => {
      render(<ConfirmDialog {...defaultProps} open={true} container={null} />);

      const dialogContent = screen.getByTestId('dialog-content');
      expect(dialogContent).toHaveAttribute('data-container', 'default');
    });
  });

  describe('Props Behavior', () => {
    it('responds to open prop changes', () => {
      const { rerender } = render(<ConfirmDialog {...defaultProps} open={false} />);

      expect(screen.getByTestId('dialog')).toHaveAttribute('data-open', 'false');
      expect(screen.queryByTestId('dialog-content')).not.toBeInTheDocument();

      rerender(<ConfirmDialog {...defaultProps} open={true} />);

      expect(screen.getByTestId('dialog')).toHaveAttribute('data-open', 'true');
      expect(screen.getByTestId('dialog-content')).toBeInTheDocument();
    });

    it('passes onOpenChange prop correctly to Dialog', () => {
      render(<ConfirmDialog {...defaultProps} open={true} />);

      const dialog = screen.getByTestId('dialog');
      fireEvent.click(dialog);

      expect(mockOnOpenChange).toHaveBeenCalledWith(false);
    });

    it('works with different onConfirm callbacks', () => {
      const customOnConfirm = vi.fn();
      render(<ConfirmDialog {...defaultProps} open={true} onConfirm={customOnConfirm} />);

      const yesButton = screen.getByTestId('yes-button');
      fireEvent.click(yesButton);

      expect(customOnConfirm).toHaveBeenCalledWith(true);
      expect(mockOnConfirm).not.toHaveBeenCalled();
    });
  });

  describe('Accessibility', () => {
    it('has proper dialog structure for screen readers', () => {
      render(<ConfirmDialog {...defaultProps} open={true} />);

      const title = screen.getByTestId('dialog-title');
      const description = screen.getByTestId('dialog-description');

      expect(title.tagName.toLowerCase()).toBe('h2');
      expect(description.tagName.toLowerCase()).toBe('p');
    });

    it('has focusable buttons', () => {
      render(<ConfirmDialog {...defaultProps} open={true} />);

      const yesButton = screen.getByTestId('yes-button');
      const noButton = screen.getByTestId('no-button');

      expect(yesButton.tagName.toLowerCase()).toBe('button');
      expect(noButton.tagName.toLowerCase()).toBe('button');
    });
  });

  describe('Component Structure', () => {
    it('maintains correct component hierarchy', () => {
      render(<ConfirmDialog {...defaultProps} open={true} />);

      const dialog = screen.getByTestId('dialog');
      const content = screen.getByTestId('dialog-content');
      const header = screen.getByTestId('dialog-header');
      const footer = screen.getByTestId('dialog-footer');

      expect(dialog).toContainElement(content);
      expect(content).toContainElement(header);
      expect(content).toContainElement(footer);
      expect(header).toContainElement(screen.getByTestId('dialog-title'));
      expect(header).toContainElement(screen.getByTestId('dialog-description'));
      expect(footer).toContainElement(screen.getByTestId('yes-button'));
      expect(footer).toContainElement(screen.getByTestId('no-button'));
    });

    it('renders all required elements when open', () => {
      render(<ConfirmDialog {...defaultProps} open={true} />);

      // Check all elements are present
      expect(screen.getByTestId('dialog')).toBeInTheDocument();
      expect(screen.getByTestId('dialog-content')).toBeInTheDocument();
      expect(screen.getByTestId('dialog-header')).toBeInTheDocument();
      expect(screen.getByTestId('dialog-title')).toBeInTheDocument();
      expect(screen.getByTestId('dialog-description')).toBeInTheDocument();
      expect(screen.getByTestId('dialog-footer')).toBeInTheDocument();
      expect(screen.getByTestId('yes-button')).toBeInTheDocument();
      expect(screen.getByTestId('no-button')).toBeInTheDocument();
    });
  });

  describe('Edge Cases', () => {
    it('handles missing translation function gracefully', () => {
      vi.mocked(useTranslation).mockReturnValue({
        t: vi.fn((key: string) => key) as any, // Provide fallback function
        i18n: {} as any,
        ready: false,
      } as any);

      expect(() => {
        render(<ConfirmDialog {...defaultProps} open={true} />);
      }).not.toThrow();
    });

    it('handles rapid open/close state changes', () => {
      const { rerender } = render(<ConfirmDialog {...defaultProps} open={false} />);

      // Rapidly toggle open state
      rerender(<ConfirmDialog {...defaultProps} open={true} />);
      rerender(<ConfirmDialog {...defaultProps} open={false} />);
      rerender(<ConfirmDialog {...defaultProps} open={true} />);

      expect(screen.getByTestId('dialog')).toHaveAttribute('data-open', 'true');
      expect(screen.getByTestId('dialog-content')).toBeInTheDocument();
    });

    it('maintains state when props change but dialog remains open', () => {
      const { rerender } = render(<ConfirmDialog {...defaultProps} open={true} />);

      const newOnConfirm = vi.fn();
      rerender(<ConfirmDialog {...defaultProps} open={true} onConfirm={newOnConfirm} />);

      const yesButton = screen.getByTestId('yes-button');
      fireEvent.click(yesButton);

      expect(newOnConfirm).toHaveBeenCalledWith(true);
    });
  });

  describe('Button Styling', () => {
    it('applies correct CSS classes to buttons', () => {
      render(<ConfirmDialog {...defaultProps} open={true} />);

      const yesButton = screen.getByTestId('yes-button');
      const noButton = screen.getByTestId('no-button');

      const expectedClasses = ['border-none', 'rounded-full', 'px-6', 'py-2'];

      expectedClasses.forEach(className => {
        expect(yesButton).toHaveClass(className);
        expect(noButton).toHaveClass(className);
      });
    });

    it('applies correct variant colors to buttons', () => {
      render(<ConfirmDialog {...defaultProps} open={true} />);

      const yesButton = screen.getByTestId('yes-button');
      const noButton = screen.getByTestId('no-button');

      expect(yesButton).toHaveAttribute('data-variant', 'success');
      expect(noButton).toHaveAttribute('data-variant', 'error');
    });
  });
});
