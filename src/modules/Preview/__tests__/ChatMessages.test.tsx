/// <reference types="vitest/globals" />
import { describe, it, expect, vi, beforeEach, afterEach } from 'vitest';
import { render, screen } from '@testing-library/react';
import { useTranslation } from 'react-i18next';
import ChatMessages from '../ChatMessages';
import { ChatMessage, BotFormFields } from '../types/types';
import { RendererType, SenderType, FormFieldType, FormType } from '../types/enums';
import { getRenderer } from '../utils/rendererRegistry';

// Mock dependencies
vi.mock('react-i18next', () => ({
  useTranslation: vi.fn(),
}));

vi.mock('../utils/rendererRegistry', () => ({
  getRenderer: vi.fn(),
}));

// Mock renderer components
const MockMessageRenderer = vi.fn(({ msg, idx }) => (
  <div data-testid={`message-renderer-${idx}`}>
    Message: {msg.data?.text || msg.data?.content || ''}
  </div>
));

const MockFormRenderer = vi.fn(({ msg, idx }) => (
  <div data-testid={`form-renderer-${idx}`}>Form: {msg.data?.prompt?.[0]?.fieldName || ''}</div>
));

const MockFeedbackRenderer = vi.fn(({ msg, idx }) => (
  <div data-testid={`feedback-renderer-${idx}`}>Feedback: {msg.data?.text || ''}</div>
));

describe('ChatMessages', () => {
  const mockT = vi.fn((key: string) => key);
  const mockOnFormSubmit = vi.fn();
  const mockOnFormActiveChange = vi.fn();

  const defaultProps = {
    messages: [],
    lastFormPrompt: null,
    formLocked: false,
    onFormSubmit: mockOnFormSubmit,
    lastFormFieldValues: undefined,
    loading: false,
    onFormActiveChange: mockOnFormActiveChange,
  };

  // Sample messages for testing
  const sampleUserMessage: ChatMessage = {
    sender: SenderType.USER,
    nodeType: RendererType.USER_MESSAGE,
    data: { text: 'Hello from user' },
  };

  const sampleBotMessage: ChatMessage = {
    sender: SenderType.BOT,
    nodeType: RendererType.MESSAGE,
    data: { text: 'Hello from bot', content: 'Bot response' },
  };

  const sampleFormMessage: ChatMessage = {
    sender: SenderType.BOT,
    nodeType: RendererType.FORM,
    data: {
      prompt: [
        {
          fieldName: 'email',
          fieldType: FormFieldType.EMAIL,
          label: 'Email Address',
          required: true,
        },
      ],
      formType: FormType.MULTI_ASK_FORM,
    },
  };

  const sampleSubmittedFormMessage: ChatMessage = {
    sender: SenderType.BOT,
    nodeType: RendererType.FORM,
    data: {
      prompt: [
        {
          fieldName: 'name',
          fieldType: FormFieldType.TEXT,
          label: 'Name',
          required: true,
        },
      ],
      formType: FormType.MULTI_ASK_FORM,
      submittedValues: { name: 'John Doe' },
    },
  };

  const sampleTextFieldFormMessage: ChatMessage = {
    sender: SenderType.BOT,
    nodeType: RendererType.FORM,
    data: {
      prompt: [
        {
          fieldName: 'message',
          fieldType: 'text' as any,
          label: 'Message',
          required: false,
        },
      ],
      formType: FormType.MULTI_ASK_FORM,
    },
  };

  const sampleFeedbackMessage: ChatMessage = {
    sender: SenderType.BOT,
    nodeType: RendererType.FEEDBACK,
    data: { text: 'Thank you for your feedback', type: 'positive' },
  };

  beforeEach(() => {
    vi.mocked(useTranslation).mockReturnValue({
      t: mockT as any,
      i18n: {} as any,
      ready: true,
    } as any);

    // Setup default renderer mocks
    vi.mocked(getRenderer).mockImplementation((nodeType: RendererType) => {
      switch (nodeType) {
        case RendererType.USER_MESSAGE:
        case RendererType.MESSAGE:
          return MockMessageRenderer as any;
        case RendererType.FORM:
          return MockFormRenderer as any;
        case RendererType.FEEDBACK:
          return MockFeedbackRenderer as any;
        default:
          return null;
      }
    });
  });

  afterEach(() => {
    vi.clearAllMocks();
  });

  describe('Rendering', () => {
    it('renders empty chat container correctly', () => {
      render(<ChatMessages {...defaultProps} />);

      const container = document.querySelector('.flex-1.border-none.p-4.space-y-3.overflow-y-auto');
      expect(container).toBeInTheDocument();
      expect(container).toHaveClass('flex-1', 'border-none', 'p-4', 'space-y-3', 'overflow-y-auto');
    });

    it('renders multiple messages with correct renderers', () => {
      const messages = [sampleUserMessage, sampleBotMessage, sampleFormMessage];
      render(<ChatMessages {...defaultProps} messages={messages} />);

      expect(screen.getByTestId('message-renderer-0')).toBeInTheDocument();
      expect(screen.getByTestId('message-renderer-1')).toBeInTheDocument();
      expect(screen.getByTestId('form-renderer-2')).toBeInTheDocument();

      expect(screen.getByText('Message: Hello from user')).toBeInTheDocument();
      expect(screen.getByText('Message: Hello from bot')).toBeInTheDocument();
      expect(screen.getByText('Form: email')).toBeInTheDocument();
    });

    it('renders feedback message correctly', () => {
      const messages = [sampleFeedbackMessage];
      render(<ChatMessages {...defaultProps} messages={messages} />);

      expect(screen.getByTestId('feedback-renderer-0')).toBeInTheDocument();
      expect(screen.getByText('Feedback: Thank you for your feedback')).toBeInTheDocument();
    });

    it('handles unknown renderer types gracefully', () => {
      vi.mocked(getRenderer).mockReturnValue(null);
      const messages = [sampleUserMessage];

      render(<ChatMessages {...defaultProps} messages={messages} />);

      // Should not render anything for unknown renderer
      expect(screen.queryByTestId('message-renderer-0')).not.toBeInTheDocument();
    });

    it('renders chat end reference element', () => {
      render(<ChatMessages {...defaultProps} />);

      // The ref element should be present (last div)
      const container = document.querySelector('.flex-1.border-none.p-4.space-y-3.overflow-y-auto');
      const lastChild = container?.lastChild;
      expect(lastChild).toBeInTheDocument();
    });
  });

  describe('Loading State', () => {
    it('shows typing indicator when loading is true', () => {
      render(<ChatMessages {...defaultProps} loading={true} />);

      const typingIndicator = screen.getByText('form.typing');
      expect(typingIndicator).toBeInTheDocument();
      expect(typingIndicator).toHaveClass(
        'px-4',
        'py-2',
        'rounded-2xl',
        'bg-tertiary-100',
        'text-secondary-400',
        'text-base',
        'animate-pulse'
      );

      expect(mockT).toHaveBeenCalledWith('form.typing');
    });

    it('does not show typing indicator when loading is false', () => {
      render(<ChatMessages {...defaultProps} loading={false} />);

      expect(screen.queryByText('form.typing')).not.toBeInTheDocument();
    });

    it('shows typing indicator with correct container styles', () => {
      render(<ChatMessages {...defaultProps} loading={true} />);

      const typingContainer = screen.getByText('form.typing').parentElement;
      expect(typingContainer).toHaveClass('flex', 'justify-start');
    });
  });

  describe('Renderer Component Props', () => {
    it('passes correct props to renderer components', () => {
      const messages = [sampleBotMessage];
      const lastFormPrompt = sampleFormMessage;
      const lastFormFieldValues = { email: '<EMAIL>' };

      render(
        <ChatMessages
          {...defaultProps}
          messages={messages}
          lastFormPrompt={lastFormPrompt}
          formLocked={true}
          lastFormFieldValues={lastFormFieldValues}
        />
      );

      expect(MockMessageRenderer).toHaveBeenCalledWith(
        expect.objectContaining({
          msg: sampleBotMessage,
          idx: 0,
          lastFormPrompt: lastFormPrompt,
          formLocked: true,
          onFormSubmit: mockOnFormSubmit,
          lastFormFieldValues: lastFormFieldValues,
        }),
        {}
      );
    });

    it('passes correct message index to each renderer', () => {
      const messages = [sampleUserMessage, sampleBotMessage, sampleFormMessage];
      render(<ChatMessages {...defaultProps} messages={messages} />);

      expect(MockMessageRenderer).toHaveBeenNthCalledWith(
        1,
        expect.objectContaining({ idx: 0 }),
        {}
      );
      expect(MockMessageRenderer).toHaveBeenNthCalledWith(
        2,
        expect.objectContaining({ idx: 1 }),
        {}
      );
      expect(MockFormRenderer).toHaveBeenCalledWith(expect.objectContaining({ idx: 2 }), {});
    });
  });

  describe('Active Form Detection', () => {
    it('detects active form correctly', () => {
      const messages = [sampleFormMessage]; // Unsubmitted form
      render(<ChatMessages {...defaultProps} messages={messages} />);

      expect(mockOnFormActiveChange).toHaveBeenCalledWith(true);
    });

    it('does not detect active form for submitted forms', () => {
      const messages = [sampleSubmittedFormMessage]; // Form with submitted values
      render(<ChatMessages {...defaultProps} messages={messages} />);

      expect(mockOnFormActiveChange).toHaveBeenCalledWith(false);
    });

    it('does not detect active form for text field forms', () => {
      const messages = [sampleTextFieldFormMessage]; // Text field form
      render(<ChatMessages {...defaultProps} messages={messages} />);

      expect(mockOnFormActiveChange).toHaveBeenCalledWith(false);
    });

    it('does not detect active form for non-form messages', () => {
      const messages = [sampleUserMessage, sampleBotMessage];
      render(<ChatMessages {...defaultProps} messages={messages} />);

      expect(mockOnFormActiveChange).toHaveBeenCalledWith(false);
    });

    it('detects active form when multiple messages include one active form', () => {
      const messages = [
        sampleUserMessage,
        sampleSubmittedFormMessage, // Submitted form
        sampleFormMessage, // Active form
        sampleBotMessage,
      ];
      render(<ChatMessages {...defaultProps} messages={messages} />);

      expect(mockOnFormActiveChange).toHaveBeenCalledWith(true);
    });

    it('handles forms without prompt fields', () => {
      const emptyFormMessage: ChatMessage = {
        sender: SenderType.BOT,
        nodeType: RendererType.FORM,
        data: { prompt: [], formType: FormType.MULTI_ASK_FORM },
      };
      const messages = [emptyFormMessage];
      render(<ChatMessages {...defaultProps} messages={messages} />);

      expect(mockOnFormActiveChange).toHaveBeenCalledWith(false);
    });

    it('handles forms without data', () => {
      const invalidFormMessage: ChatMessage = {
        sender: SenderType.BOT,
        nodeType: RendererType.FORM,
        data: undefined as any,
      };
      const messages = [invalidFormMessage];
      render(<ChatMessages {...defaultProps} messages={messages} />);

      expect(mockOnFormActiveChange).toHaveBeenCalledWith(false);
    });

    it('calls onFormActiveChange when active form state changes', () => {
      const { rerender } = render(
        <ChatMessages {...defaultProps} messages={[sampleFormMessage]} />
      );

      expect(mockOnFormActiveChange).toHaveBeenCalledWith(true);

      mockOnFormActiveChange.mockClear();

      // Rerender with submitted form
      rerender(<ChatMessages {...defaultProps} messages={[sampleSubmittedFormMessage]} />);

      expect(mockOnFormActiveChange).toHaveBeenCalledWith(false);
    });

    it('does not call onFormActiveChange when callback is not provided', () => {
      render(
        <ChatMessages
          {...defaultProps}
          messages={[sampleFormMessage]}
          onFormActiveChange={undefined}
        />
      );

      // Should not throw error when onFormActiveChange is undefined
      expect(mockOnFormActiveChange).not.toHaveBeenCalled();
    });
  });

  describe('Message Handling Edge Cases', () => {
    it('handles empty messages array', () => {
      render(<ChatMessages {...defaultProps} messages={[]} />);

      expect(screen.queryByTestId(/renderer/)).not.toBeInTheDocument();
      expect(mockOnFormActiveChange).toHaveBeenCalledWith(false);
    });

    it('handles messages with missing data', () => {
      const invalidMessage: ChatMessage = {
        sender: SenderType.BOT,
        nodeType: RendererType.MESSAGE,
        data: undefined as any,
      };
      render(<ChatMessages {...defaultProps} messages={[invalidMessage]} />);

      expect(screen.getByTestId('message-renderer-0')).toBeInTheDocument();
      expect(screen.getByText('Message:')).toBeInTheDocument(); // Empty text
    });

    it('handles mixed message types correctly', () => {
      const messages = [
        sampleUserMessage,
        sampleBotMessage,
        sampleFormMessage,
        sampleFeedbackMessage,
      ];
      render(<ChatMessages {...defaultProps} messages={messages} />);

      expect(screen.getByTestId('message-renderer-0')).toBeInTheDocument();
      expect(screen.getByTestId('message-renderer-1')).toBeInTheDocument();
      expect(screen.getByTestId('form-renderer-2')).toBeInTheDocument();
      expect(screen.getByTestId('feedback-renderer-3')).toBeInTheDocument();
    });
  });

  describe('Props Variation', () => {
    it('handles optional props correctly', () => {
      const minimalProps = {
        messages: [sampleBotMessage],
        lastFormPrompt: null,
        formLocked: false,
        onFormSubmit: mockOnFormSubmit,
      };

      render(<ChatMessages {...minimalProps} />);

      expect(MockMessageRenderer).toHaveBeenCalledWith(
        expect.objectContaining({
          lastFormFieldValues: undefined,
        }),
        {}
      );
      expect(screen.queryByText('form.typing')).not.toBeInTheDocument();
    });

    it('passes all props when provided', () => {
      const fullProps = {
        messages: [sampleFormMessage],
        lastFormPrompt: sampleFormMessage,
        formLocked: true,
        onFormSubmit: mockOnFormSubmit,
        lastFormFieldValues: { email: '<EMAIL>' },
        loading: true,
        onFormActiveChange: mockOnFormActiveChange,
      };

      render(<ChatMessages {...fullProps} />);

      expect(MockFormRenderer).toHaveBeenCalledWith(
        expect.objectContaining({
          msg: sampleFormMessage,
          lastFormPrompt: sampleFormMessage,
          formLocked: true,
          onFormSubmit: mockOnFormSubmit,
          lastFormFieldValues: { email: '<EMAIL>' },
        }),
        {}
      );
      expect(screen.getByText('form.typing')).toBeInTheDocument();
    });
  });

  describe('Component Behavior', () => {
    it('calls getRenderer for each message', () => {
      const messages = [sampleUserMessage, sampleFormMessage, sampleFeedbackMessage];
      render(<ChatMessages {...defaultProps} messages={messages} />);

      expect(getRenderer).toHaveBeenCalledTimes(3);
      expect(getRenderer).toHaveBeenCalledWith(RendererType.USER_MESSAGE);
      expect(getRenderer).toHaveBeenCalledWith(RendererType.FORM);
      expect(getRenderer).toHaveBeenCalledWith(RendererType.FEEDBACK);
    });

    it('generates correct keys for message components', () => {
      const messages = [sampleUserMessage, sampleBotMessage];
      render(<ChatMessages {...defaultProps} messages={messages} />);

      // Keys are used internally, but we can verify components are rendered with correct idx
      expect(MockMessageRenderer).toHaveBeenNthCalledWith(
        1,
        expect.objectContaining({ idx: 0 }),
        {}
      );
      expect(MockMessageRenderer).toHaveBeenNthCalledWith(
        2,
        expect.objectContaining({ idx: 1 }),
        {}
      );
    });
  });

  describe('Memoization', () => {
    it('component is memoized', () => {
      const { rerender } = render(<ChatMessages {...defaultProps} />);

      // Re-render with same props should not cause re-creation
      rerender(<ChatMessages {...defaultProps} />);

      // Component should still be rendered correctly
      const container = document.querySelector('.flex-1.border-none.p-4.space-y-3.overflow-y-auto');
      expect(container).toBeInTheDocument();
    });

    it('re-renders when messages change', () => {
      const { rerender } = render(
        <ChatMessages {...defaultProps} messages={[sampleUserMessage]} />
      );

      expect(screen.getByTestId('message-renderer-0')).toBeInTheDocument();

      mockOnFormActiveChange.mockClear();

      rerender(
        <ChatMessages {...defaultProps} messages={[sampleUserMessage, sampleFormMessage]} />
      );

      expect(screen.getByTestId('message-renderer-0')).toBeInTheDocument();
      expect(screen.getByTestId('form-renderer-1')).toBeInTheDocument();
      expect(mockOnFormActiveChange).toHaveBeenCalledWith(true);
    });
  });

  describe('Translation Integration', () => {
    it('uses translation hook correctly', () => {
      render(<ChatMessages {...defaultProps} loading={true} />);

      expect(useTranslation).toHaveBeenCalled();
      expect(mockT).toHaveBeenCalledWith('form.typing');
    });
  });
});
