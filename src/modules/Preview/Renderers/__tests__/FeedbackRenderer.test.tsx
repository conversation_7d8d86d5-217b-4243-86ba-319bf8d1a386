import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import { describe, it, expect, vi } from 'vitest';
import FeedbackRenderer from '../FeedbackRenderer';
import { RendererType, SenderType } from '../../types/enums';
import type { FeedbackMessage } from '../../types/types';

interface FeedbackTypeRendererProps {
  feedbackType: string;
  fieldValue: string;
  onFieldChange: (value: string) => void;
  isLocked: boolean;
}

// Mock FeedbackTypeRenderer since we want to test FeedbackRenderer in isolation
vi.mock('../FeedbackTypeRenderer', () => ({
  default: ({ feedbackType, fieldValue, onFieldChange, isLocked }: FeedbackTypeRendererProps) => (
    <div data-testid="mock-feedback-type">
      <span>Type: {feedbackType}</span>
      <input
        type="text"
        value={fieldValue}
        onChange={e => onFieldChange(e.target.value)}
        disabled={isLocked}
        data-testid="mock-feedback-input"
      />
    </div>
  ),
}));

// Mock i18next
vi.mock('react-i18next', () => ({
  useTranslation: () => ({
    t: (key: string) => (key === 'common.submit' ? 'Submit' : key),
  }),
}));

describe('FeedbackRenderer', () => {
  const defaultMsg: FeedbackMessage = {
    nodeType: RendererType.FEEDBACK,
    sender: SenderType.BOT,
    data: {
      text: 'Please rate your experience:',
      type: 'Star',
    },
  };

  it('renders feedback form with default values', () => {
    render(<FeedbackRenderer msg={defaultMsg} idx={0} />);

    expect(screen.getByText('Please rate your experience:')).toBeInTheDocument();
    expect(screen.getByTestId('mock-feedback-type')).toBeInTheDocument();
    expect(screen.getByText('Type: Star')).toBeInTheDocument();
    expect(screen.getByRole('button', { name: /submit/i })).toBeDisabled();
  });

  it('enables submit button when feedback is provided', async () => {
    render(<FeedbackRenderer msg={defaultMsg} idx={0} />);

    const input = screen.getByTestId('mock-feedback-input');
    fireEvent.change(input, { target: { value: '5' } });

    const submitButton = screen.getByRole('button', { name: /submit/i });
    expect(submitButton).not.toBeDisabled();
  });

  it('calls onFormSubmit with correct data when submitted', async () => {
    const mockOnFormSubmit = vi.fn();
    render(<FeedbackRenderer msg={defaultMsg} idx={0} onFormSubmit={mockOnFormSubmit} />);

    const input = screen.getByTestId('mock-feedback-input');
    fireEvent.change(input, { target: { value: '5' } });

    const submitButton = screen.getByRole('button', { name: /submit/i });
    fireEvent.click(submitButton);

    await waitFor(() => {
      expect(mockOnFormSubmit).toHaveBeenCalledWith({
        feedback: '5',
        feedbackType: 'Star',
      });
    });
  });

  it('disables form when submittedValues are present', () => {
    const msgWithSubmittedValues = {
      ...defaultMsg,
      data: {
        ...defaultMsg.data,
        submittedValues: { feedback: '5' },
      },
    };

    render(<FeedbackRenderer msg={msgWithSubmittedValues} idx={0} />);

    const input = screen.getByTestId('mock-feedback-input');
    expect(input).toBeDisabled();
    expect(screen.getByRole('button', { name: /submit/i })).toBeDisabled();
  });

  it('disables form when formLocked is true and message is not lastFormPrompt', () => {
    const lastFormPrompt = { ...defaultMsg, data: { ...defaultMsg.data } };

    render(
      <FeedbackRenderer
        msg={defaultMsg}
        idx={0}
        formLocked={true}
        lastFormPrompt={lastFormPrompt}
      />
    );

    const input = screen.getByTestId('mock-feedback-input');
    expect(input).toBeDisabled();
    expect(screen.getByRole('button', { name: /submit/i })).toBeDisabled();
  });

  it('shows custom feedback prompt text', () => {
    const msgWithCustomPrompt = {
      ...defaultMsg,
      data: {
        ...defaultMsg.data,
        text: 'Custom feedback prompt',
      },
    };

    render(<FeedbackRenderer msg={msgWithCustomPrompt} idx={0} />);

    expect(screen.getByText('Custom feedback prompt')).toBeInTheDocument();
  });

  it('passes correct feedback type to FeedbackTypeRenderer', () => {
    const msgWithThumbsType: FeedbackMessage = {
      ...defaultMsg,
      data: {
        ...defaultMsg.data,
        type: 'Thumbs',
      },
    };

    render(<FeedbackRenderer msg={msgWithThumbsType} idx={0} />);

    expect(screen.getByTestId('mock-feedback-type')).toHaveTextContent('Type: Thumbs');
  });

  it('uses Star type when type is empty', () => {
    const msgWithEmptyType: FeedbackMessage = {
      ...defaultMsg,
      data: {
        text: defaultMsg.data.text,
        type: '',
      },
    };

    render(<FeedbackRenderer msg={msgWithEmptyType} idx={0} />);

    expect(screen.getByTestId('mock-feedback-type')).toHaveTextContent('Type:');
  });
});
