import React from 'react';
import { render, screen, fireEvent } from '@testing-library/react';
import { describe, it, expect, vi } from 'vitest';
import StarRating from '../StarRating';

// Mock the Star component from lucide-react
vi.mock('lucide-react', () => ({
  Star: ({
    className,
    onClick,
    fill,
  }: {
    className: string;
    onClick?: () => void;
    fill: string;
  }) => (
    <svg
      data-testid="star-icon"
      className={className}
      onClick={onClick}
      fill={fill}
      role="button"
      aria-label="star"
    />
  ),
}));

describe('StarRating', () => {
  const mockOnChange = vi.fn();

  beforeEach(() => {
    mockOnChange.mockClear();
  });

  it('renders 5 stars', () => {
    render(<StarRating fieldValue="" onFieldChange={mockOnChange} isLocked={false} />);

    const stars = screen.getAllByTestId('star-icon');
    expect(stars).toHaveLength(5);
  });

  it('highlights correct number of stars based on fieldValue', () => {
    render(<StarRating fieldValue="3" onFieldChange={mockOnChange} isLocked={false} />);

    const stars = screen.getAllByTestId('star-icon');

    // First 3 stars should be filled
    stars.slice(0, 3).forEach(star => {
      expect(star).toHaveAttribute('fill', 'currentColor');
      expect(star).toHaveClass('text-warning-500');
    });

    // Last 2 stars should be unfilled
    stars.slice(3).forEach(star => {
      expect(star).toHaveAttribute('fill', 'none');
      expect(star).toHaveClass('text-muted-foreground');
    });
  });

  it('handles star clicks when not locked', () => {
    render(<StarRating fieldValue="3" onFieldChange={mockOnChange} isLocked={false} />);

    const stars = screen.getAllByTestId('star-icon');
    fireEvent.click(stars[4]); // Click the 5th star

    expect(mockOnChange).toHaveBeenCalledWith('5');
    expect(stars[4]).toHaveClass('cursor-pointer');
  });

  it('disables star clicks when locked', () => {
    render(<StarRating fieldValue="3" onFieldChange={mockOnChange} isLocked={true} />);

    const stars = screen.getAllByTestId('star-icon');
    fireEvent.click(stars[4]); // Try to click the 5th star

    expect(mockOnChange).not.toHaveBeenCalled();
    expect(stars[4]).toHaveClass('cursor-not-allowed', 'opacity-50');
  });

  it('updates rating when clicking different stars', () => {
    render(<StarRating fieldValue="3" onFieldChange={mockOnChange} isLocked={false} />);

    const stars = screen.getAllByTestId('star-icon');

    // Click each star and verify the callback
    stars.forEach((star, index) => {
      fireEvent.click(star);
      expect(mockOnChange).toHaveBeenLastCalledWith((index + 1).toString());
    });

    expect(mockOnChange).toHaveBeenCalledTimes(5);
  });

  it('handles empty fieldValue correctly', () => {
    render(<StarRating fieldValue="" onFieldChange={mockOnChange} isLocked={false} />);

    const stars = screen.getAllByTestId('star-icon');

    // All stars should be unfilled
    stars.forEach(star => {
      expect(star).toHaveAttribute('fill', 'none');
      expect(star).toHaveClass('text-muted-foreground');
    });
  });
});
