import React from 'react';
import { render, screen, fireEvent } from '@testing-library/react';
import { describe, it, expect, vi } from 'vitest';
import ThumbsFeedback from '../ThumbsFeedback';

// Mock the Lucide icons
vi.mock('lucide-react', () => ({
  ThumbsUp: ({
    className,
    onClick,
    fill,
  }: {
    className: string;
    onClick?: () => void;
    fill: string;
  }) => (
    <svg
      data-testid="thumbs-up-icon"
      className={className}
      onClick={onClick}
      fill={fill}
      role="button"
      aria-label="thumbs up"
    />
  ),
  ThumbsDown: ({
    className,
    onClick,
    fill,
  }: {
    className: string;
    onClick?: () => void;
    fill: string;
  }) => (
    <svg
      data-testid="thumbs-down-icon"
      className={className}
      onClick={onClick}
      fill={fill}
      role="button"
      aria-label="thumbs down"
    />
  ),
}));

describe('ThumbsFeedback', () => {
  const mockOnChange = vi.fn();

  beforeEach(() => {
    mockOnChange.mockClear();
  });

  it('renders both thumbs up and down buttons', () => {
    render(<ThumbsFeedback fieldValue="" onFieldChange={mockOnChange} isLocked={false} />);

    expect(screen.getByTestId('thumbs-up-icon')).toBeInTheDocument();
    expect(screen.getByTestId('thumbs-down-icon')).toBeInTheDocument();
  });

  it('highlights thumbs up when fieldValue is "1"', () => {
    render(<ThumbsFeedback fieldValue="1" onFieldChange={mockOnChange} isLocked={false} />);

    const thumbsUp = screen.getByTestId('thumbs-up-icon');
    const thumbsDown = screen.getByTestId('thumbs-down-icon');

    expect(thumbsUp).toHaveClass('text-success-500');
    expect(thumbsUp).toHaveAttribute('fill', 'currentColor');
    expect(thumbsDown).toHaveClass('text-muted-foreground');
    expect(thumbsDown).toHaveAttribute('fill', 'none');
  });

  it('highlights thumbs down when fieldValue is "0"', () => {
    render(<ThumbsFeedback fieldValue="0" onFieldChange={mockOnChange} isLocked={false} />);

    const thumbsUp = screen.getByTestId('thumbs-up-icon');
    const thumbsDown = screen.getByTestId('thumbs-down-icon');

    expect(thumbsDown).toHaveClass('text-error-500');
    expect(thumbsDown).toHaveAttribute('fill', 'currentColor');
    expect(thumbsUp).toHaveClass('text-muted-foreground');
    expect(thumbsUp).toHaveAttribute('fill', 'none');
  });

  it('handles thumbs up click when not locked', () => {
    render(<ThumbsFeedback fieldValue="" onFieldChange={mockOnChange} isLocked={false} />);

    const thumbsUp = screen.getByTestId('thumbs-up-icon');
    fireEvent.click(thumbsUp);

    expect(mockOnChange).toHaveBeenCalledWith('1');
    expect(thumbsUp).toHaveClass('cursor-pointer');
    expect(thumbsUp).not.toHaveClass('cursor-not-allowed', 'opacity-50');
  });

  it('handles thumbs down click when not locked', () => {
    render(<ThumbsFeedback fieldValue="" onFieldChange={mockOnChange} isLocked={false} />);

    const thumbsDown = screen.getByTestId('thumbs-down-icon');
    fireEvent.click(thumbsDown);

    expect(mockOnChange).toHaveBeenCalledWith('0');
    expect(thumbsDown).toHaveClass('cursor-pointer');
    expect(thumbsDown).not.toHaveClass('cursor-not-allowed', 'opacity-50');
  });

  it('disables clicks when locked', () => {
    render(<ThumbsFeedback fieldValue="1" onFieldChange={mockOnChange} isLocked={true} />);

    const thumbsUp = screen.getByTestId('thumbs-up-icon');
    const thumbsDown = screen.getByTestId('thumbs-down-icon');

    fireEvent.click(thumbsUp);
    fireEvent.click(thumbsDown);

    expect(mockOnChange).not.toHaveBeenCalled();
    [thumbsUp, thumbsDown].forEach(thumb => {
      expect(thumb).toHaveClass('cursor-not-allowed', 'opacity-50');
    });
  });

  it('allows changing selection when not locked', () => {
    const { rerender } = render(
      <ThumbsFeedback fieldValue="1" onFieldChange={mockOnChange} isLocked={false} />
    );

    // Click thumbs down after having thumbs up selected
    const thumbsDown = screen.getByTestId('thumbs-down-icon');
    fireEvent.click(thumbsDown);
    expect(mockOnChange).toHaveBeenCalledWith('0');

    // Simulate the state change
    rerender(<ThumbsFeedback fieldValue="0" onFieldChange={mockOnChange} isLocked={false} />);

    // Verify the visual state changed
    expect(thumbsDown).toHaveClass('text-error-500');
    expect(thumbsDown).toHaveAttribute('fill', 'currentColor');
  });

  it('handles empty fieldValue correctly', () => {
    render(<ThumbsFeedback fieldValue="" onFieldChange={mockOnChange} isLocked={false} />);

    const thumbsUp = screen.getByTestId('thumbs-up-icon');
    const thumbsDown = screen.getByTestId('thumbs-down-icon');

    [thumbsUp, thumbsDown].forEach(thumb => {
      expect(thumb).toHaveClass('text-muted-foreground');
      expect(thumb).toHaveAttribute('fill', 'none');
    });
  });
});
