import React from 'react';
import { render, screen } from '@testing-library/react';
import { describe, it, expect, vi } from 'vitest';
import FormRenderer from '../FormRenderer';
import { RendererType, SenderType, FormFieldType, FormType } from '../../types/enums';
import type { FormMessage, ChatMessage } from '../../types/types';

// Mock DynamicFormPrompt since we only want to test form enabling/disabling
vi.mock('../DynamicFormPrompt', () => {
  return {
    default: vi.fn(props => (
      <form className="space-y-3 p-2 mb-3 border border-muted-400 rounded-lg">
        <input
          type="text"
          name="text"
          disabled={props.formDisabled}
          value={(props.defaultValues?.text as string) || ''}
          data-testid="form-input"
        />
        <button type="submit" disabled={props.formDisabled} data-testid="form-submit">
          Submit
        </button>
      </form>
    )),
  };
});

describe('FormRenderer', () => {
  const defaultMsg: FormMessage = {
    nodeType: RendererType.FORM,
    sender: SenderType.BOT,
    data: {
      prompt: [
        {
          fieldName: 'name',
          fieldType: FormFieldType.TEXT,
          label: 'Enter your name',
          required: true,
        },
      ],
      formType: FormType.SINGLE_ASK_FORM,
    },
  };

  it('renders form with input and submit button when prompt is present', () => {
    render(<FormRenderer msg={defaultMsg} idx={0} />);
    expect(screen.getByTestId('text-input')).toBeInTheDocument();
    expect(screen.getByRole('button', { name: 'Submit' })).toBeInTheDocument();
  });

  it('does not render form when prompt is empty', () => {
    const msgWithoutPrompt: FormMessage = {
      ...defaultMsg,
      data: {
        ...defaultMsg.data,
        prompt: [],
      },
    };

    render(<FormRenderer msg={msgWithoutPrompt} idx={0} />);
    expect(screen.queryByTestId('text-input')).not.toBeInTheDocument();
    expect(screen.queryByRole('button', { name: 'Submit' })).not.toBeInTheDocument();
  });

  it('does not render when nodeType is not FORM', () => {
    const nonFormMsg: ChatMessage = {
      nodeType: RendererType.MESSAGE,
      sender: SenderType.BOT,
      data: {
        text: 'Hello',
      },
    };

    render(<FormRenderer msg={nonFormMsg} idx={0} />);
    expect(screen.queryByTestId('text-input')).not.toBeInTheDocument();
  });

  it('disables form when submittedValues are present', () => {
    const submittedMsg: FormMessage = {
      ...defaultMsg,
      data: {
        ...defaultMsg.data,
        submittedValues: { name: 'John' },
      },
    };

    render(<FormRenderer msg={submittedMsg} idx={0} />);
    const input = screen.getByTestId('text-input');
    const submit = screen.getByRole('button', { name: 'Submit' });

    expect(input).toBeDisabled();
    expect(submit).toBeDisabled();
  });

  it('disables form when formLocked is true and message is not lastFormPrompt', () => {
    const currentFormPrompt: FormMessage = { ...defaultMsg };
    const differentFormPrompt: FormMessage = {
      ...defaultMsg,
      data: {
        ...defaultMsg.data,
        prompt: [
          {
            fieldName: 'otherField',
            fieldType: FormFieldType.TEXT,
            label: 'Other field',
            required: true,
          },
        ],
      },
    };

    render(
      <FormRenderer
        msg={currentFormPrompt}
        idx={0}
        lastFormPrompt={differentFormPrompt}
        formLocked={true}
      />
    );

    const input = screen.getByTestId('text-input');
    expect(input).toBeDisabled();
  });

  it('does not lock form when message is lastFormPrompt', () => {
    const currentFormPrompt: FormMessage = { ...defaultMsg };

    render(
      <FormRenderer
        msg={currentFormPrompt}
        idx={0}
        lastFormPrompt={currentFormPrompt}
        formLocked={true}
      />
    );

    const input = screen.getByTestId('text-input');
    expect(input).not.toBeDisabled();
  });

  it('sets input value from submittedValues', () => {
    const submittedMsg: FormMessage = {
      ...defaultMsg,
      data: {
        ...defaultMsg.data,
        submittedValues: { name: 'John' },
      },
    };

    render(<FormRenderer msg={submittedMsg} idx={0} />);
    expect(screen.getByTestId('text-input')).toHaveValue('John');
  });

  it('uses lastFormFieldValues when submittedValues are not present', () => {
    const lastFormFieldValues = { name: 'Jane' };

    render(<FormRenderer msg={defaultMsg} idx={0} lastFormFieldValues={lastFormFieldValues} />);

    expect(screen.getByTestId('text-input')).toHaveValue('Jane');
  });
});
