import React from 'react';
import { render, screen, fireEvent } from '@testing-library/react';
import { describe, it, expect, vi } from 'vitest';
import TextRating from '../TextRating';

describe('TextRating', () => {
  const mockOnChange = vi.fn();

  beforeEach(() => {
    mockOnChange.mockClear();
  });

  it('renders 5 numbered buttons', () => {
    render(<TextRating fieldValue="" onFieldChange={mockOnChange} isLocked={false} />);

    const buttons = screen.getAllByRole('button');
    expect(buttons).toHaveLength(5);

    buttons.forEach((button, index) => {
      expect(button).toHaveTextContent((index + 1).toString());
    });
  });

  it('shows correct selected state for buttons', () => {
    render(<TextRating fieldValue="3" onFieldChange={mockOnChange} isLocked={false} />);

    const buttons = screen.getAllByRole('button');

    // Selected button should have primary background
    expect(buttons[2]).toHaveClass('bg-primary', 'text-primary-foreground', 'border-primary');

    // Other buttons should have background style
    buttons.forEach((button, index) => {
      if (index !== 2) {
        expect(button).toHaveClass('bg-background', 'text-muted-foreground', 'border-input');
      }
    });
  });

  it('handles button clicks when not locked', () => {
    render(<TextRating fieldValue="3" onFieldChange={mockOnChange} isLocked={false} />);

    const buttons = screen.getAllByRole('button');
    fireEvent.click(buttons[4]); // Click the 5th button

    expect(mockOnChange).toHaveBeenCalledWith('5');
    expect(buttons[4]).toHaveClass('cursor-pointer');
    expect(buttons[4]).not.toHaveClass('cursor-not-allowed', 'opacity-50');
  });

  it('disables buttons when locked', () => {
    render(<TextRating fieldValue="3" onFieldChange={mockOnChange} isLocked={true} />);

    const buttons = screen.getAllByRole('button');
    fireEvent.click(buttons[4]); // Try to click the 5th button

    expect(mockOnChange).not.toHaveBeenCalled();
    buttons.forEach(button => {
      expect(button).toBeDisabled();
      expect(button).toHaveClass('cursor-not-allowed', 'opacity-50');
    });
  });

  it('updates rating when clicking different buttons', () => {
    render(<TextRating fieldValue="3" onFieldChange={mockOnChange} isLocked={false} />);

    const buttons = screen.getAllByRole('button');

    // Click each button and verify the callback
    buttons.forEach((button, index) => {
      fireEvent.click(button);
      expect(mockOnChange).toHaveBeenLastCalledWith((index + 1).toString());
    });

    expect(mockOnChange).toHaveBeenCalledTimes(5);
  });

  it('handles empty fieldValue correctly', () => {
    render(<TextRating fieldValue="" onFieldChange={mockOnChange} isLocked={false} />);

    const buttons = screen.getAllByRole('button');

    // No button should be selected
    buttons.forEach(button => {
      expect(button).toHaveClass('bg-background', 'text-muted-foreground', 'border-input');
      expect(button).not.toHaveClass('bg-primary', 'text-primary-foreground', 'border-primary');
    });
  });

  it('maintains correct button type', () => {
    render(<TextRating fieldValue="" onFieldChange={mockOnChange} isLocked={false} />);

    const buttons = screen.getAllByRole('button');
    buttons.forEach(button => {
      expect(button).toHaveAttribute('type', 'button');
    });
  });
});
