import React from 'react';
import { render, screen } from '@testing-library/react';
import { describe, it, expect, vi } from 'vitest';
import MessageRenderer from '../MessageRenderer';
import { RendererType, SenderType } from '../../types/enums';
import type { BotMessage, UserMessage } from '../../types';

// Mock SVG imports
vi.mock('@/assets/icons/bot.svg', () => ({ default: 'bot.svg' }));

// Mock RichTextEditor component
vi.mock('../../rich-text-editor', () => ({
  RichTextEditor: ({ content }: { content: string }) => (
    <div data-testid="rte-container" className="flex flex-col">
      <div data-testid="editor-content">
        <div data-testid="editor-view" className="ProseMirror">
          <p>{content}</p>
        </div>
      </div>
    </div>
  ),
}));

describe('MessageRenderer', () => {
  const userMessage: UserMessage = {
    nodeType: RendererType.USER_MESSAGE,
    sender: SenderType.USER,
    data: {
      text: 'Hello, how are you?',
    },
  };

  const botMessage: BotMessage = {
    nodeType: RendererType.MESSAGE,
    sender: SenderType.BOT,
    data: {
      text: 'I am doing well, thank you!',
    },
  };

  const botMessageWithContent: BotMessage = {
    nodeType: RendererType.MESSAGE,
    sender: SenderType.BOT,
    data: {
      content: 'Alternative content field',
    },
  };

  it('renders user message correctly', () => {
    render(<MessageRenderer msg={userMessage} idx={0} />);

    // Check if message content is rendered
    expect(screen.getByTestId('editor-view')).toHaveTextContent('Hello, how are you?');

    // Check for user avatar (Lucide User icon)
    const userIcon = screen.getByText('', { selector: 'svg.lucide-user' });
    expect(userIcon).toBeInTheDocument();

    // Check for correct alignment classes
    const container = screen.getByTestId('rte-container').parentElement?.parentElement;
    expect(container).toHaveClass('justify-end');

    // Check for correct styling classes
    const messageContainer = screen.getByTestId('rte-container').parentElement;
    expect(messageContainer).toHaveClass('bg-primary-100', 'text-primary-900', 'rounded-br-md');
  });

  it('renders bot message correctly', () => {
    render(<MessageRenderer msg={botMessage} idx={0} />);

    // Check if message content is rendered
    expect(screen.getByTestId('editor-view')).toHaveTextContent('I am doing well, thank you!');

    // Check for bot avatar
    const botAvatar = screen.getByRole('img', { name: 'Bot Avatar' });
    expect(botAvatar).toBeInTheDocument();
    expect(botAvatar).toHaveAttribute('src', 'bot.svg');

    // Check for correct alignment classes
    const container = screen.getByTestId('rte-container').parentElement?.parentElement;
    expect(container).toHaveClass('justify-start');

    // Check for correct styling classes
    const messageContainer = screen.getByTestId('rte-container').parentElement;
    expect(messageContainer).toHaveClass('bg-tertiary-100', 'text-secondary-900', 'rounded-bl-md');
  });

  it('renders bot message with content field when text is not available', () => {
    render(<MessageRenderer msg={botMessageWithContent} idx={0} />);

    expect(screen.getByTestId('editor-view')).toHaveTextContent('Alternative content field');
  });

  it('returns null when no content is available', () => {
    const emptyMessage: BotMessage = {
      nodeType: RendererType.MESSAGE,
      sender: SenderType.BOT,
      data: {},
    };

    const { container } = render(<MessageRenderer msg={emptyMessage} idx={0} />);
    expect(container).toBeEmptyDOMElement();
  });
});
