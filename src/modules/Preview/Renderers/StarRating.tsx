import React from 'react';
import { Star } from 'lucide-react';
import { cn } from '@/lib/utils';

interface StarRatingProps {
  fieldValue: string;
  onFieldChange: (value: string) => void;
  isLocked: boolean;
}

const StarRating: React.FC<StarRatingProps> = ({ fieldValue, onFieldChange, isLocked }) => {
  return (
    <div className="flex gap-2">
      {Array.from({ length: 5 }, (_, i) => {
        const starValue = (i + 1).toString();
        const isActive = fieldValue && starValue <= fieldValue;

        return (
          <Star
            key={starValue}
            className={cn(
              'w-7 h-7 transition-colors',
              isLocked ? 'cursor-not-allowed opacity-50' : 'cursor-pointer',
              isActive ? 'text-warning-500' : 'text-muted-foreground'
            )}
            onClick={!isLocked ? () => onFieldChange(starValue) : undefined}
            fill={isActive ? 'currentColor' : 'none'}
          />
        );
      })}
    </div>
  );
};

export default StarRating;
