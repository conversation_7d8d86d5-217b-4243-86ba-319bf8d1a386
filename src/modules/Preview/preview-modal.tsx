'use client';
import React, { useState, useCallback, useRef, useEffect } from 'react';
import { useAppDispatch, useAppSelector } from '@/hooks/useRedux';
import { togglePreview } from '@/store/slices/uiSlice';
import { useBotIdParam } from '@/hooks/useRouterParam';
import { useTranslation } from 'react-i18next';
import { useForm } from 'react-hook-form';
import PreviewHeader from './previewHeader';
import ChatMessages from './ChatMessages';
import ChatInput from './ChatInput';
import ConfirmDialog from './ConfirmDialog';
import {
  PlatformType,
  FormType,
  RendererType,
  BotFormFields,
  ChatMessage,
  FeedbackMessage,
  FormMessage,
  SenderType,
} from './types';
import { useResetConversationMutation } from '@/store/api';
import DebuggerPanel from './debugger';
import { SendMessageRequest } from '@/types/botInteraction.type';
import { useWebSocket } from '@/hooks/useWebSocket';
import { SocketEvents } from './config';
import {
  createUserMessage,
  createFormSubmissionPayload,
  createTextMessagePayload,
  processServerResponse,
  isSingleTextFieldForm,
  hasBlockingForm,
} from './utils/messageUtils';
import { useToast } from '@/hooks/use-toast';
import { ApiSliceIdentifier, getBaseUrl } from '@/store/helper';

type SocketResponseEvent = {
  isAgentHandoff?: boolean;
  ticketId: string;
  code?: number;
  message?: string;
  response?: any;
};

type ReceiveMessageEvent = {
  senderType: string;
  chatId: string;
  message: string;
  text: string;
  timestamp: string;
};

type MemberJoinedEvent = {
  isAgent: boolean;
  chatId: string;
};

const PreviewModal: React.FC = () => {
  const { t } = useTranslation();
  const dispatch = useAppDispatch();
  const { toast } = useToast();
  const { showPreview } = useAppSelector(state => state.ui);
  const [showDebugger, setShowDebugger] = useState(false);
  const { botId } = useBotIdParam();
  const [platform, setPlatform] = useState<PlatformType>(PlatformType.Web);
  const [messages, setMessages] = useState<ChatMessage[]>([]);
  const [resetConversationApi, { isLoading: isResettingConversation }] =
    useResetConversationMutation();
  const [error, setError] = useState<string | null>(null);
  const [lastFormPrompt, setLastFormPrompt] = useState<ChatMessage | null>(null);
  const [lastFormFieldValues, setLastFormFieldValues] = useState<BotFormFields | undefined>(
    undefined
  );
  const [showConfirmDialog, setShowConfirmDialog] = useState(false);
  const [formLocked, setFormLocked] = useState(false);
  const [isFormActive, setIsFormActive] = useState(false);
  const [isFeedbackForm, setIsFeedbackForm] = useState(false);
  const conversationIdRef = useRef<string>(crypto.randomUUID());
  const [isAgentHandoffActive, setIsAgentHandoffActive] = useState(false);
  const [isWaitingForAgent, setIsWaitingForAgent] = useState(false);
  const [agentJoined, setAgentJoined] = useState(false);

  // WebSocket state management
  const { emit, on, userId } = useWebSocket(getBaseUrl(ApiSliceIdentifier.CHATBOT_SERVICE)!);
  const hasJoinedRef = useRef(false);
  const ticketIdRef = useRef<string>('');
  const chatIdRef = useRef<string>('');
  const [isLoading, setIsLoading] = useState(false);

  const loading =
    isResettingConversation || isLoading || (isAgentHandoffActive && isWaitingForAgent);

  const chatForm = useForm<{ message: string }>({
    defaultValues: { message: '' },
  });
  const { setValue } = chatForm;

  const isFormValid = useCallback((): boolean => {
    if (!lastFormPrompt) return false;
    if (isFeedbackForm) {
      return Boolean(lastFormFieldValues?.feedback);
    }
    // Type guard to ensure we have FormData
    if (lastFormPrompt.nodeType === RendererType.FORM && 'prompt' in lastFormPrompt.data) {
      return lastFormPrompt.data.prompt.every(
        (field: any) => !field.required || lastFormFieldValues?.[field.fieldName]
      );
    }
    return false;
  }, [lastFormPrompt, lastFormFieldValues]);

  useEffect(() => {
    const cleanup = on(SocketEvents.RECEIVE_BOT_RESPONSE, (data: SocketResponseEvent) => {
      setIsLoading(false);
      setFormLocked(false);

      if (data.code) {
        setError(data.message ?? t('errors.somethingWrong'));
        return;
      }

      if (data.isAgentHandoff) {
        setIsAgentHandoffActive(true);
        setIsWaitingForAgent(true);
        setAgentJoined(false);
        ticketIdRef.current = data.ticketId;

        emit(SocketEvents.JOIN_CHANNEL, data.ticketId);

        // Add system message about handoff
        const handoffMessage: ChatMessage = {
          sender: SenderType.BOT,
          nodeType: RendererType.MESSAGE,
          data: {
            text: 'Connecting you to an agent...',
            type: 'text',
            timestamp: new Date().toISOString(),
          },
        };
        setMessages(prev => [...prev, handoffMessage]);

        return;
      }

      const botMessages = processServerResponse(data.response ?? []);
      setMessages(prev => [...prev, ...botMessages]);

      const formMessage = botMessages.find(m => m.nodeType === RendererType.FORM);
      const feedbackMessage = botMessages.find(m => m.nodeType === RendererType.FEEDBACK);

      if (formMessage) {
        setLastFormPrompt(formMessage);
        const initial: BotFormFields = {};
        formMessage.data.prompt.forEach(field => (initial[field.fieldName] = ''));
        setLastFormFieldValues(initial);
      } else if (feedbackMessage) {
        setLastFormPrompt(feedbackMessage);
        setLastFormFieldValues({ feedback: '' });
        setIsFeedbackForm(true);
      } else {
        setLastFormPrompt(null);
        setLastFormFieldValues(undefined);
      }
    });

    return () => {
      cleanup();
    };
  }, [on, t]);

  useEffect(() => {
    const cleanup = on(SocketEvents.RECEIVE_MESSAGE, (data: ReceiveMessageEvent) => {
      if (data.senderType !== SenderType.BOT) {
        if (isAgentHandoffActive) {
          const agentMessage: ChatMessage = {
            sender: SenderType.BOT,
            nodeType: RendererType.MESSAGE,
            data: {
              text: data.message ?? data.text ?? '',
              type: 'text',
              timestamp: new Date().toISOString(),
            },
          };
          if (chatIdRef.current !== data.chatId) {
            chatIdRef.current = data.chatId;
          }

          setMessages(prev => [...prev, agentMessage]);

          if (!agentJoined && isWaitingForAgent) {
            setAgentJoined(true);
            setIsWaitingForAgent(false);
          }
        }
      }
    });
    return () => {
      cleanup();
    };
  }, [on, isAgentHandoffActive, agentJoined, isWaitingForAgent]);

  useEffect(() => {
    const cleanup = on(SocketEvents.MEMBER_JOINED, (data: MemberJoinedEvent) => {
      if (isAgentHandoffActive && data.isAgent) {
        setAgentJoined(true);
        setIsWaitingForAgent(false);

        const agentJoinedMessage: ChatMessage = {
          sender: SenderType.BOT,
          nodeType: RendererType.MESSAGE,
          data: {
            text: 'Agent has joined the conversation',
            type: 'text',
            timestamp: new Date().toISOString(),
          },
        };
        setMessages(prev => [...prev, agentJoinedMessage]);
      }
    });
    return () => {
      cleanup();
    };
  }, [on, isAgentHandoffActive]);

  useEffect(() => {
    const cleanup = on(SocketEvents.CHAT_ENDED_FROM_BOT, () => {
      setMessages(prev => [
        ...prev,
        {
          sender: SenderType.BOT,
          nodeType: RendererType.MESSAGE,
          data: {
            text: 'Agent has left the conversation',
            type: 'text',
            timestamp: new Date().toISOString(),
          },
        },
      ]);
      setIsAgentHandoffActive(false);
      setIsWaitingForAgent(false);
      setAgentJoined(false);
      emit(SocketEvents.LEAVE_CHANNEL, ticketIdRef.current);
      ticketIdRef.current = '';
      chatIdRef.current = '';
      hasJoinedRef.current = false;
    });
    return () => {
      cleanup();
    };
  }, [on]);

  const sendMessageToServer = useCallback(
    async (body: SendMessageRequest) => {
      setError(null);
      setIsLoading(true);
      setFormLocked(true);

      try {
        if (!hasJoinedRef.current) {
          hasJoinedRef.current = true;
        }

        emit(SocketEvents.SEND_BOT_MESSAGE, {
          conversationId: userId,
          ticketId: userId,
          message: body,
          userId,
        });
      } catch (err: any) {
        setError(err.message ?? t('errors.somethingWrong'));
        toast({
          title: t('common.error'),
          description: err,
          variant: 'destructive',
        });
      }
    },
    [emit, t]
  );

  const handleChatInputSubmit = useCallback(
    async (fields: { message: string }) => {
      if (loading) return;

      const userInput = fields.message.trim();
      if (!userInput) return;

      if (isAgentHandoffActive) {
        const userMessage = createUserMessage(userInput);
        setMessages(prev => [...prev, userMessage]);

        if (hasJoinedRef.current) {
          emit(SocketEvents.SEND_MESSAGE, {
            ticketId: ticketIdRef.current,
            text: `<p>${userInput}</p>`,
            senderType: SenderType.BOT,
            chatId: chatIdRef.current,
            senderId: userId,
            userId,
          });
        }

        chatForm.reset({ message: '' });
        setValue('message', '');
        return;
      }

      if (hasBlockingForm(lastFormPrompt)) {
        return; // multi-field form → block sending
      }

      setMessages(prev => [...prev, createUserMessage(userInput)]);

      const textPayload = createTextMessagePayload(userInput, botId, conversationIdRef.current);
      chatForm.reset({ message: '' });
      await sendMessageToServer(textPayload);

      setValue('message', '');
    },
    [
      botId,
      loading,
      sendMessageToServer,
      chatForm,
      lastFormPrompt,
      setValue,
      isAgentHandoffActive,
      emit,
    ]
  );

  const handleFormPromptSubmit = useCallback(
    async (fields: BotFormFields) => {
      if (!lastFormPrompt || loading) return;

      const formPayload = createFormSubmissionPayload(
        fields,
        botId,
        userId,

        lastFormPrompt.nodeType === RendererType.FORM ? lastFormPrompt.data?.formId : undefined
      );

      setMessages(prev => {
        if (!lastFormPrompt || !formPayload.formData) {
          return prev; // Should not happen if logic is correct, but for safety
        }

        const lastFormPromptIndex = prev.indexOf(lastFormPrompt);

        if (lastFormPromptIndex === -1) {
          return prev; // lastFormPrompt not found in prev array, return as is
        }

        // Assert lastFormPrompt to be a FormMessage or FeedbackMessage
        const currentFormPrompt = lastFormPrompt as FormMessage | FeedbackMessage;

        const updatedData = {
          ...currentFormPrompt.data,
          submittedValues: formPayload.formData,
        };

        const updatedLastFormPrompt = {
          ...currentFormPrompt,
          data: updatedData,
        } as ChatMessage;

        const newMessages = [...prev];
        newMessages[lastFormPromptIndex] = updatedLastFormPrompt;
        return newMessages;
      });

      setLastFormFieldValues(fields);
      setFormLocked(true);

      await sendMessageToServer(formPayload);
    },
    [lastFormPrompt, loading, sendMessageToServer, botId]
  );

  const resetConversation = useCallback(async () => {
    try {
      await resetConversationApi({ conversationId: conversationIdRef.current }).unwrap();
    } catch (err: any) {
      console.error('Reset failed:', err);
      toast({
        title: t('common.error'),
        description: err,
        variant: 'destructive',
      });
      emit(SocketEvents.LEAVE_CHANNEL, conversationIdRef.current);
    }
  }, [emit]);

  const clearState = useCallback(() => {
    setMessages([]);
    setValue('message', '');
    setLastFormPrompt(null);
    setLastFormFieldValues(undefined);
    setFormLocked(false);
    setError(null);
    hasJoinedRef.current = false;
    conversationIdRef.current = crypto.randomUUID();
    setIsAgentHandoffActive(false);
    setIsWaitingForAgent(false);
    setAgentJoined(false);
    dispatch(togglePreview());
  }, [dispatch, setValue]);

  const handleClose = useCallback(() => {
    if (messages.length > 0) setShowConfirmDialog(true);
    else clearState();
  }, [messages, clearState]);

  const confirmClose = useCallback(
    async (confirm: boolean) => {
      setShowConfirmDialog(false);
      if (confirm) {
        await resetConversation();
        clearState();
      }
    },
    [resetConversation, clearState]
  );

  const isSingleTextField =
    isSingleTextFieldForm(lastFormPrompt) &&
    Object.values(FormType).includes(lastFormPrompt.data.formType);

  if (!showPreview) return null;

  return (
    <div className="fixed right-0 top-0 bottom-0 border bg-background flex items-center justify-center z-50">
      <div className="w-96 h-full flex flex-col">
        <PreviewHeader
          platform={platform}
          setPlatform={setPlatform}
          onDebuggerToggle={() => setShowDebugger(!showDebugger)}
          onClose={handleClose}
        />
        <div className="flex-1 p-4 overflow-auto">
          <div className="bg-background rounded-lg h-full flex flex-col relative">
            <ChatMessages
              messages={messages}
              lastFormPrompt={lastFormPrompt}
              formLocked={formLocked}
              onFormSubmit={handleFormPromptSubmit}
              lastFormFieldValues={lastFormFieldValues}
              loading={isLoading}
              onFormActiveChange={setIsFormActive}
            />
            {error && <div className="text-error-500 text-xs mt-2">{error}</div>}

            {/* Agent Connecting Overlay */}
            {isAgentHandoffActive && isWaitingForAgent && (
              <div className="inset-0 bg-background/80 backdrop-blur-sm flex flex-col items-center justify-center z-10">
                <p className="text-lg font-semibold">{t('agentTransfer.connectingToAgent')}</p>
                <p className="text-sm text-tertiary-500 mt-2">
                  {t('agentTransfer.waitingForAgentMessage')}
                </p>
              </div>
            )}

            <ChatInput
              activeForm={lastFormPrompt}
              loading={isLoading}
              isFormValid={isFormValid}
              disabled={isFormActive || (isAgentHandoffActive && isWaitingForAgent)}
              isSingleTextField={!!isSingleTextField}
              onSubmit={handleChatInputSubmit}
              form={chatForm}
            />
          </div>
        </div>
        <ConfirmDialog
          open={showConfirmDialog}
          onOpenChange={setShowConfirmDialog}
          onConfirm={confirmClose}
        />
      </div>
      {showDebugger && (
        <DebuggerPanel conversationId={conversationIdRef.current} onOpenChange={setShowDebugger} />
      )}
    </div>
  );
};

export default PreviewModal;
