import React from 'react';
import { render, screen, fireEvent, act } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { beforeEach, describe, expect, it, vi } from 'vitest';
import DebuggerPanel from '../DebuggerPanel';
import { DebuggerEvent, DebuggerEventType, LogLevel } from '@/types/botInteraction.type';
import { render as customRender } from '@/test/utils';

vi.mock('@/lib/utils', () => ({
  cn: (...args: any[]) => args.filter(Boolean).join(' '),
}));

vi.mock('@/store/helper', () => ({
  getBaseUrl: () => 'http://localhost:8080',
  ApiSliceIdentifier: {
    BOT_INTERACTION_SERVICE: 'bot-interaction',
  },
}));

// Mock EventSource
class MockEventSource {
  url: string;
  onmessage: ((event: MessageEvent) => void) | null = null;
  onerror: ((event: Event) => void) | null = null;

  constructor(url: string) {
    this.url = url;
    MockEventSource.instances.push(this);
  }

  close = vi.fn();
  static instances: MockEventSource[] = [];

  static clearInstances() {
    MockEventSource.instances = [];
  }

  static dispatchMessage(data: any) {
    MockEventSource.instances.forEach(instance => {
      if (instance.onmessage) {
        instance.onmessage(new MessageEvent('message', { data: JSON.stringify(data) }));
      }
    });
  }

  static dispatchError() {
    MockEventSource.instances.forEach(instance => {
      if (instance.onerror) {
        instance.onerror(new Event('error'));
      }
    });
  }
}

vi.stubGlobal('EventSource', MockEventSource);

describe('DebuggerPanel', () => {
  const mockOnOpenChange = vi.fn();
  const conversationId = 'test-conversation-id';
  const MIN_HEIGHT = 320;

  beforeEach(() => {
    vi.clearAllMocks();
    MockEventSource.clearInstances();
    Object.defineProperty(window, 'innerHeight', { writable: true, value: 1000 });
  });

  it('renders correctly with initial elements', () => {
    customRender(<DebuggerPanel conversationId={conversationId} onOpenChange={mockOnOpenChange} />);

    expect(screen.getByText('Debugger')).toBeInTheDocument();
    expect(screen.getByLabelText('Close debugger panel')).toBeInTheDocument();
    expect(screen.getByText('Logs')).toBeInTheDocument();
    expect(screen.getByText('AI Analysis')).toBeInTheDocument();
    expect(screen.getByText('Session Data')).toBeInTheDocument();
  });

  it('applies correct initial height', () => {
    const { container } = customRender(
      <DebuggerPanel conversationId={conversationId} onOpenChange={mockOnOpenChange} />
    );
    const panel = container.querySelector('.fixed');
    expect(panel).toHaveStyle(`height: ${MIN_HEIGHT}px`);
  });

  it('calls onOpenChange when close button is clicked', async () => {
    customRender(<DebuggerPanel conversationId={conversationId} onOpenChange={mockOnOpenChange} />);
    await userEvent.click(screen.getByLabelText('Close debugger panel'));
    expect(mockOnOpenChange).toHaveBeenCalledWith(false);
  });

  it('switches tabs correctly', async () => {
    customRender(<DebuggerPanel conversationId={conversationId} onOpenChange={mockOnOpenChange} />);

    // Default logs tab should show "no logs" message
    expect(screen.getByText('No logs available.')).toBeInTheDocument();

    // Switch to AI Analysis tab
    await userEvent.click(screen.getByText('AI Analysis'));
    expect(screen.getByText('No AI Analysis logs available.')).toBeInTheDocument();

    // Switch to Session Data tab
    await userEvent.click(screen.getByText('Session Data'));
    expect(screen.getByText('No Session Data logs available.')).toBeInTheDocument();

    // Switch back to Logs tab
    await userEvent.click(screen.getByText('Logs'));
    expect(screen.getByText('No logs available.')).toBeInTheDocument();
  });

  it('initializes EventSource with correct URL', () => {
    customRender(<DebuggerPanel conversationId={conversationId} onOpenChange={mockOnOpenChange} />);

    expect(MockEventSource.instances.length).toBe(1);
    const es = MockEventSource.instances[0];
    expect(es.url).toBe(
      `http://localhost:8080/debugger/stream/${conversationId}?token=mockAccessToken`
    );
  });

  it('handles LOG events and displays them in Logs tab', async () => {
    customRender(<DebuggerPanel conversationId={conversationId} onOpenChange={mockOnOpenChange} />);

    const mockLogEvent: DebuggerEvent = {
      type: DebuggerEventType.LOG,
      payload: { level: LogLevel.INFO, message: 'Test info message' },
      timestamp: '2023-01-01T00:00:00Z',
      conversationId: 'test-id',
    };

    await act(async () => {
      MockEventSource.dispatchMessage(mockLogEvent);
    });

    expect(screen.getByText('Test info message')).toBeInTheDocument();
    expect(screen.queryByText('debugger.noLogs')).not.toBeInTheDocument();
  });

  it('handles different log levels in Logs component', async () => {
    customRender(<DebuggerPanel conversationId={conversationId} onOpenChange={mockOnOpenChange} />);

    const logEvents: DebuggerEvent[] = [
      {
        type: DebuggerEventType.LOG,
        payload: { level: LogLevel.ERROR, message: 'Error message' },
        timestamp: '2023-01-01T00:00:00Z',
        conversationId: 'test-id',
      },
      {
        type: DebuggerEventType.LOG,
        payload: { level: LogLevel.WARNING, message: 'Warning message' },
        timestamp: '2023-01-01T00:00:01Z',
        conversationId: 'test-id',
      },
      {
        type: DebuggerEventType.LOG,
        payload: { level: LogLevel.INFO, message: 'Info message' },
        timestamp: '2023-01-01T00:00:02Z',
        conversationId: 'test-id',
      },
    ];

    for (const event of logEvents) {
      await act(async () => {
        MockEventSource.dispatchMessage(event);
      });
    }

    expect(screen.getByText('Error message')).toBeInTheDocument();
    expect(screen.getByText('Warning message')).toBeInTheDocument();
    expect(screen.getByText('Info message')).toBeInTheDocument();
  });

  it('handles NLU_LOG events and displays them in AI Analysis tab', async () => {
    customRender(<DebuggerPanel conversationId={conversationId} onOpenChange={mockOnOpenChange} />);

    const mockNluEvent: DebuggerEvent = {
      type: DebuggerEventType.NLU_LOG,
      payload: {
        intent: { name: 'greeting', confidence: 0.95 },
        entities: [{ entity: 'name', value: 'John' }],
        text: 'Hello John',
      },
      timestamp: '2023-01-01T00:00:00Z',
      conversationId: 'test-id',
    };

    await act(async () => {
      MockEventSource.dispatchMessage(mockNluEvent);
    });

    await userEvent.click(screen.getByText('AI Analysis'));

    expect(screen.getByText(/"intent"/)).toBeInTheDocument();
    expect(screen.getByText(/"greeting"/)).toBeInTheDocument();
    expect(screen.getByText(/0.95/)).toBeInTheDocument();
    expect(screen.queryByText('debugger.noAiAnalysisLogs')).not.toBeInTheDocument();
  });

  it('handles CONTEXT events and displays them in Session Data tab', async () => {
    customRender(<DebuggerPanel conversationId={conversationId} onOpenChange={mockOnOpenChange} />);

    const mockContextEvent: DebuggerEvent = {
      type: DebuggerEventType.CONTEXT,
      payload: {
        chatConversationId: 'test-conv',
        botId: 'test-bot',
        invokedIntent: null,
        sessionStartedAt: new Date('2023-01-01'),
        lastActivityAt: new Date('2023-01-01'),
        metadata: { key: 'value' },
        expiresAt: new Date('2023-01-02'),
        journeyContext: { step: 1 },
      },
      timestamp: '2023-01-01T00:00:00Z',
      conversationId: 'test-id',
    };

    await act(async () => {
      MockEventSource.dispatchMessage(mockContextEvent);
    });

    await userEvent.click(screen.getByText('Session Data'));

    expect(screen.getByText(/"chatConversationId"/)).toBeInTheDocument();
    expect(screen.getByText(/"test-conv"/)).toBeInTheDocument();
    expect(screen.queryByText('debugger.noSessionDataLogs')).not.toBeInTheDocument();
  });

  it('filters events correctly in each tab', async () => {
    customRender(<DebuggerPanel conversationId={conversationId} onOpenChange={mockOnOpenChange} />);

    const events: DebuggerEvent[] = [
      {
        type: DebuggerEventType.LOG,
        payload: { level: LogLevel.INFO, message: 'Log message' },
        timestamp: '2023-01-01T00:00:00Z',
        conversationId: 'test-id',
      },
      {
        type: DebuggerEventType.NLU_LOG,
        payload: { intent: { name: 'test', confidence: 0.9 }, entities: [], text: 'test' },
        timestamp: '2023-01-01T00:00:01Z',
        conversationId: 'test-id',
      },
      {
        type: DebuggerEventType.CONTEXT,
        payload: {
          chatConversationId: 'test',
          botId: 'test',
          invokedIntent: null,
          sessionStartedAt: new Date(),
          lastActivityAt: new Date(),
          metadata: {},
          expiresAt: new Date(),
          journeyContext: {},
        },
        timestamp: '2023-01-01T00:00:02Z',
        conversationId: 'test-id',
      },
    ];

    for (const event of events) {
      await act(async () => {
        MockEventSource.dispatchMessage(event);
      });
    }

    // Logs tab should only show LOG events
    expect(screen.getByText('Log message')).toBeInTheDocument();

    // AI Analysis tab should only show NLU_LOG events
    await userEvent.click(screen.getByText('AI Analysis'));
    expect(screen.getByText(/"intent"/)).toBeInTheDocument();
    expect(screen.queryByText('Log message')).not.toBeInTheDocument();

    // Session Data tab should only show CONTEXT events
    await userEvent.click(screen.getByText('Session Data'));
    expect(screen.getByText(/"chatConversationId"/)).toBeInTheDocument();
    expect(screen.queryByText('Log message')).not.toBeInTheDocument();
  });

  it('handles EventSource errors', () => {
    const consoleErrorSpy = vi.spyOn(console, 'error').mockImplementation(() => {});
    customRender(<DebuggerPanel conversationId={conversationId} onOpenChange={mockOnOpenChange} />);

    const es = MockEventSource.instances[0];
    act(() => {
      MockEventSource.dispatchError();
    });

    expect(consoleErrorSpy).toHaveBeenCalledWith('EventSource failed:', expect.any(Event));
    expect(es.close).toHaveBeenCalledTimes(1);
    consoleErrorSpy.mockRestore();
  });

  it('handles malformed EventSource messages gracefully', () => {
    const consoleErrorSpy = vi.spyOn(console, 'error').mockImplementation(() => {});
    customRender(<DebuggerPanel conversationId={conversationId} onOpenChange={mockOnOpenChange} />);

    const es = MockEventSource.instances[0];
    act(() => {
      if (es.onmessage) {
        es.onmessage(new MessageEvent('message', { data: 'invalid json' }));
      }
    });

    expect(consoleErrorSpy).toHaveBeenCalledWith(
      'Error parsing debugger event:',
      expect.any(Error)
    );
    consoleErrorSpy.mockRestore();
  });

  it('closes EventSource on unmount', () => {
    const { unmount } = customRender(
      <DebuggerPanel conversationId={conversationId} onOpenChange={mockOnOpenChange} />
    );
    const es = MockEventSource.instances[0];
    unmount();
    expect(es.close).toHaveBeenCalledTimes(1);
  });

  it('does not initialize EventSource if conversationId is empty', () => {
    customRender(<DebuggerPanel conversationId="" onOpenChange={mockOnOpenChange} />);
    expect(MockEventSource.instances.length).toBe(0);
  });

  it('reinitializes EventSource when conversationId changes', () => {
    const { rerender } = customRender(
      <DebuggerPanel conversationId={conversationId} onOpenChange={mockOnOpenChange} />
    );
    expect(MockEventSource.instances.length).toBe(1);
    const firstEs = MockEventSource.instances[0];

    rerender(
      <DebuggerPanel conversationId="new-conversation-id" onOpenChange={mockOnOpenChange} />
    );
    expect(firstEs.close).toHaveBeenCalledTimes(1);
    expect(MockEventSource.instances.length).toBe(2);
    expect(MockEventSource.instances[1].url).toBe(
      `http://localhost:8080/debugger/stream/new-conversation-id?token=mockAccessToken`
    );
  });

  describe('Resizing functionality', () => {
    let mockOffsetHeight = MIN_HEIGHT;

    beforeEach(() => {
      mockOffsetHeight = MIN_HEIGHT;
      Object.defineProperty(HTMLElement.prototype, 'offsetHeight', {
        configurable: true,
        get: function () {
          return mockOffsetHeight;
        },
      });
    });

    it('handles resize drag correctly', async () => {
      const { container } = customRender(
        <DebuggerPanel conversationId={conversationId} onOpenChange={mockOnOpenChange} />
      );
      const resizeHandle = screen.getByLabelText('debugger-resize-handle');
      const panel = container.querySelector('.fixed') as HTMLElement;

      fireEvent.mouseDown(resizeHandle, { clientY: 500 });

      await act(async () => {
        fireEvent.mouseMove(document, { clientY: 400 });
      });

      expect(panel.style.height).toBe(`${MIN_HEIGHT + 100}px`);

      fireEvent.mouseUp(document);
    });

    it('respects MIN_HEIGHT during resizing', async () => {
      const { container } = customRender(
        <DebuggerPanel conversationId={conversationId} onOpenChange={mockOnOpenChange} />
      );
      const resizeHandle = screen.getByLabelText('debugger-resize-handle');
      const panel = container.querySelector('.fixed') as HTMLElement;

      fireEvent.mouseDown(resizeHandle, { clientY: 500 });

      await act(async () => {
        fireEvent.mouseMove(document, { clientY: 1000 });
      });

      expect(panel.style.height).toBe(`${MIN_HEIGHT}px`);

      fireEvent.mouseUp(document);
    });

    it('respects MAX_HEIGHT during resizing', async () => {
      const { container } = customRender(
        <DebuggerPanel conversationId={conversationId} onOpenChange={mockOnOpenChange} />
      );
      const resizeHandle = screen.getByLabelText('debugger-resize-handle');
      const panel = container.querySelector('.fixed') as HTMLElement;
      const MAX_HEIGHT = 950;

      fireEvent.mouseDown(resizeHandle, { clientY: 500 });

      await act(async () => {
        fireEvent.mouseMove(document, { clientY: -500 });
      });

      expect(panel.style.height).toBe(`${MAX_HEIGHT}px`);

      fireEvent.mouseUp(document);
    });

    it('stops resizing on mouse up', async () => {
      const { container } = customRender(
        <DebuggerPanel conversationId={conversationId} onOpenChange={mockOnOpenChange} />
      );
      const resizeHandle = screen.getByLabelText('debugger-resize-handle');
      const panel = container.querySelector('.fixed') as HTMLElement;

      fireEvent.mouseDown(resizeHandle, { clientY: 500 });
      fireEvent.mouseMove(document, { clientY: 400 });
      fireEvent.mouseUp(document);

      const heightAfterMouseUp = panel.style.height;

      fireEvent.mouseMove(document, { clientY: 300 });
      expect(panel.style.height).toBe(heightAfterMouseUp);
    });

    it('prevents default on mouse down', () => {
      customRender(
        <DebuggerPanel conversationId={conversationId} onOpenChange={mockOnOpenChange} />
      );
      const resizeHandle = screen.getByLabelText('debugger-resize-handle');

      const mouseDownEvent = new MouseEvent('mousedown', {
        bubbles: true,
        cancelable: true,
        clientY: 500,
      });

      const preventDefaultSpy = vi.spyOn(mouseDownEvent, 'preventDefault');
      fireEvent(resizeHandle, mouseDownEvent);

      expect(preventDefaultSpy).toHaveBeenCalled();
    });

    it('handles resize when panelRef.current offsetHeight is undefined', async () => {
      const { container } = customRender(
        <DebuggerPanel conversationId={conversationId} onOpenChange={mockOnOpenChange} />
      );
      const resizeHandle = screen.getByLabelText('debugger-resize-handle');
      const panel = container.querySelector('.fixed') as HTMLElement;

      Object.defineProperty(HTMLElement.prototype, 'offsetHeight', {
        configurable: true,
        get: function () {
          return undefined;
        },
      });

      fireEvent.mouseDown(resizeHandle, { clientY: 500 });

      await act(async () => {
        fireEvent.mouseMove(document, { clientY: 400 });
      });

      expect(panel.style.height).toBe(`${MIN_HEIGHT + 100}px`);

      fireEvent.mouseUp(document);
    });
  });

  it('calculates MAX_HEIGHT correctly based on window.innerHeight', () => {
    Object.defineProperty(window, 'innerHeight', { writable: true, value: 800 });

    const { container } = customRender(
      <DebuggerPanel conversationId={conversationId} onOpenChange={mockOnOpenChange} />
    );
    const resizeHandle = screen.getByLabelText('debugger-resize-handle');
    const panel = container.querySelector('.fixed') as HTMLElement;

    fireEvent.mouseDown(resizeHandle, { clientY: 500 });

    act(() => {
      fireEvent.mouseMove(document, { clientY: -500 });
    });

    expect(panel.style.height).toBe('750px');

    fireEvent.mouseUp(document);
  });

  it('applies correct tab styling based on active state', () => {
    customRender(<DebuggerPanel conversationId={conversationId} onOpenChange={mockOnOpenChange} />);

    const logsTab = screen.getByText('Logs');
    const aiAnalysisTab = screen.getByText('AI Analysis');

    expect(logsTab).toHaveAttribute('data-state', 'active');
    expect(aiAnalysisTab).not.toHaveAttribute('data-state', 'active');
  });

  it('handles log with unknown level gracefully', async () => {
    customRender(<DebuggerPanel conversationId={conversationId} onOpenChange={mockOnOpenChange} />);

    const mockLogEvent: DebuggerEvent = {
      type: DebuggerEventType.LOG,
      payload: { level: 'unknown' as LogLevel, message: 'Unknown level message' },
      timestamp: '2023-01-01T00:00:00Z',
      conversationId: 'test-id',
    };

    await act(async () => {
      MockEventSource.dispatchMessage(mockLogEvent);
    });

    expect(screen.getByText('Unknown level message')).toBeInTheDocument();
  });
});
