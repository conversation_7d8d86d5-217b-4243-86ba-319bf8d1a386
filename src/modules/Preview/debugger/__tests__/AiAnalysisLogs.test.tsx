import React from 'react';
import { render, screen } from '@testing-library/react';
import { describe, it, expect, vi } from 'vitest';
import AiAnalysisLogs from '../AiAnalysisLogs';
import { DebuggerEvent, DebuggerEventType, LogLevel } from '@/types/botInteraction.type';

// Mock i18next
vi.mock('react-i18next', () => ({
  useTranslation: () => ({
    t: (key: string) =>
      key === 'debugger.noAiAnalysisLogs' ? 'No AI analysis logs available' : key,
  }),
}));

describe('AiAnalysisLogs', () => {
  it('renders empty state when no logs are provided', () => {
    render(<AiAnalysisLogs logs={[]} />);

    expect(screen.getByText('No AI analysis logs available')).toBeInTheDocument();
  });

  it('renders empty state when no NLU logs are present', () => {
    const logs: DebuggerEvent[] = [
      {
        type: DebuggerEventType.LOG,
        timestamp: '2025-09-19T10:00:00Z',
        conversationId: '123',
        payload: {
          level: LogLevel.INFO,
          message: 'test',
        },
      },
    ];

    render(<AiAnalysisLogs logs={logs} />);

    expect(screen.getByText('No AI analysis logs available')).toBeInTheDocument();
  });

  it('renders NLU logs when present', () => {
    const mockPayload = {
      intent: {
        name: 'greeting',
        confidence: 0.95,
      },
      entities: [],
      text: 'hello',
    };

    const logs: DebuggerEvent[] = [
      {
        type: DebuggerEventType.NLU_LOG,
        timestamp: '2025-09-19T10:00:00Z',
        conversationId: '123',
        payload: mockPayload,
      },
    ];

    render(<AiAnalysisLogs logs={logs} />);

    const preElement = screen.getByRole('presentation');
    const displayedJson = JSON.parse(preElement.textContent || '');
    expect(displayedJson).toEqual(mockPayload);
  });

  it('renders multiple NLU logs in order', () => {
    const mockLogs: DebuggerEvent[] = [
      {
        type: DebuggerEventType.NLU_LOG,
        timestamp: '2025-09-19T10:00:00Z',
        conversationId: '123',
        payload: {
          intent: {
            name: 'greeting',
            confidence: 0.95,
          },
          entities: [],
          text: 'hello',
        },
      },
      {
        type: DebuggerEventType.NLU_LOG,
        timestamp: '2025-09-19T10:01:00Z',
        conversationId: '123',
        payload: {
          intent: {
            name: 'farewell',
            confidence: 0.92,
          },
          entities: [],
          text: 'goodbye',
        },
      },
    ];

    render(<AiAnalysisLogs logs={mockLogs} />);

    const preElements = screen.getAllByRole('presentation');
    expect(preElements).toHaveLength(2);

    const firstLog = JSON.parse(preElements[0].textContent || '');
    const secondLog = JSON.parse(preElements[1].textContent || '');

    expect(firstLog.intent.name).toBe('greeting');
    expect(secondLog.intent.name).toBe('farewell');
  });

  it('filters out non-NLU logs', () => {
    const mockLogs: DebuggerEvent[] = [
      {
        type: DebuggerEventType.NLU_LOG,
        timestamp: '2025-09-19T10:00:00Z',
        conversationId: '123',
        payload: {
          intent: {
            name: 'greeting',
            confidence: 0.95,
          },
          entities: [],
          text: 'hello',
        },
      },
      {
        type: DebuggerEventType.LOG,
        timestamp: '2025-09-19T10:01:00Z',
        conversationId: '123',
        payload: {
          level: LogLevel.INFO,
          message: 'other log',
        },
      },
    ];

    render(<AiAnalysisLogs logs={mockLogs} />);

    const preElements = screen.getAllByRole('presentation');
    expect(preElements).toHaveLength(1);

    const firstLog = JSON.parse(preElements[0].textContent || '');
    expect(firstLog.intent.name).toBe('greeting');
  });
});
