import { SenderType, RendererType, FormFieldType, FormType } from './enums';

export interface FormFieldData {
  fieldName: string;
  fieldType: FormFieldType;
  label?: string;
  required?: boolean;
  rangeStart?: string;
  rangeEnd?: string;
}

export interface BotFormFields {
  [key: string]: string | Date | undefined;
}

export interface UserMessageData {
  text: string;
}

export interface BotMessageData {
  text?: string;
  content?: string;
  type?: string; // e.g., 'text'
  timestamp?: string;
}

export interface FormData {
  prompt: FormFieldData[];
  submittedValues?: BotFormFields;
  formId?: string;
  formType: FormType;
}

export interface FeedbackData {
  text: string;
  type: string;
  submittedValues?: BotFormFields; // This is added locally after submission
  timestamp?: string;
}

// Discriminated Union for ChatMessage
interface BaseChatMessage {
  sender: SenderType;
  conversationId?: string;
}

export type UserMessage = BaseChatMessage & {
  nodeType: RendererType.USER_MESSAGE;
  data: UserMessageData;
};

export type BotMessage = BaseChatMessage & {
  nodeType: RendererType.MESSAGE;
  data: BotMessageData;
};

export type FormMessage = BaseChatMessage & {
  nodeType: RendererType.FORM;
  data: FormData;
};

export type FeedbackMessage = BaseChatMessage & {
  nodeType: RendererType.FEEDBACK;
  data: FeedbackData;
};

export type AgentHandoffMessage = BaseChatMessage & {
  nodeType: RendererType.MESSAGE;
  data: BotMessageData;
  isAgent?: boolean;
  isSystemMessage?: boolean;
};

export type ChatMessage =
  | UserMessage
  | BotMessage
  | FormMessage
  | FeedbackMessage
  | AgentHandoffMessage;
