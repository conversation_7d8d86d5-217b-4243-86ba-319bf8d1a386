import z from 'zod';
import { FormFieldData } from './types/types';
import { FormFieldType } from './types/enums';
import { format } from 'date-fns';
import { PhoneNumberUtil } from 'google-libphonenumber';
import i18n from 'i18next';

const phoneUtil = PhoneNumberUtil.getInstance();
export const createPhoneSchema = (field: FormFieldData) =>
  z
    .string()
    .optional()
    .refine(
      val => {
        if (!val || val.trim() === '') return !field.required;
        try {
          const regionCode = phoneUtil.getRegionCodeForNumber(phoneUtil.parse(val));
          const parsed = phoneUtil.parseAndKeepRawInput(val, regionCode);
          return phoneUtil.isValidNumberForRegion(parsed, regionCode);
        } catch (err) {
          console.warn('Phone validation failed', err);
          return false;
        }
      },
      {
        message: i18n.t('validation.invalidPhoneNumber', { field: field.label ?? field.fieldName }),
      }
    );

const isValidDate = (value: string | Date): boolean => {
  const date = value instanceof Date ? value : new Date(value);
  return !isNaN(date.getTime()) && date.toString() !== 'Invalid Date';
};

const withRequired = <T extends z.ZodTypeAny>(schema: T, field: FormFieldData, message: string) =>
  field.required
    ? schema.refine(val => val !== undefined && val !== '' && val !== null, { message })
    : schema.optional();

export const createTextFieldSchema = (field: FormFieldData) =>
  withRequired(
    z
      .string()
      .trim()
      .min(1, {
        message: i18n.t('validation.fieldRequired', { field: field.label ?? field.fieldName }),
      }),
    field,
    i18n.t('validation.fieldRequired', { field: field.label ?? field.fieldName })
  );

export const createEmailSchema = (field: FormFieldData) =>
  withRequired(
    z
      .string()
      .trim()
      .email({
        message: i18n.t('validation.invalidEmail', { field: field.label ?? field.fieldName }),
      }),
    field,
    i18n.t('validation.fieldRequired', { field: field.label ?? field.fieldName })
  );

export const createTimeSchema = (field: FormFieldData) =>
  withRequired(
    z
      .string()
      .trim()
      .min(1, { message: `${field.label ?? field.fieldName} ${i18n.t('validation.isRequired')}` }),
    field,
    `${field.label ?? field.fieldName} ${i18n.t('validation.isRequired')}`
  );

export const createDateSchema = (field: FormFieldData) =>
  withRequired(
    z
      .union([z.string().trim(), z.date()])
      .transform(val => (typeof val === 'string' && val.trim() !== '' ? new Date(val) : val))
      .refine(val => val !== undefined && isValidDate(val), {
        message: `${field.label ?? field.fieldName} ${i18n.t('validation.validDate')}`,
      }),
    field,
    i18n.t('validation.fieldRequired', { field: field.label ?? field.fieldName })
  );

export const createPastDateSchema = (field: FormFieldData) => {
  const currentDate = new Date();
  return withRequired(
    z
      .union([z.string().trim(), z.date()])
      .transform(val => (typeof val === 'string' && val.trim() !== '' ? new Date(val) : val))
      .refine(val => val !== undefined && isValidDate(val), {
        message: `${field.label ?? field.fieldName} ${i18n.t('validation.validDate')}`,
      })
      .refine(val => val < currentDate, {
        message: `${field.label ?? field.fieldName} ${i18n.t('validation.pastDate')} ${format(
          currentDate,
          'PPP p'
        )})`,
      }),
    field,
    i18n.t('validation.fieldRequired', { field: field.label ?? field.fieldName })
  );
};

export const createFutureDateSchema = (field: FormFieldData) => {
  const currentDate = new Date();
  return withRequired(
    z
      .union([z.string().trim(), z.date()])
      .refine(val => !val || isValidDate(val), {
        message: `${field.label ?? field.fieldName} ${i18n.t('validation.validDate')}`,
      })
      .transform(val => (typeof val === 'string' && val.trim() !== '' ? new Date(val) : val))
      .refine(val => !val || val > currentDate, {
        message: `${field.label ?? field.fieldName} ${i18n.t('validation.futuredate')} ${format(
          currentDate,
          'PPP p'
        )})`,
      }),
    field,
    i18n.t('validation.fieldRequired', { field: field.label ?? field.fieldName })
  );
};

export const createNumberSchema = (field: FormFieldData) =>
  withRequired(
    z
      .string()
      .trim()
      .refine(val => val === '' || !isNaN(Number(val)), {
        message: `${field.label ?? field.fieldName} ${i18n.t('validation.validDate')}`,
      })
      .transform(val => (val === '' ? undefined : Number(val)))
      .refine(val => val === undefined || !isNaN(val), {
        message: `${field.label ?? field.fieldName} ${i18n.t('validation.validDate')}`,
      }),
    field,
    `${field.label ?? field.fieldName} ${i18n.t('validation.isRequired')}`
  );

export const createFormValidationSchema = (fields: FormFieldData[]) =>
  z.object(
    fields.reduce(
      (schema, field) => {
        switch (field.fieldType) {
          case FormFieldType.TEXT:
          case FormFieldType.TEXT_FIELD:
            schema[field.fieldName] = createTextFieldSchema(field);
            break;
          case FormFieldType.EMAIL:
            schema[field.fieldName] = createEmailSchema(field);
            break;
          case FormFieldType.NUMBER:
            schema[field.fieldName] = createNumberSchema(field);
            break;
          case FormFieldType.TIME:
            schema[field.fieldName] = createTimeSchema(field);
            break;
          case FormFieldType.DATE:
            schema[field.fieldName] = createDateSchema(field);
            break;
          case FormFieldType.PAST_DATE:
            schema[field.fieldName] = createPastDateSchema(field);
            break;
          case FormFieldType.FUTURE_DATE:
            schema[field.fieldName] = createFutureDateSchema(field);
            break;
          case FormFieldType.MOBILE_NUMBER:
            schema[field.fieldName] = createPhoneSchema(field);
            break;
          default:
            schema[field.fieldName] = z.string().trim().optional();
        }
        return schema;
      },
      {} as Record<string, z.ZodTypeAny>
    )
  );
