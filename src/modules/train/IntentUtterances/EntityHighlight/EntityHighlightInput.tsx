import React, { useState, useRef, useCallback, useEffect, useMemo } from 'react';
import { Entity } from '@/types';
import { useGetEntitiesQuery } from '@/store/api';
import { cn } from '@/lib/utils';
import { Button } from '@/components/ui/button';
import { X } from 'lucide-react';
import { useTranslation } from 'react-i18next';
import {
  expandToWord,
  getDisplayText,
  getEntityColor,
  HistoryState,
  HoveredEntity,
  ParsedEntity,
  parseEntities,
  reconcileTextWithEntities,
  SelectionState,
} from './utils';
import EntityDropdown from './EntitiesDropdown';
import { EntityTooltip } from './EntityTooltip';

// Types
interface EntityHighlightInputProps {
  value: string;
  onChange: (value: string) => void;
  placeholder?: string;
  className?: string;
  botId: string;
  intentId?: string;
  autoFocus?: boolean;
}

// Main Component
const EntityHighlightInput: React.FC<EntityHighlightInputProps> = ({
  value,
  onChange,
  placeholder,
  className,
  botId,
  intentId,
  autoFocus,
}) => {
  const { t } = useTranslation();
  const defaultPlaceholder = placeholder || t('intents.utterances.enterPlaceholder');

  const [selection, setSelection] = useState<SelectionState>({ text: '', start: 0, end: 0 });
  const [showDropdown, setShowDropdown] = useState(false);
  const [searchTerm, setSearchTerm] = useState('');
  const [hoveredEntity, setHoveredEntity] = useState<HoveredEntity | null>(null);
  const [error, setError] = useState<string>('');
  const [history, setHistory] = useState<HistoryState>({ undo: [], redo: [] });

  const [isTyping, setIsTyping] = useState(false);
  const contentEditableRef = useRef<HTMLDivElement>(null);
  const typingTimeoutRef = useRef<NodeJS.Timeout | null>(null);
  const tooltipTimeoutRef = useRef<NodeJS.Timeout | null>(null);
  const isTooltipHoveredRef = useRef(false);

  // Flags to control DOM & history side-effects
  const isInternalUpdateRef = useRef(false); // prevents clobbering caret on controlled updates
  const isApplyingHistoryRef = useRef(false); // prevents pushing history while undo/redo is applying

  useEffect(() => {
    if (autoFocus && contentEditableRef.current) {
      contentEditableRef.current.focus();
    }
  }, [autoFocus]);

  const { data: entitiesResponse } = useGetEntitiesQuery({ botId, page: 1, limit: 100 });
  const filteredEntities = useMemo(
    () =>
      entitiesResponse?.data?.items.filter(
        (entity: Entity) =>
          (entity.intentId === null || entity.intentId === intentId) &&
          (searchTerm === '' ||
            entity.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
            entity.type.toLowerCase().includes(searchTerm.toLowerCase()))
      ) || [],
    [entitiesResponse, intentId, searchTerm]
  );

  const renderContent = useCallback(() => {
    const entities = parseEntities(value);
    const displayText = getDisplayText(value);
    if (entities.length === 0) return displayText;

    const parts: string[] = [];
    let lastEnd = 0;
    const displayEntities = entities.map(entity => {
      const displayStart = getDisplayText(value.substring(0, entity.start)).length;
      return { ...entity, displayStart, displayEnd: displayStart + entity.text.length };
    });

    displayEntities.forEach(entity => {
      if (entity.displayStart > lastEnd) {
        parts.push(displayText.substring(lastEnd, entity.displayStart));
      }
      const color = getEntityColor(entity.type);
      parts.push(
        `<span class="${color} border rounded px-1 cursor-pointer inline-block whitespace-nowrap" data-entity-id="${entity.id}" data-entity-name="${entity.name}" data-entity-type="${entity.type}" data-display-start="${entity.displayStart}">${entity.text}</span>`
      );
      lastEnd = entity.displayEnd;
    });

    if (lastEnd < displayText.length) {
      parts.push(displayText.substring(lastEnd));
    }
    return parts.join('');
  }, [value]);

  // Save & restore caret using offsets
  const saveCursor = useCallback(() => {
    const selection = window.getSelection();
    if (!selection || selection.rangeCount === 0 || !contentEditableRef.current) {
      return { position: 0, node: null, offset: 0 };
    }
    const range = selection.getRangeAt(0);
    if (!contentEditableRef.current.contains(range.startContainer)) {
      return { position: 0, node: null, offset: 0 };
    }
    const beforeRange = range.cloneRange();
    beforeRange.selectNodeContents(contentEditableRef.current);
    beforeRange.setEnd(range.startContainer, range.startOffset);
    return {
      position: beforeRange.toString().length,
      node: range.startContainer,
      offset: range.startOffset,
    };
  }, []);

  const restoreCursor = useCallback(
    (saved: { position: number; node: Node | null; offset: number }) => {
      if (!contentEditableRef.current) return;
      const { position, node, offset } = saved;

      try {
        if (node && contentEditableRef.current.contains(node) && node.nodeType === Node.TEXT_NODE) {
          const range = document.createRange();
          range.setStart(node, Math.min(offset, node.textContent?.length ?? 0));
          range.collapse(true);
          const sel = window.getSelection();
          sel?.removeAllRanges();
          sel?.addRange(range);
          return;
        }

        if (position > 0) {
          const walker = document.createTreeWalker(
            contentEditableRef.current,
            NodeFilter.SHOW_TEXT
          );
          let currentPos = 0;
          let textNode = walker.nextNode() as Text | null;
          while (textNode) {
            const nodeText = textNode.textContent ?? '';
            const nodeEnd = currentPos + nodeText.length;
            if (position <= nodeEnd) {
              const range = document.createRange();
              range.setStart(textNode, Math.min(position - currentPos, nodeText.length));
              range.collapse(true);
              const sel = window.getSelection();
              sel?.removeAllRanges();
              sel?.addRange(range);
              break;
            }
            currentPos = nodeEnd;
            textNode = walker.nextNode() as Text | null;
          }
        } else {
          const walker = document.createTreeWalker(
            contentEditableRef.current,
            NodeFilter.SHOW_TEXT
          );
          const first = walker.nextNode();
          if (first) {
            const range = document.createRange();
            range.setStart(first, 0);
            range.collapse(true);
            const sel = window.getSelection();
            sel?.removeAllRanges();
            sel?.addRange(range);
          }
        }
      } catch {
        // ignore
      }
    },
    []
  );

  const setDOMSelectionByOffsets = useCallback((start: number, end: number) => {
    if (!contentEditableRef.current) return;
    const walker = document.createTreeWalker(contentEditableRef.current, NodeFilter.SHOW_TEXT);
    let currentPos = 0;
    let startNode: Text | null = null;
    let endNode: Text | null = null;
    let startOffset = 0;
    let endOffset = 0;

    let node = walker.nextNode() as Text | null;
    while (node) {
      const len = node.textContent?.length ?? 0;
      if (!startNode && start <= currentPos + len) {
        startNode = node;
        startOffset = Math.max(0, Math.min(len, start - currentPos));
      }
      if (!endNode && end <= currentPos + len) {
        endNode = node;
        endOffset = Math.max(0, Math.min(len, end - currentPos));
        break;
      }
      currentPos += len;
      node = walker.nextNode() as Text | null;
    }

    if (startNode && endNode) {
      const range = document.createRange();
      range.setStart(startNode, startOffset);
      range.setEnd(endNode, endOffset);
      const sel = window.getSelection();
      sel?.removeAllRanges();
      sel?.addRange(range);
    }
  }, []);

  const getSelectionOffsets = useCallback(() => {
    const sel = window.getSelection();
    if (!sel || sel.rangeCount === 0 || !contentEditableRef.current) return null;
    const range = sel.getRangeAt(0);
    if (!contentEditableRef.current.contains(range.commonAncestorContainer)) return null;

    const before = range.cloneRange();
    before.selectNodeContents(contentEditableRef.current);
    before.setEnd(range.startContainer, range.startOffset);
    const start = before.toString().length;
    const end = start + range.toString().length;
    return { start, end };
  }, []);

  const checkEntityOverlap = useCallback(
    (start: number, end: number, entities: ParsedEntity[]) => {
      for (const entity of entities) {
        const entityDisplayStart = getDisplayText(value.substring(0, entity.start)).length;
        const entityDisplayEnd = entityDisplayStart + entity.text.length;
        if (!(end <= entityDisplayStart || start >= entityDisplayEnd)) {
          return { hasOverlap: true, conflictingEntity: entity };
        }
      }
      return { hasOverlap: false };
    },
    [value]
  );

  // ---- History helpers ----
  const pushUndo = useCallback((prevValue: string) => {
    setHistory(h => ({ undo: [...h.undo.slice(-49), prevValue], redo: [] })); // cap at 50
  }, []);

  const handleUndo = useCallback(() => {
    setHistory(h => {
      if (h.undo.length === 0) return h;
      const target = h.undo[h.undo.length - 1];
      isApplyingHistoryRef.current = true;
      isInternalUpdateRef.current = true;
      onChange(target);
      return { undo: h.undo.slice(0, -1), redo: [...h.redo, value] };
    });
  }, [onChange, value]);

  const handleRedo = useCallback(() => {
    setHistory(h => {
      if (h.redo.length === 0) return h;
      const target = h.redo[h.redo.length - 1];
      isApplyingHistoryRef.current = true;
      isInternalUpdateRef.current = true;
      onChange(target);
      return { undo: [...h.undo, value], redo: h.redo.slice(0, -1) };
    });
  }, [onChange, value]);

  // ----------------- Event handlers -----------------
  const handleContentChange = useCallback(() => {
    if (!contentEditableRef.current) return;
    setIsTyping(true);
    if (typingTimeoutRef.current) clearTimeout(typingTimeoutRef.current);
    typingTimeoutRef.current = setTimeout(() => setIsTyping(false), 500);

    const newText = contentEditableRef.current.textContent ?? '';
    const newValue = reconcileTextWithEntities(newText, value);
    if (newValue !== value) {
      if (!isApplyingHistoryRef.current) pushUndo(value);
      isInternalUpdateRef.current = true;
      onChange(newValue);
    }
  }, [value, onChange, pushUndo]);

  const handleSelection = useCallback(() => {
    const offsets = getSelectionOffsets();
    if (!offsets || !contentEditableRef.current) {
      setShowDropdown(false);
      setSelection({ text: '', start: 0, end: 0 });
      return;
    }
    let { start, end } = offsets;
    if (start === end) {
      setShowDropdown(false);
      setSelection({ text: '', start: 0, end: 0 });
      return;
    }

    const displayText = contentEditableRef.current.innerText;
    const expanded = expandToWord(displayText, start, end);
    setDOMSelectionByOffsets(expanded.start, expanded.end);
    setSelection({ text: expanded.word, start: expanded.start, end: expanded.end });
    setShowDropdown(true);
    setSearchTerm('');
  }, [getSelectionOffsets, setDOMSelectionByOffsets]);

  const handleKeyDown = useCallback(
    (e: React.KeyboardEvent) => {
      const key = e.key.toLowerCase();
      const mod = e.ctrlKey || e.metaKey;

      // Handle Undo/Redo
      if (handleUndoRedo(e, key, mod)) return;

      // Handle Arrow navigation out of entity spans
      if (['ArrowLeft', 'ArrowRight'].includes(e.key)) {
        handleArrowNavigation(e);
      }
    },
    [handleUndo, handleRedo]
  );

  // Handle Undo/Redo actions
  const handleUndoRedo = (e: React.KeyboardEvent, key: string, mod: boolean) => {
    if (mod && key === 'z' && !e.shiftKey) {
      e.preventDefault();
      handleUndo();
      return true;
    }
    if ((mod && e.shiftKey && key === 'z') || (mod && key === 'y')) {
      e.preventDefault();
      handleRedo();
      return true;
    }
    return false;
  };

  // Handle Arrow Navigation
  const handleArrowNavigation = (e: React.KeyboardEvent) => {
    const selection = window.getSelection();
    if (!selection || selection.rangeCount === 0 || !contentEditableRef.current) return;

    const range = selection.getRangeAt(0);
    let node = range.startContainer;
    let offset = range.startOffset;

    contentEditableRef.current.normalize();

    let entitySpan: HTMLElement | null = null;
    let currentNode: Node | null = node.nodeType === Node.ELEMENT_NODE ? node : node.parentElement;

    while (currentNode && currentNode !== contentEditableRef.current) {
      if (currentNode.nodeName === 'SPAN' && (currentNode as HTMLElement).dataset.entityId) {
        entitySpan = currentNode as HTMLElement;
        break;
      }
      currentNode = currentNode.parentElement;
    }

    if (entitySpan) {
      handleEntitySpanArrowNavigation(e, node, offset, entitySpan);
    } else {
      handleTextNodeArrowNavigation(e, node, offset);
    }
  };

  // Handle navigation when inside an entity span
  const handleEntitySpanArrowNavigation = (
    e: React.KeyboardEvent,
    node: Node,
    offset: number,
    entitySpan: HTMLElement
  ) => {
    const textNode = entitySpan.firstChild as Text;
    const textLength = textNode?.length || 0;

    if (e.key === 'ArrowRight' && node === textNode && offset === textLength) {
      e.preventDefault();
      moveSelectionAfterEntitySpan(entitySpan);
    } else if (e.key === 'ArrowLeft' && node === textNode && offset === 0) {
      e.preventDefault();
      moveSelectionBeforeEntitySpan(entitySpan);
    }
  };

  const handleTextNodeArrowNavigation = (e: React.KeyboardEvent, node: Node, offset: number) => {
    if (node.nodeType !== Node.TEXT_NODE) return;

    if (e.key === 'ArrowRight') {
      handleArrowRightNavigation(e, node, offset);
    } else if (e.key === 'ArrowLeft') {
      handleArrowLeftNavigation(e, node, offset);
    }
  };

  const handleArrowRightNavigation = (e: React.KeyboardEvent, node: Node, offset: number) => {
    const nextSibling = node.nextSibling;

    if (isEntitySpan(nextSibling) && offset === node.textContent?.length) {
      e.preventDefault();
      moveSelectionAfterEntitySpan(nextSibling as HTMLElement);
    }
  };

  const handleArrowLeftNavigation = (e: React.KeyboardEvent, node: Node, offset: number) => {
    const previousSibling = node.previousSibling;

    if (isEntitySpan(previousSibling) && offset === 0) {
      e.preventDefault();
      moveSelectionBeforeEntitySpan(previousSibling as HTMLElement);
    }
  };

  const isEntitySpan = (node: Node | null): node is HTMLElement => {
    return (
      !!node && node.nodeName === 'SPAN' && (node as HTMLElement).dataset.entityId !== undefined
    );
  };

  // Move the selection after an entity span
  const moveSelectionAfterEntitySpan = (entitySpan: HTMLElement) => {
    const newRange = document.createRange();
    newRange.setStartAfter(entitySpan);
    newRange.collapse(true);
    const selection = window.getSelection();
    if (selection) {
      selection.removeAllRanges();
      selection.addRange(newRange);
    }
  };

  // Move the selection before an entity span
  const moveSelectionBeforeEntitySpan = (entitySpan: HTMLElement) => {
    const newRange = document.createRange();
    newRange.setStartBefore(entitySpan);
    newRange.collapse(true);
    const selection = window.getSelection();
    if (selection) {
      selection.removeAllRanges();
      selection.addRange(newRange);
    }
  };

  // Insert new entity (merges, doesn't remove other text)
  const handleEntitySelect = useCallback(
    (entity: Entity) => {
      if (!selection.text) return;
      const parsed = parseEntities(value);
      const overlap = checkEntityOverlap(selection.start, selection.end, parsed);
      if (overlap.hasOverlap) {
        setError(
          `Cannot assign "${entity.name}" to "${selection.text}". Overlaps with "${overlap.conflictingEntity?.name}".`
        );
        setTimeout(() => setError(''), 5000);
        return;
      }

      const displayText = getDisplayText(value);
      const entityMarkup = `[${selection.text}](${entity.name}_${entity.id}_${entity.type})`;

      const existingParts = parsed.map(e => ({
        start: getDisplayText(value.substring(0, e.start)).length,
        end: getDisplayText(value.substring(0, e.start)).length + e.text.length,
        content: `[${e.text}](${e.name}_${e.id}_${e.type})`,
      }));

      const allParts = [
        ...existingParts,
        { start: selection.start, end: selection.end, content: entityMarkup },
      ].sort((a, b) => a.start - b.start);

      let newValue = '';
      let currentPos = 0;
      for (const part of allParts) {
        if (part.start > currentPos) {
          newValue += displayText.substring(currentPos, part.start);
        }
        newValue += part.content;
        currentPos = part.end;
      }
      if (currentPos < displayText.length) {
        newValue += displayText.substring(currentPos);
      }

      pushUndo(value);
      isInternalUpdateRef.current = true;
      onChange(newValue);

      setShowDropdown(false);
      setSelection({ text: '', start: 0, end: 0 });
      window.getSelection()?.removeAllRanges();
    },
    [selection, value, checkEntityOverlap, onChange, pushUndo]
  );

  // Remove only the hovered instance (not all with same id)
  const removeEntity = useCallback(
    (id: string, displayStart: number) => {
      const parsed = parseEntities(value);
      const displayText = getDisplayText(value);
      const displayEntities = parsed.map(e => {
        const s = getDisplayText(value.substring(0, e.start)).length;
        return { ...e, displayStart: s, displayEnd: s + e.text.length };
      });

      let newValue = '';
      let cursor = 0;
      let removedOnce = false;

      for (const e of displayEntities) {
        if (cursor < e.displayStart) {
          newValue += displayText.slice(cursor, e.displayStart);
        }

        if (!removedOnce && e.id === id && e.displayStart === displayStart) {
          // unwrap just this instance
          newValue += e.text;
          removedOnce = true;
        } else {
          newValue += `[${e.text}](${e.name}_${e.id}_${e.type})`;
        }

        cursor = e.displayEnd;
      }

      if (cursor < displayText.length) {
        newValue += displayText.slice(cursor);
      }

      pushUndo(value);
      isInternalUpdateRef.current = true;
      onChange(newValue);
      setHoveredEntity(null);
    },
    [value, onChange, pushUndo]
  );

  const handleHover = useCallback((e: React.MouseEvent) => {
    const target = e.target as HTMLElement;
    if (target.dataset.entityId && contentEditableRef.current) {
      if (tooltipTimeoutRef.current) clearTimeout(tooltipTimeoutRef.current);
      const rect = target.getBoundingClientRect();
      const containerRect = contentEditableRef.current.getBoundingClientRect();
      setHoveredEntity({
        id: target.dataset.entityId,
        name: target.dataset.entityName ?? '',
        type: target.dataset.entityType ?? '',
        x: rect.left + rect.width / 2 - containerRect.left,
        y: rect.top - containerRect.top - 10,
        displayStart: parseInt(target.dataset.displayStart ?? '0', 10),
      });
    } else if (!isTooltipHoveredRef.current) {
      if (tooltipTimeoutRef.current) clearTimeout(tooltipTimeoutRef.current);
      tooltipTimeoutRef.current = setTimeout(() => setHoveredEntity(null), 100);
    }
  }, []);

  const handleTooltipMouseEnter = useCallback(() => {
    isTooltipHoveredRef.current = true;
    if (tooltipTimeoutRef.current) clearTimeout(tooltipTimeoutRef.current);
  }, []);

  const handleTooltipMouseLeave = useCallback(() => {
    isTooltipHoveredRef.current = false;
    if (tooltipTimeoutRef.current) clearTimeout(tooltipTimeoutRef.current);
    tooltipTimeoutRef.current = setTimeout(() => setHoveredEntity(null), 100);
  }, []);

  // Keep DOM in sync
  useEffect(() => {
    if (!contentEditableRef.current) return;

    const cursor = saveCursor();
    contentEditableRef.current.innerHTML = renderContent();
    restoreCursor(cursor);

    // clear flags after applying controlled updates
    if (isApplyingHistoryRef.current) isApplyingHistoryRef.current = false;
    if (isInternalUpdateRef.current) isInternalUpdateRef.current = false;

    return () => {
      if (typingTimeoutRef.current) clearTimeout(typingTimeoutRef.current);
      if (tooltipTimeoutRef.current) clearTimeout(tooltipTimeoutRef.current);
    };
  }, [value, renderContent, saveCursor, restoreCursor]);

  // Initial render
  useEffect(() => {
    if (contentEditableRef.current) {
      contentEditableRef.current.innerHTML = renderContent();
    }
  }, [contentEditableRef.current]);

  return (
    <div className={cn('relative', className)}>
      <div
        ref={contentEditableRef}
        contentEditable
        suppressContentEditableWarning
        onInput={handleContentChange}
        onMouseUp={handleSelection}
        onKeyUp={handleSelection}
        onKeyDown={handleKeyDown}
        onMouseOver={handleHover}
        onMouseLeave={handleHover}
        className="w-full h-[40px] px-3 py-2 border border-tertiary-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500 bg-white overflow-x-auto overflow-y-hidden scrollbar-thin"
        style={{ fontSize: '14px', lineHeight: '1.5', whiteSpace: 'nowrap' }}
        data-placeholder={defaultPlaceholder}
        role="textbox"
        aria-label="Enter utterance"
      />
      {!value && (
        <div className="absolute inset-0 px-3 py-2 text-tertiary-400 pointer-events-none flex items-center text-sm">
          {defaultPlaceholder}
        </div>
      )}
      {error && (
        <div className="absolute top-full left-0 right-0 mt-1 p-2 bg-error-50 border border-error-200 rounded-md text-error-700 text-xs z-40">
          <div className="flex items-start gap-2">
            <X className="h-3 w-3 text-error-500 mt-0.5" />
            <span>{error}</span>
            <Button
              variant="ghost"
              size="sm"
              onClick={() => setError('')}
              className="ml-auto h-4 w-4 p-0 text-error-500 hover:text-error-700"
            >
              <X className="h-3 w-3" />
            </Button>
          </div>
        </div>
      )}
      {showDropdown && (
        <EntityDropdown
          entities={filteredEntities}
          selectedText={selection.text}
          onSelect={handleEntitySelect}
          onClose={() => {
            setShowDropdown(false);
            setSelection({ text: '', start: 0, end: 0 });
            setSearchTerm('');
            window.getSelection()?.removeAllRanges();
          }}
          searchTerm={searchTerm}
          setSearchTerm={setSearchTerm}
        />
      )}
      {hoveredEntity && (
        <EntityTooltip
          entity={hoveredEntity}
          onRemove={removeEntity}
          onMouseEnter={handleTooltipMouseEnter}
          onMouseLeave={handleTooltipMouseLeave}
        />
      )}
    </div>
  );
};

export default EntityHighlightInput;
