import { Button } from '@/components/ui/button';
import { Trash2 } from 'lucide-react';
import { useTranslation } from 'react-i18next';
import { HoveredEntity } from './utils';

export const EntityTooltip: React.FC<{
  entity: HoveredEntity;
  onRemove: (id: string, displayStart: number) => void;
  onMouseEnter: () => void;
  onMouseLeave: () => void;
}> = ({ entity, onRemove, onMouseEnter, onMouseLeave }) => {
  const { t } = useTranslation();
  return (
    <div
      className="absolute z-50 bg-white border border-tertiary-200 rounded-md shadow-lg p-3 text-xs min-w-[120px]"
      style={{
        left: Math.max(10, Math.min(entity.x, window.innerWidth - 150)),
        top: entity.y - 50,
        transform: 'translateX(-50%)',
      }}
      onMouseEnter={onMouseEnter}
      onMouseLeave={onMouseLeave}
    >
      <div className="flex items-center justify-between gap-2 mb-1">
        <span className="font-medium text-tertiary-900">{entity.name}</span>
        <Button
          variant="ghost"
          size="sm"
          onClick={() => onRemove(entity.id, entity.displayStart)}
          className="h-5 w-5 p-0 text-error-500 hover:text-error-700 hover:bg-error-50"
          title={t('entities.removeEntity')}
        >
          <Trash2 className="h-3 w-3" />
        </Button>
      </div>
      <div className="text-tertiary-500">{entity.type}</div>
      <div
        className="absolute top-full left-1/2 -translate-x-1/2 w-0 h-0 border-l-4 border-r-4 border-t-4 border-transparent border-t-tertiary-200"
        style={{ marginTop: '-1px' }}
      />
    </div>
  );
};
