import React, { useEffect, useState, useCallback } from 'react';
import { useTranslation } from 'react-i18next';
import { Plus } from 'lucide-react';
import { useToast } from '@/hooks/use-toast';
import { Button } from '@/components/ui/button';
import EmptyState from '@/components/EmptyState';
import LeftPanel from '@/modules/train/components/LeftPanel';
import RightPanel from '@/modules/train/components/RightPanel';
import AddModal from '@/modules/train/components/AddModal';
import TagBox from '@/components/TagBox';
import ActionDropdown from '../components/ActionDropdown';
import SuccessToastMessage from '@/components/SuccessToastMessage';
import DeleteConfirmationModal from '@/components/DeleteConfirmationModal';
import AddIntentForm from './AddIntentForm';
import RightPanelContent from './RightPanelContent';
import {
  useDeleteIntentItemMutation,
  useGetIntentItemsQuery,
  useGetUtteranceTranslationsCountQuery,
} from '@/store/api';
import { IntentItem, IntentType, ModalStateCUD } from '@/types';
import { useBotIdParam } from '@/hooks/useRouterParam';
import {
  PaginationProvider,
  usePagination,
  PaginationRenderItems,
  PaginationLoader,
  PaginationError,
} from '@/components/Pagination';
import PaginatedListItems from '../components/PaginatedListItems';
import { TooltipWrapper } from '@/components/TooltipWrapper';
import { getTruncatedText } from '@/utils/uiUtil';

const IntentUtterancesTab = () => {
  const { t } = useTranslation();
  const { toast } = useToast();
  const { botId } = useBotIdParam();

  const [selectedIntent, setSelectedIntent] = useState<IntentItem | null>(null);
  const [modalState, setModalState] = useState<ModalStateCUD<IntentItem> | null>(null);

  const { data: utteranceCountData } = useGetUtteranceTranslationsCountQuery(
    {
      intentId: modalState?.item?.id as string,
    },
    {
      skip: modalState?.type !== 'delete',
    }
  );
  const utteranceCount = utteranceCountData?.data ?? 0;

  const pagination = usePagination({
    useQueryHook: query =>
      useGetIntentItemsQuery(
        {
          filter: { botId: { eq: botId } },
          ...query,
        },
        { skip: !botId }
      ),
    initialParams: { page: 1, limit: 10 },
  });

  const {
    queryState: { data: intentItemsData, isFetching, isLoading },
  } = pagination;
  const intentItems = intentItemsData?.data?.items ?? [];

  console.log('intentItems', intentItems);

  const [deleteIntentItem] = useDeleteIntentItemMutation();

  const handleModalClose = useCallback(
    (intent?: IntentItem) => {
      setModalState(null);
      if (intent) setSelectedIntent(intent);
    },
    [pagination]
  );

  const toggleCreateModal = useCallback(() => {
    setModalState(prev => (prev?.type === 'create' ? null : { type: 'create' }));
  }, []);

  const handleEditIntent = useCallback((intent: IntentItem) => {
    setModalState({ type: 'edit', item: intent });
  }, []);

  const handleDeleteIntent = useCallback((intent: IntentItem) => {
    setModalState({ type: 'delete', item: intent });
  }, []);

  const confirmDeleteIntent = useCallback(async () => {
    if (!modalState || modalState.type !== 'delete') return;

    try {
      await deleteIntentItem({ id: modalState.item.id }).unwrap();
      toast({
        title: <SuccessToastMessage message={t('intents.intentDeleted')} />,
      });
      setSelectedIntent(null);
      handleModalClose();
    } catch (error: any) {
      toast({
        title: t('common.error'),
        description: error,
        variant: 'destructive',
      });
    }
  }, [modalState, deleteIntentItem, toast, t, handleModalClose]);

  useEffect(() => {
    if (intentItems.length && !selectedIntent && !isFetching) {
      setSelectedIntent(intentItems[0]);
    }
  }, [intentItems, selectedIntent, isFetching]);

  const renderIntentItem = useCallback(
    (intent: IntentItem) => {
      const isDefault = intent?.type === IntentType.DEFAULT;
      return (
        <>
          <TooltipWrapper content={intent.name} hidden={intent.name.length <= 16}>
            <span className="truncate">{getTruncatedText(intent.name, 16)}</span>
          </TooltipWrapper>
          <div className="flex gap-2 items-center">
            {!isDefault && (
              <>
                {!intent.flowId && <TagBox text={t('intents.noFlowsConnected')} />}
                <ActionDropdown
                  onEdit={() => handleEditIntent(intent)}
                  onDelete={() => handleDeleteIntent(intent)}
                />
              </>
            )}
          </div>
        </>
      );
    },
    [t, handleEditIntent, handleDeleteIntent]
  );

  const renderAddIntentTrigger = (trigger?: React.ReactNode) => (
    <AddModal
      title={modalState?.item?.id ? t('intents.editTitle') : t('intents.addTitle')}
      open={!!modalState && modalState?.type !== 'delete'}
      onOpenChange={toggleCreateModal}
      trigger={trigger}
    >
      <AddIntentForm botId={botId} onClose={handleModalClose} intent={modalState?.item} />
    </AddModal>
  );

  if (!isLoading && intentItems.length === 0) {
    return (
      <div className="flex flex-col h-full items-center justify-center">
        <EmptyState
          className="w-full"
          title={t('intents.startAdding')}
          description={t('common.nothingToShow')}
        >
          {renderAddIntentTrigger(
            <Button variant="outline" className="mt-4">
              {t('intents.addTitle')}
            </Button>
          )}
        </EmptyState>
      </div>
    );
  }

  return (
    <div className="flex h-full">
      <PaginationProvider value={pagination}>
        <LeftPanel
          title={t('intents.title')}
          addTrigger={
            <Button
              size="icon"
              variant="solid"
              className="w-fit h-fit rounded-sm"
              onClick={toggleCreateModal}
              data-testid="add-category-button"
            >
              <Plus className="!h-6 !w-6" />
            </Button>
          }
        >
          <PaginationRenderItems<IntentItem>
            renderItems={items => (
              <PaginatedListItems
                items={items}
                selectedItemId={selectedIntent?.id}
                onSelectItem={setSelectedIntent}
                renderItemContent={renderIntentItem}
              />
            )}
          />

          <PaginationLoader />

          <PaginationError />
        </LeftPanel>
      </PaginationProvider>
      <RightPanel title={t('intents.utterances.title')}>
        {selectedIntent ? (
          <RightPanelContent selectedIntent={selectedIntent} />
        ) : (
          <div className="flex-1 flex items-center justify-center text-tertiary-500">
            {t('intents.selectToManage')}
          </div>
        )}
      </RightPanel>

      {modalState && modalState?.type !== 'delete' && renderAddIntentTrigger()}
      <DeleteConfirmationModal
        isOpen={modalState?.type === 'delete'}
        onClose={handleModalClose}
        onConfirm={confirmDeleteIntent}
        title={t('intents.confirmDeleteTitle')}
        description={t(
          utteranceCount
            ? 'intents.deleteConfirmationMessageWithUtterances'
            : 'intents.deleteConfirmationMessage',
          { count: utteranceCount }
        )}
      />
    </div>
  );
};

export default IntentUtterancesTab;
