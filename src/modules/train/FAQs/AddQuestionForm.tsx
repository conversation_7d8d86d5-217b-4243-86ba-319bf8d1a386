import React, { useEffect, useRef, useState } from 'react';
import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';
import { Languages, LinkIcon, Plus, Smile, Sparkles, Trash2, TypeOutline } from 'lucide-react';
import RenderButtons from '@/modules/train/components/RenderButtons';
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form';
import { Card, CardContent, CardFooter } from '@/components/ui/card';
import { FaqItem } from '@/types';
import LanguageDropdown from '@/components/LanguageDropdown';
import { useAddQuestionForm } from '@/modules/train/FAQs/useAddQuestionForm';
import { useTranslation } from 'react-i18next';
import { useFieldArray } from 'react-hook-form';
import { useGetPlatformConfigByKeyQuery } from '@/store/api/platformConfigApi';
import { InboxLinkInsertModal } from './LinkInsertModal';
import ChatFileUploader from './ChatFileUploader';
import { cn } from '@/lib/utils';
import { RichTextEditor, RTEditorState } from '@/modules/rich-text-editor';
import InboxChatEmojiModal from './ChatEmojiModal';
import { useURLValidator } from '@/hooks/useValidator';
import FlowDropdown from './FlowDropdown';
import { PlatformConfigKey } from '@/types/platformConfig.type';
import { useToast } from '@/hooks/use-toast';

interface AddQuestionFormProps {
  onClose: () => void;
  categoryId: string;
  botId: string;
  faqItemNode?: FaqItem | null;
  selectedLangId: string;
}

function AddQuestionForm({
  onClose,
  categoryId,
  botId,
  faqItemNode,
  selectedLangId,
}: AddQuestionFormProps) {
  const { form, onSubmit, isLoadingTranslation, initialData } = useAddQuestionForm({
    onClose,
    categoryId,
    botId,
    faqItemNode,
    selectedLangId,
  });

  const { fields, append, remove } = useFieldArray({
    control: form.control,
    name: 'questions',
  });
  const toggleButtonRef = useRef<HTMLButtonElement | null>(null);
  const toggleLinkPopoverButtonRef = useRef<HTMLButtonElement | null>(null);
  const [showEmojiPicker, setShowEmojiPicker] = useState(false);
  const [inputValue, setInputValue] = useState('');
  const [showToolbar, setShowToolbar] = useState(false);
  const toggleToolbar = () => setShowToolbar(prev => !prev);
  const [showLinkPopover, setShowLinkPopover] = useState(false);
  const [selectedText, setSelectedText] = useState('');
  const [selectedLinkUrl, setSelectedLinkUrl] = useState<string | null>(null);
  const { t } = useTranslation();
  const [selectedFiles, setSelectedFiles] = useState<File[]>([]);
  const { data: faqQuestionLimitData } = useGetPlatformConfigByKeyQuery(
    PlatformConfigKey.FAQ_QUESTION_LIMIT
  );
  const faqQuestionLimit = Number(faqQuestionLimitData?.data?.value) || 10;
  const [initialLink, setInitialLink] = useState<{ url: string; text: string } | null>(null);
  const editorRef = useRef<RTEditorState | null>(null);
  const { validateURL } = useURLValidator();
  const { toast } = useToast();
  const handleAddQuestion = () => {
    if (fields.length >= faqQuestionLimit) {
      toast({
        variant: 'destructive',
        title: t('faqs.items.limitQuestionError'),
      });
      return;
    }
    append({ value: '' });
  };

  const handleLinkInsert = (url: string, displayText: string) => {
    if (!editorRef.current?.instance) return;
    editorRef.current.instance.insertLink(url, displayText);
    setInputValue(editorRef.current.instance.getContent());
    form.setValue('answer', editorRef.current.instance.getContent(), {
      shouldDirty: true,
      shouldTouch: true,
    });
    setShowLinkPopover(false);
  };

  const handleLinkUpdate = (url: string, displayText: string) => {
    if (!editorRef.current?.instance) return;
    editorRef.current.instance.updateLink(url, displayText);
    setInputValue(editorRef.current.instance.getContent());
    form.setValue('answer', editorRef.current.instance.getContent(), {
      shouldDirty: true,
      shouldTouch: true,
    });
    setShowLinkPopover(false);
  };

  const handleLinkRemove = () => {
    if (!editorRef.current?.instance || !initialLink) return;
    const removed = editorRef.current.instance.removeLink(initialLink.url);
    if (removed) {
      const newContent = editorRef.current.instance.getContent();
      setInputValue(newContent);
      form.setValue('answer', newContent, {
        shouldDirty: true,
        shouldTouch: true,
      });
    }
    setInitialLink(null);
    setShowLinkPopover(false);
  };

  const handleLinkClick = () => {
    if (selectedText?.trim()) {
      const [isValidURL] = validateURL(selectedText);
      if (isValidURL) {
        setInitialLink({
          url: selectedText,
          text: selectedText,
        });
      } else {
        setInitialLink({
          url: selectedLinkUrl || '',
          text: selectedText,
        });
      }
    } else {
      setInitialLink(null);
    }
    setShowLinkPopover(prev => !prev);
  };
  useEffect(() => {
    if (initialData?.answer) {
      setInputValue(initialData.answer);
      form.setValue('answer', initialData.answer);
    } else {
      setInputValue('');
    }
  }, [initialData, form]);

  useEffect(() => {
    const isTouched = form.getFieldState('answer').isTouched;
    form.setValue('answer', inputValue, {
      shouldDirty: true,
      shouldTouch: true,
      shouldValidate: isTouched,
    });
  }, [inputValue, form]);

  if (isLoadingTranslation) {
    return <div>{t('common.loading')}</div>;
  }

  const questionErrorMsg = form.formState.errors.questions?.root?.message;

  return (
    <Form {...form}>
      <form className="h-0 flex flex-col flex-1" onSubmit={form.handleSubmit(onSubmit)}>
        <Card className="flex flex-col h-0 flex-1">
          <CardContent className="pt-5 flex flex-col h-0 flex-1">
            <FormField
              control={form.control}
              name="langId"
              render={({ field }) => (
                <FormItem className="mb-4 border rounded-md flex flex-col">
                  <FormControl>
                    <LanguageDropdown onChange={field.onChange} initialValue={field.value} />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            {/* Questions Section */}
            <FormLabel className="pb-2 font-medium">{t('faqs.items.questionLabel')}</FormLabel>
            <div className="flex flex-col gap-2 overflow-y-auto custom-scrollbar">
              {fields.map((field, index) => (
                <FormField
                  key={field.id}
                  control={form.control}
                  name={`questions.${index}.value`}
                  render={({ field: formField }) => (
                    <FormItem className="flex flex-col relative">
                      <div className="flex-1 flex gap-1 items-center">
                        <FormControl>
                          <Input
                            placeholder={t('faqs.items.questionPlaceholder')}
                            className="h-11"
                            {...formField}
                            autoFocus
                          />
                        </FormControl>

                        {index === 0 && (
                          <div className="bg-tertiary-100 rounded-sm text-xs text-tertiary-500 !m-0 p-1 absolute right-3">
                            {t('faqs.items.primaryLabel')}
                          </div>
                        )}
                        {index > 0 && (
                          <Button
                            type="button"
                            variant="ghost"
                            size="icon"
                            className="absolute !bg-background right-1 !m-0 h-4 w-8"
                            onClick={() => remove(index)}
                          >
                            <Trash2 className="h-4 w-4 text-tertiary-500" />
                          </Button>
                        )}
                      </div>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              ))}

              {questionErrorMsg && <FormMessage>{questionErrorMsg}</FormMessage>}
            </div>
            <div className="flex items-center font-normal justify-between mt-2">
              <Button
                type="button"
                variant="ghost"
                className="uppercase pl-0"
                onClick={handleAddQuestion}
              >
                <Plus className="!h-5 !w-5" /> {t('common.add')}
              </Button>

              <Button
                type="button"
                variant="ghost"
                className="text-sparkle !bg-sparkle !bg-opacity-5 p-1 font-normal"
              >
                <Sparkles className="h-4 w-4" /> {t('common.generate')}
              </Button>
            </div>

            <FormField
              control={form.control}
              name="answer"
              render={({ field }) => (
                <FormItem className="mt-3 relative">
                  <FormLabel className="font-medium">{t('faqs.items.answerLabel')}</FormLabel>
                  <div className="relative mb-[18px]">
                    <RichTextEditor
                      className="pl-5"
                      content={inputValue}
                      onChange={setInputValue}
                      placeholder={t('faqs.items.answerPlaceholder')}
                      isToolbar={showToolbar}
                      onEditorState={editorState => (editorRef.current = editorState)}
                      selectedFiles={selectedFiles}
                      onFileRemove={i =>
                        setSelectedFiles(prev => prev.filter((_, idx) => idx !== i))
                      }
                      onLinkClick={(url, text) => {
                        setInitialLink({ url, text });
                        setShowLinkPopover(true);
                      }}
                      onSelectionChange={(text, href) => {
                        setSelectedText(text);
                        setSelectedLinkUrl(href);
                      }}
                    />
                    <div className="absolute bottom-4 right-4 flex items-center gap-2">
                      <Button
                        type="button"
                        variant="ghost"
                        onClick={toggleToolbar}
                        className={cn(
                          'text-tertiary-600 px-0 w-5 h-5',
                          showToolbar && 'bg-primary-100'
                        )}
                      >
                        <TypeOutline className="w-5 h-5" />
                      </Button>
                      <div className="h-4 w-px bg-tertiary-200 mx-2" />
                      <button
                        ref={toggleButtonRef}
                        type="button"
                        onClick={() => setShowEmojiPicker(!showEmojiPicker)}
                        className={cn(
                          'w-5 h-5 flex items-center justify-center rounded-md',
                          showEmojiPicker && 'bg-primary-100'
                        )}
                      >
                        <Smile className="w-5 h-5 text-tertiary-600" />
                      </button>
                      <button
                        className="w-5 h-5 rounded-sm flex items-center justify-center"
                        type="button"
                      >
                        <ChatFileUploader
                          onFileSelect={file => setSelectedFiles(prev => [...prev, file])}
                        />
                      </button>
                      <button
                        ref={toggleLinkPopoverButtonRef}
                        type="button"
                        onClick={handleLinkClick}
                        className="w-5 h-5 rounded-sm flex items-center justify-center"
                      >
                        <LinkIcon className="w-5 h-5 text-tertiary-600" />
                      </button>
                      {showLinkPopover && (
                        <InboxLinkInsertModal
                          isOpen={showLinkPopover}
                          onClose={() => setShowLinkPopover(false)}
                          onInsert={handleLinkInsert}
                          onUpdate={handleLinkUpdate}
                          onRemove={handleLinkRemove}
                          toggleButtonRef={
                            toggleLinkPopoverButtonRef as React.RefObject<HTMLElement>
                          }
                          initialUrl={initialLink?.url}
                          initialDisplayText={initialLink?.text}
                        />
                      )}
                    </div>
                  </div>
                  {showEmojiPicker && (
                    <InboxChatEmojiModal
                      show={showEmojiPicker}
                      onClose={() => setShowEmojiPicker(false)}
                      onEmojiClick={emoji => setInputValue(prev => prev + emoji.emoji)}
                      toggleButtonRef={toggleButtonRef}
                    />
                  )}
                  <FormMessage />
                </FormItem>
              )}
            />
          </CardContent>

          <CardFooter className="border-t py-3">
            <FormField
              control={form.control}
              name="translateTo"
              render={({ field }) => (
                <FormItem className="flex items-center justify-between flex-1">
                  <div className="flex gap-3 items-center">
                    <div className="flex gap-1 items-center text-tertiary-600 opacity-85">
                      <Languages className="w-5 h-5" />
                      <FormLabel>{t('common.translateTo')}:</FormLabel>
                    </div>
                    <LanguageDropdown initialValue={field.value} onChange={field.onChange} />
                  </div>
                  <Button type="button" variant="ghost" className="px-0 opacity-70 font-normal">
                    {t('common.translate')}
                  </Button>
                  <FormMessage />
                </FormItem>
              )}
            />
          </CardFooter>
        </Card>

        <FormField
          control={form.control}
          name="flowId"
          render={({ field }) => (
            <FormItem className="mt-4 space-y-0 flex gap-2 items-center">
              <FormLabel className="text-nowrap">{t('faqs.items.linkFlowLabel')}:</FormLabel>
              <FlowDropdown
                name={field.name}
                initialValue={field.value}
                onChange={field.onChange}
              />
              {field.value && (
                <Button
                  type="button"
                  variant="ghost"
                  size="icon"
                  onClick={() => field.onChange(null)}
                >
                  <Trash2 className="h-4 w-4 text-tertiary-500" />
                </Button>
              )}
            </FormItem>
          )}
        />

        <RenderButtons
          handleClose={onClose}
          handleAddClick={form.handleSubmit(onSubmit)}
          isEdit={!!initialData}
        />
      </form>
    </Form>
  );
}

export default AddQuestionForm;
