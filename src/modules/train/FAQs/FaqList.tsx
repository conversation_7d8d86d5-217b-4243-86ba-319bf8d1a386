import React, { useState } from 'react';
import { useTranslation } from 'react-i18next';
import EmptyState from '@/components/EmptyState';
import QuestionCard from './QuestionCard';
import { FaqCategory, FaqItem, ModalState } from '@/types';
import { useDeleteFaqTranslationMutation, useGetFaqsByCategoryAndLanguageQuery } from '@/store/api';
import AddModal from '../components/AddModal';
import AddQuestionForm from './AddQuestionForm';
import { Button } from '@/components/ui/button';
import { useToast } from '@/hooks/use-toast';
import SuccessToastMessage from '@/components/SuccessToastMessage';
import DeleteConfirmationModal from '@/components/DeleteConfirmationModal';
import {
  PaginationProvider,
  usePagination,
  PaginationRenderItems,
  PaginationEmptyState,
  PaginationError,
} from '@/components/Pagination';

interface FaqListProps {
  selectedCategory: FaqCategory;
  language: string;
  setIsAddQuestionModalOpen: (isOpen: boolean) => void;
}

const FaqList: React.FC<FaqListProps> = ({
  selectedCategory,
  language,
  setIsAddQuestionModalOpen,
}) => {
  const { t } = useTranslation();
  const { toast } = useToast();
  const [modalState, setModalState] = useState<ModalState<FaqItem> | null>(null);

  const pagination = usePagination({
    useQueryHook: query =>
      useGetFaqsByCategoryAndLanguageQuery(
        {
          categoryId: selectedCategory.id,
          langId: language,
          ...query,
        },
        { skip: !language }
      ),
  });

  const [deleteFaqTranslation] = useDeleteFaqTranslationMutation();

  const handleEdit = (faqItem: FaqItem) => {
    setModalState({ type: 'edit', item: faqItem });
  };

  const handleDelete = (faqItem: FaqItem) => {
    setModalState({ type: 'delete', item: faqItem });
  };

  const confirmDelete = async () => {
    if (!modalState || modalState.type !== 'delete') return;
    try {
      await deleteFaqTranslation({ id: modalState.item.id }).unwrap();
      toast({
        title: <SuccessToastMessage message={t('faqs.items.questionsDeleted')} />,
      });
    } catch (error: any) {
      console.error('Failed to delete FAQ item:', error);
      toast({
        title: t('common.error'),
        description: error,
        variant: 'destructive',
      });
    } finally {
      handleCloseModal();
    }
  };

  const handleCloseModal = () => {
    setModalState(null);
  };

  return (
    <PaginationProvider value={pagination}>
      <PaginationRenderItems<FaqItem>
        className="space-y-4 overflow-auto h-full"
        renderItems={items =>
          items.map((faqItem, index) => (
            <QuestionCard
              key={faqItem.id}
              faqItem={faqItem}
              index={index}
              onEdit={handleEdit}
              onDelete={handleDelete}
            />
          ))
        }
      />

      {/* //TODO: need to fix the infinite scrolling issue for this and than uncomment it */}
      {/* <PaginationLoader /> */}

      <PaginationEmptyState>
        <EmptyState title={t('faqs.items.startAdding')} description={t('common.nothingToShow')}>
          <div className="mt-5">
            <Button variant="outline" onClick={() => setIsAddQuestionModalOpen(true)}>
              {t('faqs.items.addTitle')}
            </Button>
          </div>
        </EmptyState>
      </PaginationEmptyState>

      <PaginationError />

      {modalState?.type === 'edit' && (
        <AddModal
          title={t('faqs.items.editTitle')}
          open={true}
          onOpenChange={handleCloseModal}
          className="max-w-[790px]"
        >
          <AddQuestionForm
            categoryId={selectedCategory.id}
            botId={selectedCategory.botId}
            onClose={handleCloseModal}
            faqItemNode={modalState.item}
            selectedLangId={language}
          />
        </AddModal>
      )}

      <DeleteConfirmationModal
        isOpen={modalState?.type === 'delete'}
        onClose={handleCloseModal}
        onConfirm={confirmDelete}
        title={t('faqs.items.confirmDeleteTitle')}
        description={t('faqs.items.deleteConfirmationMessage')}
      />
    </PaginationProvider>
  );
};

export default FaqList;
