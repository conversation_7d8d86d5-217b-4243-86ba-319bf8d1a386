import React, { useMemo, useState } from 'react';
import { FaqItem } from '@/types';
import { ChevronDown } from 'lucide-react';
import { useTranslation } from 'react-i18next';
import { cn } from '@/lib/utils';
import ActionDropdown from '../components/ActionDropdown';
import { RichTextEditor } from '@/modules/rich-text-editor';
import LanguageDisplay from '../components/LanguageDisplay';
import { TooltipWrapper } from '@/components/TooltipWrapper';

interface QuestionCardProps {
  faqItem: FaqItem;
  onEdit: (faqItem: FaqItem) => void;
  onDelete: (faqItem: FaqItem) => void;
  index: number;
}

const QuestionCard: React.FC<QuestionCardProps> = ({ faqItem, onEdit, onDelete, index }) => {
  const [expanded, setExpanded] = useState(false);
  const { t } = useTranslation();

  const toggleOpen = () => {
    setExpanded(expanded => !expanded);
  };

  const [primaryQuestion, otherQuestions] = useMemo(() => {
    if (!faqItem.questions?.length) return [null, []];
    return [faqItem.questions[0], faqItem.questions.slice(1).join(' ● ')];
  }, [faqItem]);

  return (
    <div
      className="bg-background rounded-lg shadow-sm border border-tertiary-200 p-4 gap-3 flex flex-col justify-between items-start"
      data-testid="question-card"
    >
      <div className="flex gap-2 w-full justify-between items-center">
        <TooltipWrapper content={primaryQuestion}>
          <h3 className="text-tertiary-700 truncate overflow-hidden whitespace-nowrap">
            {t('faqs.items.questionPrefix')}
            {index + 1}. {primaryQuestion}
          </h3>
        </TooltipWrapper>

        <LanguageDisplay languages={faqItem.availableLanguages} />
      </div>

      <div className="flex justify-between items-start w-full gap-3">
        <div className="space-y-2 w-0 flex-1">
          <div className="flex gap-2 justify-between pl-4">
            <div
              className={cn(`text-sm text-tertiary-500 text-ellipsis flex-1 w-0`, {
                'truncate overflow-hidden whitespace-nowrap': !expanded,
                'break-words': expanded,
              })}
            >
              {otherQuestions}
            </div>
            {!expanded && !!otherQuestions?.length && (
              // TODO: need to handle otherQuestion count correctly
              <span className="text-sm text-tertiary-500">+{2}</span>
            )}
          </div>

          <div className="text-sm flex text-tertiary-600 opacity-80  border-tertiary-200 !mt-0">
            <p className="self-start relative top-2">{t('faqs.items.answerPrefix')}:</p>
            <RichTextEditor readOnly content={faqItem.answer} />
          </div>
        </div>
        <div className="flex items-center space-x-4">
          <ActionDropdown onEdit={() => onEdit(faqItem)} onDelete={() => onDelete(faqItem)} />
          {!!otherQuestions?.length && (
            <ChevronDown
              onClick={toggleOpen}
              className={cn('text-tertiary-500 w-5 h-5 transition-transform', {
                'rotate-180': expanded,
              })}
              aria-label="Toggle expand question card"
            />
          )}
        </div>
      </div>
    </div>
  );
};

export default QuestionCard;
