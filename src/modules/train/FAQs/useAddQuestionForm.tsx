import { useEffect } from 'react';
import { useForm } from 'react-hook-form';
import { useTranslation } from 'react-i18next';
import { useToast } from '@/hooks/use-toast';
import {
  useCreateFaqTranslationMutation,
  useUpdateFaqTranslationMutation,
  useGetTranslationByFaqIdAndLangIdQuery,
} from '@/store/api';
import { zodResolver } from '@hookform/resolvers/zod';
import { createAddQuestionFormSchema, QuestionFormInputs } from '../schema';
import { FaqItem } from '@/types';
import SuccessToastMessage from '@/components/SuccessToastMessage';

interface UseAddQuestionFormProps {
  onClose: () => void;
  categoryId: string;
  botId: string;
  faqItemNode?: FaqItem | null;
  selectedLangId: string;
}

const mapQuestions = (questions: string[]) => questions.map(q => ({ value: q }));

const getInitialData = (
  faqItemNode: FaqItem | null | undefined,
  existingTranslationData: any,
  currentLangId: string
) => {
  const isSameLang = currentLangId === faqItemNode?.langId;
  return isSameLang ? faqItemNode : existingTranslationData?.data;
};

const getDefaultValues = (selectedLangId: string, flowId?: string) => ({
  questions: [{ value: '' }],
  answer: '',
  langId: selectedLangId ?? '',
  translateTo: '',
  flowId: flowId ?? '',
});

export const useAddQuestionForm = ({
  onClose,
  categoryId,
  botId,
  faqItemNode,
  selectedLangId,
}: UseAddQuestionFormProps) => {
  const { t } = useTranslation();
  const { toast } = useToast();
  const { faqId, flowId } = faqItemNode ?? {};

  const formSchema = createAddQuestionFormSchema(t);

  const form = useForm<QuestionFormInputs>({
    resolver: zodResolver(formSchema),
    defaultValues: getDefaultValues(selectedLangId, flowId),
    mode: 'onSubmit',
    reValidateMode: 'onChange',
  });

  const { watch, reset } = form;
  const currentLangId = watch('langId');

  const { data: existingTranslationData, isLoading: isLoadingTranslation } =
    useGetTranslationByFaqIdAndLangIdQuery(
      {
        faqId: faqId ?? '',
        langId: currentLangId,
      },
      { skip: !faqItemNode?.id || currentLangId === faqItemNode?.langId }
    );

  const initialData = getInitialData(faqItemNode, existingTranslationData, currentLangId);

  useEffect(() => {
    if (initialData) {
      reset({
        questions: mapQuestions(initialData.questions),
        answer: initialData.answer,
        langId: initialData.langId,
        translateTo: '',
        flowId: initialData.flowId ?? '',
      });
    } else {
      reset(getDefaultValues(currentLangId, flowId));
    }
  }, [initialData, currentLangId, flowId, reset]);

  const [createFaqTranslation] = useCreateFaqTranslationMutation();
  const [updateFaqTranslation] = useUpdateFaqTranslationMutation();

  const showSuccessToast = (messageKey: string) => {
    toast({
      title: <SuccessToastMessage message={t(messageKey)} />,
    });
  };

  const executeFaqMutation = async (data: QuestionFormInputs) => {
    const questions = data.questions.map(q => q.value);
    if (initialData) {
      await updateFaqTranslation({
        id: initialData.id,
        flowId: data.flowId || null,
        questions,
        answer: data.answer,
      }).unwrap();
      showSuccessToast('faqs.items.questionsUpdated');
    } else {
      await createFaqTranslation({
        botId,
        categoryId,
        faqId: faqId,
        flowId: data.flowId || null,
        langId: data.langId,
        questions,
        answer: data.answer,
      }).unwrap();
      showSuccessToast('faqs.items.questionsAdded');
    }
  };

  const onSubmit = async (data: QuestionFormInputs) => {
    try {
      await executeFaqMutation(data);
      reset();
      onClose();
    } catch (error: any) {
      console.error(`Failed to ${initialData ? 'update' : 'create'} FAQ item:`, error);
      toast({
        title: t('common.error'),
        description: error,
        variant: 'destructive',
      });
    }
  };

  return {
    form,
    onSubmit,
    isLoadingTranslation,
    initialData,
  };
};
