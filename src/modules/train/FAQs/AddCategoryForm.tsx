import React from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { FloatingField } from '@/components/ui/floating-label';
import { Form, FormControl, FormField, FormItem, FormMessage } from '@/components/ui/form';
import { useToast } from '@/hooks/use-toast';
import SuccessToastMessage from '@/components/SuccessToastMessage';
import { useTranslation } from 'react-i18next';
import { FaqCategory } from '@/types';
import { useUpdateFaqCategoryMutation, useCreateFaqCategoryMutation } from '@/store/api';
import RenderButtons from '@/modules/train/components/RenderButtons';
import { CategoryForm, createAddCategoryFormSchema } from '../schema';

interface AddCategoryFormProps {
  botId: string;
  onClose: (category?: FaqCategory) => void;
  category?: FaqCategory;
}

const AddCategoryForm: React.FC<AddCategoryFormProps> = ({ botId, onClose, category }) => {
  const { t } = useTranslation();
  const { toast } = useToast();
  const [updateFaqCategory] = useUpdateFaqCategoryMutation();
  const [createFaqCategory] = useCreateFaqCategoryMutation();

  const form = useForm<CategoryForm>({
    resolver: zodResolver(createAddCategoryFormSchema(t)),
    defaultValues: {
      name: category?.name || '',
    },
  });

  const onSubmit = async (values: CategoryForm) => {
    try {
      const payload = {
        name: values.name,
      };

      let resultCategory: FaqCategory | undefined;
      let successMessage: string;

      if (category) {
        const result = await updateFaqCategory({ id: category.id, ...payload }).unwrap();
        resultCategory = result.data;
        successMessage = t('faqs.category.categoryUpdated');
      } else {
        const result = await createFaqCategory({ botId, ...payload }).unwrap();
        resultCategory = result.data;
        successMessage = t('faqs.category.categoryAdded');
      }

      toast({
        title: <SuccessToastMessage message={successMessage} />,
      });
      onClose(resultCategory);
    } catch (error: any) {
      console.error('Failed to save FAQ category:', error);
      toast({
        title: t('common.error'),
        description: error,
        variant: 'destructive',
      });
    }
  };

  return (
    <Form {...form}>
      <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
        <FormField
          control={form.control}
          name="name"
          render={({ field }) => (
            <FormItem>
              <FormControl>
                <FloatingField
                  id="categoryName"
                  label={t('faqs.category.nameLabel')}
                  aria-label="Category name"
                  className="col-span-4 rounded-sm"
                  {...field}
                />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />
        <RenderButtons
          handleClose={onClose}
          handleAddClick={form.handleSubmit(onSubmit)}
          isEdit={!!category}
        />
      </form>
    </Form>
  );
};

export default AddCategoryForm;
