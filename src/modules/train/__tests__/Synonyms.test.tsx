import { render, screen } from '@/test/utils';
import SynonymsTab from '../Synonyms';
import { describe, expect, it, vi } from 'vitest';

describe('SynonymsTab', () => {
  it('renders Synonyms component with translated content', () => {
    render(<SynonymsTab />);

    expect(screen.getByText('Synonyms Content')).toBeInTheDocument();
  });

  it('uses translation hook correctly', () => {
    render(<SynonymsTab />);

    const container = screen.getByText('Synonyms Content');
    expect(container.tagName).toBe('DIV');
  });
});
