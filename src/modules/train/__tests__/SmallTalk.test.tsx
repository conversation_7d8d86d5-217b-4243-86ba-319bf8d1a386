import { render, screen } from '@/test/utils';
import SmallTalkTab from '../SmallTalk';
import { describe, expect, it, vi } from 'vitest';

describe('SmallTalkTab', () => {
  it('renders SmallTalk component with translated content', () => {
    render(<SmallTalkTab />);

    expect(screen.getByText('Small Talk Content')).toBeInTheDocument();
  });

  it('uses translation hook correctly', () => {
    render(<SmallTalkTab />);

    const container = screen.getByText('Small Talk Content');
    expect(container.tagName).toBe('DIV');
  });
});
