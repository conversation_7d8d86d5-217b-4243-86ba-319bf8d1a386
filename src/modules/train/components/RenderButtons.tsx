import { Button } from '@/components/ui/button';
import { LoaderCircle } from 'lucide-react';
import React from 'react';
import { useTranslation } from 'react-i18next';
import { useFormContext } from 'react-hook-form';

interface RenderButtonsProps {
  handleClose: () => void;
  handleAddClick?: () => void;
  isEdit?: boolean;
  submitButtonText?: string;
}

function RenderButtons({
  handleClose,
  handleAddClick,
  isEdit,
  submitButtonText,
}: RenderButtonsProps) {
  const { t } = useTranslation();
  const { formState } = useFormContext();
  const isLoading = formState.isSubmitting;
  const disabled = formState.isSubmitting || (!formState.isDirty && !isEdit);

  const buttonLabel = submitButtonText ?? (isEdit ? t('common.update') : t('common.add'));

  return (
    <div className="flex justify-end gap-2 py-4">
      <Button
        type="button"
        className="w-28 h-10 font-normal"
        variantColor="tertiary"
        variant="outline"
        onClick={handleClose}
      >
        {t('common.cancel')}
      </Button>

      <Button
        className="w-28 h-10 font-normal"
        type="submit"
        aria-label="Form Submit Button"
        disabled={disabled}
        onClick={handleAddClick}
      >
        {isLoading && <LoaderCircle className="w-4 h-4 animate-spin" data-testid="loader-circle" />}
        {buttonLabel}
      </Button>
    </div>
  );
}

export default RenderButtons;
