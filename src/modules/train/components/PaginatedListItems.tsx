import React, { ReactNode } from 'react';
import { cn } from '@/lib/utils';

interface PaginatedListItemsProps<T extends { id: string }> {
  items: T[];
  selectedItemId: string | undefined;
  onSelectItem: (item: T) => void;
  renderItemContent: (item: T) => ReactNode;
}

const PaginatedListItems = function <T extends { id: string }>({
  items,
  selectedItemId,
  onSelectItem,
  renderItemContent,
}: PaginatedListItemsProps<T>) {
  return (
    <div className="flex flex-col" data-testid="paginated-list">
      {items.map(item => (
        <button
          key={item.id}
          className={cn(
            'px-3 py-5 rounded-sm cursor-pointer flex justify-between items-center border-b',
            selectedItemId === item.id ? 'bg-primary-100 bg-opacity-20' : 'hover:bg-tertiary-50'
          )}
          onClick={() => onSelectItem(item)}
        >
          {renderItemContent(item)}
        </button>
      ))}
    </div>
  );
};

export default PaginatedListItems;
