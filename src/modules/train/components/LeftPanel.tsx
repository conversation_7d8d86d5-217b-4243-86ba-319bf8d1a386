import React, { ReactNode } from 'react';

interface LeftPanelProps {
  title: string;
  addTrigger?: ReactNode;
  children?: ReactNode;
}

function LeftPanel({ title, addTrigger, children }: LeftPanelProps) {
  return (
    <div
      className="flex flex-col w-80 border-r border-tertiary-200 bg-background"
      data-testid="left-panel"
    >
      <div
        className="flex items-center justify-between px-4 py-3 border-b border-tertiary-200"
        role="banner"
      >
        <h3 className="text-lg">{title}</h3>
        {addTrigger}
      </div>
      <div className="flex-1 overflow-y-auto" data-testid="panel-content">
        {children}
      </div>
    </div>
  );
}

export default LeftPanel;
