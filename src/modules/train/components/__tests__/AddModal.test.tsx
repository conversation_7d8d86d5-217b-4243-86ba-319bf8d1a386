import { render, screen } from '@testing-library/react';
import AddModal from '../AddModal';
import { Button } from '@/components/ui/button';

describe('AddModal', () => {
  const mockOnOpenChange = vi.fn();
  const defaultProps = {
    title: 'Test Modal',
    open: true,
    onOpenChange: mockOnOpenChange,
    children: <div>Modal Content</div>,
  };

  beforeEach(() => {
    mockOnOpenChange.mockClear();
  });

  it('renders the modal with correct title when open', () => {
    render(<AddModal {...defaultProps} />);

    expect(screen.getByText('Test Modal')).toBeInTheDocument();
    expect(screen.getByText('Modal Content')).toBeInTheDocument();
  });

  it('renders trigger element when provided', () => {
    const triggerText = 'Open Modal';
    render(<AddModal {...defaultProps} trigger={<Button>{triggerText}</Button>} />);

    expect(screen.getByText(triggerText)).toBeInTheDocument();
  });

  it('applies custom className when provided', () => {
    const customClass = 'custom-modal';
    render(<AddModal {...defaultProps} className={customClass} />);

    const dialogContent = screen.getByRole('dialog');
    expect(dialogContent).toHaveClass(customClass);
  });

  it('does not render content when closed', () => {
    render(<AddModal {...defaultProps} open={false} />);

    expect(screen.queryByText('Test Modal')).not.toBeInTheDocument();
    expect(screen.queryByText('Modal Content')).not.toBeInTheDocument();
  });
});
