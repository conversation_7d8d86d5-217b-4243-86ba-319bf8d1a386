import { render, screen } from '@testing-library/react';
import LeftPanel from '../LeftPanel';

describe('LeftPanel', () => {
  const defaultProps = {
    title: 'Test Panel',
  };

  it('renders with title', () => {
    render(<LeftPanel {...defaultProps} />);
    expect(screen.getByText('Test Panel')).toBeInTheDocument();
  });

  it('renders with add trigger', () => {
    const addTrigger = <button>Add New</button>;
    render(<LeftPanel {...defaultProps} addTrigger={addTrigger} />);

    expect(screen.getByText('Test Panel')).toBeInTheDocument();
    expect(screen.getByText('Add New')).toBeInTheDocument();
  });

  it('renders children content', () => {
    const childContent = <div data-testid="child-content">Child Content</div>;
    render(<LeftPanel {...defaultProps}>{childContent}</LeftPanel>);

    expect(screen.getByText('Test Panel')).toBeInTheDocument();
    expect(screen.getByTestId('child-content')).toBeInTheDocument();
    expect(screen.getByText('Child Content')).toBeInTheDocument();
  });

  it('renders with all props', () => {
    const addTrigger = <button>Add New</button>;
    const childContent = <div data-testid="child-content">Child Content</div>;

    render(
      <LeftPanel title="Full Panel" addTrigger={addTrigger}>
        {childContent}
      </LeftPanel>
    );

    expect(screen.getByText('Full Panel')).toBeInTheDocument();
    expect(screen.getByText('Add New')).toBeInTheDocument();
    expect(screen.getByTestId('child-content')).toBeInTheDocument();
  });

  it('has correct structural classes', () => {
    render(<LeftPanel {...defaultProps} />);

    const container = screen.getByTestId('left-panel');
    expect(container).toHaveClass(
      'flex',
      'flex-col',
      'w-80',
      'border-r',
      'border-tertiary-200',
      'bg-background'
    );

    const header = screen.getByRole('banner');
    expect(header).toHaveClass(
      'flex',
      'items-center',
      'justify-between',
      'px-4',
      'py-3',
      'border-b',
      'border-tertiary-200'
    );

    const content = screen.getByTestId('panel-content');
    expect(content).toHaveClass('flex-1', 'overflow-y-auto');
  });
});
