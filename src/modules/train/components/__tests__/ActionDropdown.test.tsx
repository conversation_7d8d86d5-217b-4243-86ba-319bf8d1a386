import React from 'react';
import { render, screen, fireEvent } from '@testing-library/react';
import { describe, it, expect, vi } from 'vitest';
import ActionDropdown from '../ActionDropdown';

// Mock UI components
vi.mock('@/components/ui/dropdown-menu', () => ({
  DropdownMenu: ({ children }: { children: React.ReactNode }) => (
    <div data-testid="dropdown-menu">{children}</div>
  ),
  DropdownMenuTrigger: ({ children }: { children: React.ReactNode }) => (
    <button data-testid="dropdown-trigger">{children}</button>
  ),
  DropdownMenuContent: ({ children, align }: { children: React.ReactNode; align: string }) => (
    <div data-testid="dropdown-content" data-align={align}>
      {children}
    </div>
  ),
  DropdownMenuItem: ({
    children,
    onClick,
    disabled,
    className,
  }: {
    children: React.ReactNode;
    onClick?: () => void;
    disabled?: boolean;
    className?: string;
  }) => (
    <button data-testid="dropdown-item" onClick={onClick} disabled={disabled} className={className}>
      {children}
    </button>
  ),
}));

// Mock Lucide icons
vi.mock('lucide-react', () => ({
  MoreVertical: ({ className }: { className?: string }) => (
    <span data-testid="more-vertical-icon" className={className}>
      More
    </span>
  ),
  SquarePen: () => <span data-testid="square-pen-icon">Edit</span>,
  Trash2: () => <span data-testid="trash-icon">Delete</span>,
}));

// Mock translations
vi.mock('react-i18next', () => ({
  useTranslation: () => ({
    t: (key: string) => (key === 'common.edit' ? 'Edit' : 'Delete'),
  }),
}));

describe('ActionDropdown', () => {
  const mockOnEdit = vi.fn();
  const mockOnDelete = vi.fn();

  beforeEach(() => {
    mockOnEdit.mockClear();
    mockOnDelete.mockClear();
  });

  it('renders dropdown trigger with more icon', () => {
    render(<ActionDropdown onEdit={mockOnEdit} onDelete={mockOnDelete} />);

    const trigger = screen.getByTestId('dropdown-trigger');
    const icon = screen.getByTestId('more-vertical-icon');

    expect(trigger).toBeInTheDocument();
    expect(icon).toBeInTheDocument();
    expect(icon).toHaveClass('text-tertiary-600', 'w-5', 'h-5', 'cursor-pointer');
  });

  it('renders dropdown content aligned to end', () => {
    render(<ActionDropdown onEdit={mockOnEdit} onDelete={mockOnDelete} />);

    const content = screen.getByTestId('dropdown-content');
    expect(content).toHaveAttribute('data-align', 'end');
  });

  it('renders edit and delete menu items', () => {
    render(<ActionDropdown onEdit={mockOnEdit} onDelete={mockOnDelete} />);

    const menuItems = screen.getAllByTestId('dropdown-item');
    expect(menuItems).toHaveLength(2);

    // Edit menu item
    expect(menuItems[0]).toHaveTextContent('Edit');
    expect(menuItems[0]).toHaveClass('flex', 'gap-2');
    expect(screen.getByTestId('square-pen-icon')).toBeInTheDocument();

    // Delete menu item
    expect(menuItems[1]).toHaveTextContent('Delete');
    expect(menuItems[1]).toHaveClass('flex', 'gap-2', 'text-error-500');
    expect(screen.getByTestId('trash-icon')).toBeInTheDocument();
  });

  it('calls onEdit when edit menu item is clicked', () => {
    render(<ActionDropdown onEdit={mockOnEdit} onDelete={mockOnDelete} />);

    const menuItems = screen.getAllByTestId('dropdown-item');
    fireEvent.click(menuItems[0]);

    expect(mockOnEdit).toHaveBeenCalledTimes(1);
    expect(mockOnDelete).not.toHaveBeenCalled();
  });

  it('calls onDelete when delete menu item is clicked', () => {
    render(<ActionDropdown onEdit={mockOnEdit} onDelete={mockOnDelete} />);

    const menuItems = screen.getAllByTestId('dropdown-item');
    fireEvent.click(menuItems[1]);

    expect(mockOnDelete).toHaveBeenCalledTimes(1);
    expect(mockOnEdit).not.toHaveBeenCalled();
  });

  it('disables delete menu item when disableDelete is true', () => {
    render(<ActionDropdown onEdit={mockOnEdit} onDelete={mockOnDelete} disableDelete={true} />);

    const menuItems = screen.getAllByTestId('dropdown-item');
    expect(menuItems[1]).toBeDisabled();

    fireEvent.click(menuItems[1]);
    expect(mockOnDelete).not.toHaveBeenCalled();
  });

  it('enables delete menu item by default', () => {
    render(<ActionDropdown onEdit={mockOnEdit} onDelete={mockOnDelete} />);

    const menuItems = screen.getAllByTestId('dropdown-item');
    expect(menuItems[1]).not.toBeDisabled();
  });

  it('maintains proper structure with icons and text', () => {
    render(<ActionDropdown onEdit={mockOnEdit} onDelete={mockOnDelete} />);

    const menuItems = screen.getAllByTestId('dropdown-item');

    // Check edit item structure
    const editItem = menuItems[0];
    expect(editItem.firstChild).toHaveAttribute('data-testid', 'square-pen-icon');
    expect(editItem.textContent).toBe('EditEdit');

    // Check delete item structure
    const deleteItem = menuItems[1];
    expect(deleteItem.firstChild).toHaveAttribute('data-testid', 'trash-icon');
    expect(deleteItem.textContent).toBe('DeleteDelete');
  });
});
