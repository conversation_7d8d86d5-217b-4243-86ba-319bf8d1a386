import { render, screen, fireEvent } from '@testing-library/react';
import { FormProvider, useForm } from 'react-hook-form';
import RenderButtons from '../RenderButtons';
import { vi } from 'vitest';
import { useEffect } from 'react';

// Mock react-i18next
vi.mock('react-i18next', () => ({
  useTranslation: () => ({
    t: (key: string) =>
      ({
        'common.cancel': 'CANCEL',
        'common.add': 'ADD',
        'common.update': 'UPDATE',
      })[key] || key,
  }),
}));

interface WrapperProps {
  children: React.ReactNode;
  isDirty?: boolean;
  isSubmitting?: boolean;
}

const FormWrapper = ({ children, isDirty = false, isSubmitting = false }: WrapperProps) => {
  const form = useForm({
    defaultValues: { test: '' },
  });

  useEffect(() => {
    if (isDirty) {
      form.setValue('test', 'changed', { shouldDirty: true });
    }
  }, [isDirty, form]);

  // Mock the submitting state
  if (isSubmitting) {
    form.formState.isSubmitting = true;
  }

  return <FormProvider {...form}>{children}</FormProvider>;
};

describe('RenderButtons', () => {
  const mockHandleClose = vi.fn();
  const mockHandleAddClick = vi.fn();

  beforeEach(() => {
    mockHandleClose.mockClear();
    mockHandleAddClick.mockClear();
    vi.clearAllMocks();
  });

  it('renders cancel and submit buttons', () => {
    render(
      <FormWrapper>
        <RenderButtons handleClose={mockHandleClose} />
      </FormWrapper>
    );

    expect(screen.getByText('CANCEL')).toBeInTheDocument();
    expect(screen.getByText('ADD')).toBeInTheDocument();
  });

  it('shows update text when isEdit is true', () => {
    render(
      <FormWrapper>
        <RenderButtons handleClose={mockHandleClose} isEdit={true} />
      </FormWrapper>
    );

    expect(screen.getByText('UPDATE')).toBeInTheDocument();
  });

  it('shows custom submit button text when provided', () => {
    render(
      <FormWrapper>
        <RenderButtons handleClose={mockHandleClose} submitButtonText="Custom Submit" />
      </FormWrapper>
    );

    expect(screen.getByText('Custom Submit')).toBeInTheDocument();
  });

  it('calls handleClose when cancel button is clicked', () => {
    render(
      <FormWrapper>
        <RenderButtons handleClose={mockHandleClose} />
      </FormWrapper>
    );

    fireEvent.click(screen.getByText('CANCEL'));
    expect(mockHandleClose).toHaveBeenCalledTimes(1);
  });

  it('calls handleAddClick when submit button is clicked', () => {
    render(
      <FormWrapper isDirty={true}>
        <RenderButtons handleClose={mockHandleClose} handleAddClick={mockHandleAddClick} />
      </FormWrapper>
    );

    const submitButton = screen.getByLabelText('Form Submit Button');
    fireEvent.click(submitButton);
    expect(mockHandleAddClick).toHaveBeenCalledTimes(1);
  });

  it('disables submit button when form is not dirty and not in edit mode', () => {
    render(
      <FormWrapper isDirty={false}>
        <RenderButtons handleClose={mockHandleClose} />
      </FormWrapper>
    );

    expect(screen.getByLabelText('Form Submit Button')).toBeDisabled();
  });

  it('enables submit button when form is dirty', () => {
    render(
      <FormWrapper isDirty={true}>
        <RenderButtons handleClose={mockHandleClose} />
      </FormWrapper>
    );

    expect(screen.getByLabelText('Form Submit Button')).not.toBeDisabled();
  });

  it('enables submit button when in edit mode regardless of dirty state', () => {
    render(
      <FormWrapper isDirty={false}>
        <RenderButtons handleClose={mockHandleClose} isEdit={true} />
      </FormWrapper>
    );

    expect(screen.getByLabelText('Form Submit Button')).not.toBeDisabled();
  });

  it('shows loading state when form is being submitted', async () => {
    const mockSubmit = vi.fn(() => new Promise(resolve => setTimeout(resolve, 100)));

    const { rerender } = render(
      <FormWrapper isDirty={true}>
        <RenderButtons
          handleClose={mockHandleClose}
          handleAddClick={() => {
            mockSubmit();
          }}
        />
      </FormWrapper>
    );

    const submitButton = screen.getByLabelText('Form Submit Button');
    await fireEvent.click(submitButton);

    // Force a rerender with submitting state
    rerender(
      <FormWrapper isDirty={true}>
        <RenderButtons handleClose={mockHandleClose} handleAddClick={mockSubmit} />
      </FormWrapper>
    );

    expect(mockSubmit).toHaveBeenCalled();
  });
});
