import { render, screen } from '@testing-library/react';
import RightPanel from '../RightPanel';

describe('RightPanel', () => {
  const defaultProps = {
    title: 'Test Panel',
  };

  it('renders the title correctly', () => {
    render(<RightPanel {...defaultProps} />);
    expect(screen.getByText('Test Panel')).toBeInTheDocument();
  });

  it('renders children content', () => {
    render(
      <RightPanel {...defaultProps}>
        <div data-testid="test-child">Child Content</div>
      </RightPanel>
    );

    expect(screen.getByText('Test Panel')).toBeInTheDocument();
    expect(screen.getByTestId('test-child')).toBeInTheDocument();
    expect(screen.getByText('Child Content')).toBeInTheDocument();
  });

  it('renders correctly without children', () => {
    render(<RightPanel {...defaultProps} />);

    const container = screen.getByTestId('right-panel');
    expect(container).toBeInTheDocument();
    expect(container.childNodes.length).toBe(1); // Just the title when no children
  });

  it('has correct layout classes', () => {
    render(<RightPanel {...defaultProps} />);

    const container = screen.getByTestId('right-panel');
    expect(container).toHaveClass('flex-1', 'w-0', 'px-5', 'py-6', 'flex', 'flex-col');

    const title = screen.getByRole('heading');
    expect(title).toHaveClass('text-lg', 'mb-4');
  });
});
