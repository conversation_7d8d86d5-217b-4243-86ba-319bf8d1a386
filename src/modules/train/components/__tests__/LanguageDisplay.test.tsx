import { render, screen } from '@testing-library/react';
import LanguageDisplay from '../LanguageDisplay';

describe('LanguageDisplay', () => {
  const mockLanguages = [
    { id: '1', name: 'English' },
    { id: '2', name: 'Spanish' },
    { id: '3', name: 'French' },
    { id: '4', name: 'German' },
    { id: '5', name: 'Very Long Language Name' },
  ];

  it('renders empty container when no languages are provided', () => {
    render(<LanguageDisplay />);
    const container = screen.getByTestId('language-display');
    expect(container.children).toHaveLength(0);
  });

  it('displays up to three languages', () => {
    render(<LanguageDisplay languages={mockLanguages.slice(0, 3)} />);

    expect(screen.getByText('English')).toBeInTheDocument();
    expect(screen.getByText('Spanish')).toBeInTheDocument();
    expect(screen.getByText('French')).toBeInTheDocument();
  });

  it('shows +N indicator for additional languages', () => {
    render(<LanguageDisplay languages={mockLanguages} />);

    // First three languages should be visible
    expect(screen.getByText('English')).toBeInTheDocument();
    expect(screen.getByText('Spanish')).toBeInTheDocument();
    expect(screen.getByText('French')).toBeInTheDocument();

    // Should show +2 for remaining two languages
    expect(screen.getByText('+2')).toBeInTheDocument();
  });

  it('truncates long language names', () => {
    const longLanguages = [{ id: '1', name: 'Very Long Language Name' }];
    render(<LanguageDisplay languages={longLanguages} />);

    // Should be truncated to 10 characters
    expect(screen.getByText('Very Long ...')).toBeInTheDocument();
  });

  it('adds tooltip for truncated language names', () => {
    const longLanguages = [{ id: '1', name: 'Very Long Language Name' }];
    render(<LanguageDisplay languages={longLanguages} />);

    const tooltipTrigger = screen.getByText('Very Long ...');
    expect(tooltipTrigger.closest('[data-state]')).toBeInTheDocument();
  });

  it('shows +N indicator for hidden languages', () => {
    render(<LanguageDisplay languages={mockLanguages} />);

    const plusIndicator = screen.getByText('+2');
    expect(plusIndicator).toBeInTheDocument();
  });
});
