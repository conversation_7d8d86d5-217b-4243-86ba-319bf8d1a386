import { render, screen, fireEvent } from '@testing-library/react';
import PaginatedListItems from '../PaginatedListItems';

interface TestItem {
  id: string;
  name: string;
}

describe('PaginatedListItems', () => {
  const mockItems: TestItem[] = [
    { id: '1', name: 'Item 1' },
    { id: '2', name: 'Item 2' },
    { id: '3', name: 'Item 3' },
  ];

  const mockOnSelectItem = vi.fn();
  const mockRenderItemContent = (item: TestItem) => (
    <span data-testid={`item-${item.id}`}>{item.name}</span>
  );

  const defaultProps = {
    items: mockItems,
    selectedItemId: undefined,
    onSelectItem: mockOnSelectItem,
    renderItemContent: mockRenderItemContent,
  };

  beforeEach(() => {
    mockOnSelectItem.mockClear();
  });

  it('renders all items', () => {
    render(<PaginatedListItems {...defaultProps} />);

    mockItems.forEach(item => {
      expect(screen.getByText(item.name)).toBeInTheDocument();
      expect(screen.getByTestId(`item-${item.id}`)).toBeInTheDocument();
    });
  });

  it('calls onSelectItem when an item is clicked', () => {
    render(<PaginatedListItems {...defaultProps} />);

    const firstItem = screen.getByText('Item 1');
    fireEvent.click(firstItem);

    expect(mockOnSelectItem).toHaveBeenCalledTimes(1);
    expect(mockOnSelectItem).toHaveBeenCalledWith(mockItems[0]);
  });

  it('applies selected style to selected item', () => {
    render(<PaginatedListItems {...defaultProps} selectedItemId="2" />);

    const selectedItem = screen.getByText('Item 2').closest('button');
    const unselectedItem = screen.getByText('Item 1').closest('button');

    expect(selectedItem).toHaveClass('bg-primary-100', 'bg-opacity-20');
    expect(unselectedItem).not.toHaveClass('bg-primary-100', 'bg-opacity-20');
  });

  it('uses custom render function for item content', () => {
    const customRender = (item: TestItem) => (
      <div data-testid={`custom-${item.id}`}>Custom {item.name}</div>
    );

    render(<PaginatedListItems {...defaultProps} renderItemContent={customRender} />);

    mockItems.forEach(item => {
      expect(screen.getByTestId(`custom-${item.id}`)).toBeInTheDocument();
      expect(screen.getByText(`Custom ${item.name}`)).toBeInTheDocument();
    });
  });

  it('renders empty list when no items provided', () => {
    render(<PaginatedListItems {...defaultProps} items={[]} />);
    const container = screen.getByTestId('paginated-list');
    expect(container).toBeEmptyDOMElement();
  });
});
