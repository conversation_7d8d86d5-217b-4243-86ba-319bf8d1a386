import React from 'react';
import { TextSelection } from 'prosemirror-state';
import { useEditor } from '../hooks/useEditor';
import MenuBar from './MenuBar';
import type { RichTextEditorProps } from '../types';
import '../styles/prosemirror.css';
import { File, X } from 'lucide-react';
import { cn } from '@/lib/utils';
import { formatFileSize } from '../utils/filesize.utils';
import { useTranslation } from 'react-i18next';

const RichTextEditor: React.FC<RichTextEditorProps> = ({
  content = '',
  onChange,
  placeholder = 'Write Message..',
  className = '',
  selectedFiles = [],
  onFileRemove,
  onEditorState,
  readOnly = false,
  isToolbar = true,
  onLinkClick,
}) => {
  const [editorRef, editorInstance, editorState] = useEditor({
    content,
    onChange,
    placeholder,
    readOnly,
    onEditorState,
  });
  const handleEditorClick = (e: React.MouseEvent) => {
    if (!editorInstance) return;
    const target = e.target as HTMLElement;
    const link = target.closest('a');

    if (link && onLinkClick) {
      const { view } = editorInstance;
      const pos = view.posAtDOM(link, 0);
      if (pos === null) return;

      let start = pos;
      let end = pos + link.innerText.length;
      const $pos = view.state.doc.resolve(pos);
      const marks = $pos.marks();
      const linkMark = marks.find(mark => mark.type.name === 'link');

      if (linkMark) {
        view.state.doc.nodesBetween(
          $pos.parent.content.size > 0 ? $pos.start() : 0,
          $pos.end(),
          (node, p) => {
            if (node.isText && linkMark.isInSet(node.marks)) {
              start = Math.min(start, p);
              end = Math.max(end, p + node.nodeSize);
            }
          }
        );
      }

      const { dispatch, state } = view;
      dispatch(state.tr.setSelection(TextSelection.create(state.doc, start, end)));
      onLinkClick(link.href, link.innerText);
    }
  };

  const { i18n } = useTranslation();
  const direction = typeof i18n.dir === 'function' ? i18n.dir() : 'ltr';
  return (
    <div
      data-testid="rte-container"
      className={cn(
        'flex flex-col overflow-hidden justify-center items-start',
        !readOnly && 'border bg-background border-tertiary-300 rounded-lg px-2',
        className
      )}
    >
      {/* File Preview only for compose mode */}
      {!readOnly && selectedFiles.length > 0 && (
        <div className="flex flex-wrap gap-2 m-3" data-testid="file-preview-container">
          {selectedFiles.map((file, index) => (
            <div
              key={`file-${file.name}-${file.size}`}
              className="flex justify-between items-center w-64 h-14 py-1 px-3 bg-tertiary-100 rounded"
            >
              {file.type.startsWith('image/') ? (
                <div
                  className="flex justify-start items-center gap-2"
                  data-testid="file-preview-image"
                >
                  <img
                    src={URL.createObjectURL(file)}
                    alt={file.name}
                    className="h-8 w-8 object-cover rounded border border-tertiary-400"
                  />
                  <div className="flex flex-col gap-1">
                    <span data-testid="file-name" className="text-sm truncate max-w-32">
                      {file.name}
                    </span>
                    <span data-testid="file-size" className="text-xs text-tertiary-500">
                      {formatFileSize(file.size)}
                    </span>
                  </div>
                </div>
              ) : (
                <div className="flex items-center gap-1" data-testid="file-preview-doc">
                  <span className="w-8 h-8 flex items-center justify-center text-sm rounded">
                    <File className="w-7 h-7" />
                  </span>
                  <div className="flex flex-col gap-1">
                    <span data-testid="file-name" className="text-sm truncate max-w-32">
                      {file.name}
                    </span>
                    <span data-testid="file-size" className="text-xs text-tertiary-500">
                      {formatFileSize(file.size)}
                    </span>
                  </div>
                </div>
              )}

              {onFileRemove && (
                <button
                  type="button"
                  className="text-tertiary-400 cursor-pointer"
                  onClick={() => onFileRemove(index)}
                  data-testid="remove-file-button"
                >
                  <X className="w-6 h-6" />
                </button>
              )}
            </div>
          ))}
        </div>
      )}

      {/* Toolbar */}
      {!readOnly && editorState && editorInstance && isToolbar && (
        <MenuBar editorState={editorState} editorView={editorInstance.view} />
      )}

      {/* ProseMirror Editor Container */}
      <div
        ref={editorRef}
        data-testid="editor-content"
        className={cn(
          'text-sm z-0 py-1 overflow-auto prose prose-sm w-full no-scrollbar my-1',
          readOnly ? 'bg-transparent w-auto h-auto px-1 py-0 m-0' : 'max-h-28 h-full bg-white'
        )}
        style={{ direction }}
        onClick={handleEditorClick}
      />
      <div className="ProseMirror" data-testid="prosemirror-editor" />
    </div>
  );
};

export default RichTextEditor;
