import React from 'react';
import { render, screen, fireEvent } from '@testing-library/react';
import { describe, it, expect, vi } from 'vitest';
import MenuItem from '../MenuItem';
import type { Command } from 'prosemirror-state';

describe('MenuItem', () => {
  const mockCommand = vi.fn() as unknown as Command;
  const mockOnExecute = vi.fn();
  const defaultProps = {
    title: 'Test Button',
    command: mockCommand,
    onExecute: mockOnExecute,
    'data-testid': 'test-button',
  };

  beforeEach(() => {
    mockOnExecute.mockClear();
  });

  it('renders button with correct title and content', () => {
    render(
      <MenuItem {...defaultProps}>
        <span>Test Content</span>
      </MenuItem>
    );

    const button = screen.getByRole('button');
    expect(button).toHaveAttribute('title', 'Test Button');
    expect(button).toHaveAttribute('type', 'button');
    expect(button).toHaveTextContent('Test Content');
  });

  it('applies default inactive styles', () => {
    render(
      <MenuItem {...defaultProps}>
        <span>Test Content</span>
      </MenuItem>
    );

    const button = screen.getByRole('button');
    expect(button).toHaveClass(
      'px-2',
      'py-1',
      'rounded',
      'text-sm',
      'font-medium',
      'transition-colors',
      'text-tertiary-700',
      'hover:bg-tertiary-200'
    );
    expect(button).not.toHaveClass('bg-primary-100');
  });

  it('applies active styles when isActive is true', () => {
    render(
      <MenuItem {...defaultProps} isActive={true}>
        <span>Test Content</span>
      </MenuItem>
    );

    const button = screen.getByRole('button');
    expect(button).toHaveClass('bg-primary-100');
    expect(button).not.toHaveClass('hover:bg-tertiary-200');
  });

  it('executes command when clicked', () => {
    render(
      <MenuItem {...defaultProps}>
        <span>Test Content</span>
      </MenuItem>
    );

    const button = screen.getByRole('button');
    fireEvent.click(button);

    expect(mockOnExecute).toHaveBeenCalledTimes(1);
    expect(mockOnExecute).toHaveBeenCalledWith(mockCommand);
  });

  it('applies data-testid correctly', () => {
    render(
      <MenuItem {...defaultProps}>
        <span>Test Content</span>
      </MenuItem>
    );

    expect(screen.getByTestId('test-button')).toBeInTheDocument();
  });

  it('renders children correctly', () => {
    render(
      <MenuItem {...defaultProps}>
        <div data-testid="test-child" className="test-class">
          Nested Content
        </div>
      </MenuItem>
    );

    const child = screen.getByTestId('test-child');
    expect(child).toBeInTheDocument();
    expect(child).toHaveClass('test-class');
    expect(child).toHaveTextContent('Nested Content');
  });

  it('has disabled styles in className', () => {
    render(
      <MenuItem {...defaultProps}>
        <span>Test Content</span>
      </MenuItem>
    );

    const button = screen.getByRole('button');
    expect(button.className).toContain('disabled:opacity-50');
    expect(button.className).toContain('disabled:cursor-not-allowed');
  });

  it('supports complex children with multiple elements', () => {
    render(
      <MenuItem {...defaultProps}>
        <span className="icon">📝</span>
        <span className="text">Edit</span>
      </MenuItem>
    );

    const button = screen.getByRole('button');
    expect(button).toHaveTextContent('📝Edit');
    expect(button.querySelector('.icon')).toBeInTheDocument();
    expect(button.querySelector('.text')).toBeInTheDocument();
  });
});
