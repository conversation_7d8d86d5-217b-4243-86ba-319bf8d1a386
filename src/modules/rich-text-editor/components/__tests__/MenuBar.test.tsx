import React from 'react';
import { render, screen, fireEvent } from '@testing-library/react';
import { describe, it, expect, vi } from 'vitest';
import { EditorState } from 'prosemirror-state';
import { EditorView } from 'prosemirror-view';
import MenuBar from '../MenuBar';
import { commands, isMarkActive } from '../../utils/commands';
import { schema } from '../../utils/schema';

// Mock emoji-picker-react
vi.mock('emoji-picker-react', () => ({
  default: ({ onEmojiClick }: { onEmojiClick: (data: { emoji: string }) => void }) => (
    <div data-testid="emoji-picker">
      <button onClick={() => onEmojiClick({ emoji: '😀' })}>Select Emoji</button>
    </div>
  ),
}));

// Mock Lucide icon
vi.mock('lucide-react', () => ({
  Smile: () => <div data-testid="smile-icon">😊</div>,
}));

// Mock translation
vi.mock('react-i18next', () => ({
  useTranslation: () => ({
    t: (key: string) => key, // Return the key as the translation
  }),
}));

// Mock commands
vi.mock('../../utils/commands', () => ({
  commands: {
    toggleBold: vi.fn(() => true),
    toggleItalic: vi.fn(() => true),
    toggleUnderline: vi.fn(() => true),
    toggleStrike: vi.fn(() => true),
    toggleHighlight: vi.fn(() => true),
    insertEmoji: vi.fn((_emoji: string) => () => true),
  },
  isMarkActive: vi.fn(() => false),
  toggleSubscript: vi.fn(() => true),
  toggleSuperscript: vi.fn(() => true),
}));

describe('MenuBar', () => {
  const mockDispatch = vi.fn();
  const mockFocus = vi.fn();

  const mockEditorView = {
    dispatch: mockDispatch,
    focus: mockFocus,
  } as unknown as EditorView;

  const mockEditorState = {
    schema,
  } as unknown as EditorState;

  beforeEach(() => {
    vi.clearAllMocks();
  });

  it('renders all menu items', () => {
    render(<MenuBar editorState={mockEditorState} editorView={mockEditorView} />);

    // Check for basic formatting options
    expect(screen.getByTestId('bold')).toBeInTheDocument();
    expect(screen.getByTestId('italic')).toBeInTheDocument();
    expect(screen.getByTestId('underline')).toBeInTheDocument();
    expect(screen.getByTestId('strike-through')).toBeInTheDocument();
    expect(screen.getByTestId('highlight')).toBeInTheDocument();
    expect(screen.getByTestId('superscript')).toBeInTheDocument();
    expect(screen.getByTestId('subscript')).toBeInTheDocument();
    expect(screen.getByTestId('emoji')).toBeInTheDocument();
  });

  it('renders separators between groups', () => {
    render(<MenuBar editorState={mockEditorState} editorView={mockEditorView} />);

    const separators = Array.from(document.getElementsByClassName('w-px'));
    expect(separators.length).toBeGreaterThan(0);
    separators.forEach(separator => {
      expect(separator).toHaveClass('w-px', 'h-6', 'bg-tertiary-300', 'mx-1');
    });
  });

  it('executes commands when menu items are clicked', () => {
    render(<MenuBar editorState={mockEditorState} editorView={mockEditorView} />);

    // Test bold button
    fireEvent.click(screen.getByTestId('bold'));
    expect(commands.toggleBold).toHaveBeenCalledWith(mockEditorState, mockDispatch);
    expect(mockFocus).toHaveBeenCalled();

    // Test italic button
    fireEvent.click(screen.getByTestId('italic'));
    expect(commands.toggleItalic).toHaveBeenCalledWith(mockEditorState, mockDispatch);
  });

  it('shows emoji picker when emoji button is clicked', () => {
    render(<MenuBar editorState={mockEditorState} editorView={mockEditorView} />);

    // Initially emoji picker should not be visible
    expect(screen.queryByTestId('emoji-picker')).not.toBeInTheDocument();

    // Click emoji button
    fireEvent.click(screen.getByTestId('emoji'));
    expect(screen.getByTestId('emoji-picker')).toBeInTheDocument();
  });

  it('inserts emoji and closes picker when emoji is selected', () => {
    render(<MenuBar editorState={mockEditorState} editorView={mockEditorView} />);

    // Open emoji picker
    fireEvent.click(screen.getByTestId('emoji'));

    // Select an emoji
    fireEvent.click(screen.getByText('Select Emoji'));

    // Check if emoji was inserted and picker was closed
    expect(commands.insertEmoji).toHaveBeenCalledWith('😀');
    expect(screen.queryByTestId('emoji-picker')).not.toBeInTheDocument();
    expect(mockFocus).toHaveBeenCalled();
  });

  it('toggles active states based on marks', () => {
    // Mock isMarkActive to return true for bold
    (isMarkActive as ReturnType<typeof vi.fn>).mockImplementation(
      (_state: EditorState, mark: { name: string }) => mark === schema.marks.strong
    );

    render(<MenuBar editorState={mockEditorState} editorView={mockEditorView} />);

    // Bold button should have bg-primary-100 class
    const boldButton = screen.getByTestId('bold');
    expect(boldButton).toHaveClass('bg-primary-100');

    // Other buttons should not have bg-primary-100 class
    const italicButton = screen.getByTestId('italic');
    expect(italicButton).not.toHaveClass('bg-primary-100');
    expect(italicButton).toHaveClass('hover:bg-tertiary-200');
  });

  it('focuses editor after command execution', () => {
    render(<MenuBar editorState={mockEditorState} editorView={mockEditorView} />);

    // Click multiple formatting buttons
    fireEvent.click(screen.getByTestId('bold'));
    expect(mockFocus).toHaveBeenCalledTimes(1);

    fireEvent.click(screen.getByTestId('italic'));
    expect(mockFocus).toHaveBeenCalledTimes(2);
  });

  it('maintains emoji picker state correctly', () => {
    render(<MenuBar editorState={mockEditorState} editorView={mockEditorView} />);

    // Open emoji picker
    fireEvent.click(screen.getByTestId('emoji'));
    expect(screen.getByTestId('emoji-picker')).toBeInTheDocument();

    // Close emoji picker by clicking emoji button again
    fireEvent.click(screen.getByTestId('emoji'));
    expect(screen.queryByTestId('emoji-picker')).not.toBeInTheDocument();
  });
});
