import { Plugin } from 'prosemirror-state';
import { Decoration, DecorationSet } from 'prosemirror-view';

export function placeholderPlugin(text: string) {
  return new Plugin({
    props: {
      decorations(state) {
        const doc = state.doc;
        const isEmpty =
          doc.childCount === 1 &&
          doc.firstChild?.type.name === 'paragraph' &&
          doc.firstChild.content.size === 0;

        if (!isEmpty) return null;

        const placeholder = document.createElement('span');
        placeholder.className = 'text-tertiary-400 pointer-events-none absolute';
        placeholder.textContent = text;

        return DecorationSet.create(doc, [Decoration.widget(1, placeholder, { side: -1 })]);
      },
    },
  });
}
