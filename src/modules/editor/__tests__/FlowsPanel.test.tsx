import { render, screen, fireEvent } from '@/test/utils';
import FlowsPanel from '../widget/flowsPanel';
import { describe, expect, it, vi } from 'vitest';
import * as flowApi from '@/store/api/flowApi';
import userEvent from '@testing-library/user-event';

const mockFlows = [
  {
    id: '1',
    name: 'Welcome',
    type: 'Default',
    appId: 'bot1',
  },
  {
    id: '2',
    name: 'Fallback',
    type: 'Default',
    appId: 'bot1',
  },
];

// Mock useRouterParam
vi.mock('@/hooks/useRouterParam', () => ({
  useBotIdParam: () => ({ botId: 'test-bot-id' }),
}));

vi.mock('@/store/api/flowApi', async () => {
  const actual = await vi.importActual<typeof flowApi>('@/store/api/flowApi');
  return {
    ...actual,
    useGetFlowsQuery: vi.fn(() => ({
      data: {
        data: {
          items: mockFlows,
          pagination: {
            page: 1,
            limit: 10,
            total: 2,
            totalPages: 1,
            hasNext: false,
            hasPrevious: false,
          },
        },
      },
      isLoading: false,
      error: null,
      refetch: vi.fn(),
    })),
    useCreateFlowMutation: () => [vi.fn().mockResolvedValue({ data: {} }), { isLoading: false }],
    useDeleteFlowMutation: () => [vi.fn().mockResolvedValue({ data: {} }), { isLoading: false }],
    useUpdateFlowMutation: () => [vi.fn().mockResolvedValue({ data: {} }), { isLoading: false }],
    useCloneFlowMutation: () => [vi.fn().mockResolvedValue({ data: {} }), { isLoading: false }],
  };
});

describe('FlowsPanel', () => {
  it('renders default flows', () => {
    render(<FlowsPanel />);
    expect(screen.getByText('Welcome')).toBeInTheDocument();
    expect(screen.getByText('Fallback')).toBeInTheDocument();
    expect(screen.getByText('Flows')).toBeInTheDocument();
  });

  it('sets active flow on click', () => {
    render(<FlowsPanel />);
    const fallback = screen.getByText('Fallback');
    fireEvent.click(fallback);
    const clickableDiv = fallback.parentElement?.parentElement?.parentElement;
    expect(clickableDiv?.className).toContain('bg-tertiary');
  });

  it('renders add flow button', () => {
    render(<FlowsPanel />);
    const plusBtn = screen.getByTestId('add-flow-btn');
    expect(plusBtn).toBeInTheDocument();
  });

  it('renders more options buttons for flows', () => {
    render(<FlowsPanel />);
    const moreBtns = screen.getAllByTestId('more-options-btn');
    expect(moreBtns).toHaveLength(2); // Welcome and Fallback flows
  });

  it('shows flow types correctly', () => {
    render(<FlowsPanel />);
    const defaultTags = screen.getAllByText('Default');
    expect(defaultTags).toHaveLength(2); // Both flows are default type
  });

  it('renders search button', () => {
    render(<FlowsPanel />);
    const searchBtn = screen.getByTestId('search-btn');
    expect(searchBtn).toBeInTheDocument();
  });

  it('renders collapse button', () => {
    render(<FlowsPanel />);
    const collapseBtn = screen.getByRole('button', { name: /collapse-panel/i });
    expect(collapseBtn).toBeInTheDocument();
  });

  it('shows and hides search input', async () => {
    render(<FlowsPanel />);
    const searchBtn = screen.getByTestId('search-btn');
    await userEvent.click(searchBtn);
    expect(screen.getByPlaceholderText('Search flows...')).toBeInTheDocument();
  });

  it('handles add flow button click', () => {
    render(<FlowsPanel />);
    const plusBtn = screen.getByTestId('add-flow-btn');
    fireEvent.click(plusBtn);
    // Test passes if no errors are thrown
    expect(plusBtn).toBeInTheDocument();
  });
});
