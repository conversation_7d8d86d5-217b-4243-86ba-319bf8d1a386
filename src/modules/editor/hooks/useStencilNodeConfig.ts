import { useMemo } from 'react';
import { getModuleText, getType, nodeKeys, tempEnableNodes } from '@/modules/editor/utils/config';
import { StencilTabsType } from '@/modules/editor/utils/constants';
import { StencilConfig } from '../types';

const enabledPlugins: Record<string, string> = tempEnableNodes; //Need to get it from api

const useNodeConfig = () => {
  const { enabledNodes, comingSoonNodes } = useMemo(() => {
    const enabled: Record<StencilTabsType, StencilConfig[]> = {};
    const comingSoon: Record<StencilTabsType, StencilConfig[]> = {};

    nodeKeys.forEach(type => {
      const category = getType(type);
      const isEnabled = enabledPlugins[type] || false;

      if (isEnabled) {
        if (!enabled[category]) enabled[category] = [];
        enabled[category].push({ type });
      } else {
        if (!comingSoon[category]) comingSoon[category] = [];
        comingSoon[category].push({ type });
      }
    });

    return { enabledNodes: enabled, comingSoonNodes: comingSoon };
  }, [enabledPlugins]);

  const getNodes = (currentTab: string, search: string) => {
    if (!search) return enabledNodes[currentTab] || [];
    return Object.values(enabledNodes)
      .flat()
      .filter(({ type }) => getModuleText(type).toLowerCase().includes(search.toLowerCase()));
  };

  const getComingSoonNodes = (currentTab: string, search: string) => {
    if (!search) return comingSoonNodes[currentTab] || [];
    return Object.values(comingSoonNodes)
      .flat()
      .filter(({ type }) => getModuleText(type).toLowerCase().includes(search.toLowerCase()));
  };

  return { enabledNodes, comingSoonNodes, getNodes, getComingSoonNodes };
};

export default useNodeConfig;
