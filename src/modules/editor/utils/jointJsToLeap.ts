import { v4 as uuidv4 } from 'uuid';
import type { dia } from 'rappid';
import type { ModuleData, LeapModules, JointJSCell } from '../types';
import { getJsonConstant } from './jsonConstant';
import { getModuleText } from './config';
import { ExtendedStencilNodes, jointJsEditorTheme } from './constants';

const getCustomCode = (id: string): string => {
  return `// Custom code panel
// main function is the default method executed after processing current module
function main(){
  return "${id}";// return end moduleId
}`;
};

export const joinToLeapJSON = (
  jointJson: { cells: JointJSCell[] },
  settingDetails: Record<string, ModuleData>,
  graph: dia.Graph
): { modules: LeapModules; startId: string | null } => {
  const modules: LeapModules = {};
  const linkNodes: JointJSCell[] = [];
  const idMapping: Record<string, string> = {};
  const choiceNodeUtils = {
    choiceNodeId: null as string | null,
    choicePositionDetails: {} as Record<string, { x: number; y: number }>,
  };
  const choiceNodesMap: string[] = [];
  let startId: string | null = null;

  jointJson.cells.forEach(node => {
    if (node.type === 'standard.Link') {
      linkNodes.push(node);
    } else {
      const result = processNode(node, settingDetails, idMapping);
      if (!result) return;

      const { uuid, leapJson } = result;

      if (node.type === ExtendedStencilNodes.APP_START) {
        startId = uuid;
      }

      if (node.type === ExtendedStencilNodes.CHOICE_OPTION) {
        choiceNodeUtils.choicePositionDetails[uuid] = { ...node.position };
        return;
      }

      if (node.type === ExtendedStencilNodes.CHOICE) {
        choiceNodeUtils.choiceNodeId = uuid;
        choiceNodesMap.push(uuid);
        leapJson.process.match_conditions = leapJson.process.match_conditions.map((d: any) => ({
          ...d,
          moduleId: null,
        }));
      }

      leapJson.coordinates = {
        ...node.position,
        nodeData: {
          title: getModuleText(node.type),
          name: node.type,
          id: uuid,
          isEditable: true,
          canDelete: false,
          status: '',
          moduleType: node.type,
        },
      };

      modules[uuid] = { ...leapJson };
    }
  });

  linkNodes.forEach(link => handleLink(link, modules, idMapping, graph));

  choiceNodesMap.forEach(choiceId => {
    modules[choiceId].process.match_conditions = modules[choiceId].process.match_conditions.map(
      (match: { id: string }) => ({
        ...match,
        coordinates: choiceNodeUtils.choicePositionDetails[match.id] ?? { x: null, y: null },
      })
    );
  });

  sanitizeModules(modules);

  return { modules, startId };
};

function processNode(
  node: JointJSCell,
  settingDetails: Record<string, ModuleData>,
  idMapping: Record<string, string>
): { uuid: string; leapJson: LeapModules[string] } | null {
  const { id, type } = node;
  const uuid = id.length === 7 ? id : uuidv4().slice(0, 7);
  idMapping[id] = uuid;

  if (type === ExtendedStencilNodes.CHOICE_OPTION) {
    return { uuid, leapJson: {} as LeapModules[string] };
  }

  let leapJson = getJsonConstant(type);
  const nodeSetting = settingDetails[id];

  if (nodeSetting) {
    const clonedSetting = JSON.parse(JSON.stringify(nodeSetting));
    if (clonedSetting.output?.customCodeIds) {
      clonedSetting.output.customCodeIds.conditionalLink = [];
    }
    leapJson = { ...leapJson, ...clonedSetting };
  }

  return { uuid, leapJson };
}

function handleLink(
  link: JointJSCell,
  modules: LeapModules,
  idMapping: Record<string, string>,
  graph: dia.Graph
) {
  const sourceId = link.source?.id;
  const targetId = link.target?.id;
  if (!sourceId || !targetId) return;

  const sourceModule = modules[idMapping[sourceId]];
  const targetModule = modules[idMapping[targetId]];

  const sourceType = sourceModule?.type || 'choiceOption';
  const targetType = targetModule?.type || 'choiceOption';

  if (targetType === 'choice' && sourceModule) {
    sourceModule.isChoiceLinked = true;
  }

  if (targetType === 'script' && sourceModule) {
    sourceModule.isScriptLinked = true;
  }

  if (sourceType === 'choiceOption') {
    linkChoiceOption(sourceId, targetId, modules);
    return;
  }

  if (sourceType === 'choice') {
    handleChoiceNoMatch(sourceId, targetId, link, modules, idMapping, graph);
    return;
  }

  // Regular connections (custom code)
  if (idMapping[targetId] && sourceModule) {
    sourceModule.output ??= {};
    sourceModule.output.customCodeIds ??= { conditionalLink: [] };

    if (!Array.isArray(sourceModule.output.customCodeIds.conditionalLink)) {
      sourceModule.output.customCodeIds.conditionalLink = [];
    }

    sourceModule.output.customCodeIds.conditionalLink.push(idMapping[targetId]);
    sourceModule.output.customCode = getCustomCode(idMapping[targetId]);
  }
}

function linkChoiceOption(sourceId: string, targetId: string, modules: LeapModules) {
  const choiceNodeId = Object.keys(modules).find(nodeId => {
    const matchIndex = (modules[nodeId]?.process.match_conditions || []).findIndex(
      ({ id }: { id: string }) => id === sourceId
    );
    return modules[nodeId].type === 'choice' && matchIndex !== -1;
  });

  if (!choiceNodeId) return;

  const matchIndex = modules[choiceNodeId].process.match_conditions.findIndex(
    ({ id }: { id: string }) => id === sourceId
  );

  if (matchIndex !== -1) {
    modules[choiceNodeId].process.match_conditions[matchIndex].moduleId = targetId;
  }
}

function handleChoiceNoMatch(
  sourceId: string,
  targetId: string,
  link: JointJSCell,
  modules: LeapModules,
  idMapping: Record<string, string>,
  graph: dia.Graph
) {
  const source = graph.getCell(sourceId);
  const portId = link.source?.port;

  const portDetails = source?.attributes?.ports?.items?.find((p: any) => p.id === portId);
  const fill = portDetails?.attrs?.portBody?.fill;

  if (fill === jointJsEditorTheme.errorColor) {
    modules[idMapping[sourceId]].process.no_match_module_id = targetId;
  }
}

function sanitizeModules(modules: LeapModules) {
  const nodeKeys = Object.keys(modules);

  nodeKeys.forEach(k => {
    const module = modules[k];
    if (module.type !== 'choice') return;

    const { no_match_module_id, match_conditions } = module.process;

    if (no_match_module_id && !nodeKeys.includes(no_match_module_id)) {
      module.process.no_match_module_id = null;
    }

    match_conditions.forEach((cond: { moduleId: string }, i: number) => {
      if (cond.moduleId && !nodeKeys.includes(cond.moduleId)) {
        module.process.match_conditions[i].moduleId = null;
      }
    });
  });
}
