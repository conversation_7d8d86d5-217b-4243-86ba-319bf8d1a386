import { v4 as uuidv4 } from 'uuid';
import type { ModuleData, LeapModules, JointJSData, ChoiceDetails, NodePort } from '../types';
import { getNodeConstant, linksConstant } from './jsonConstant';
import { jointJsEditorTheme } from './constants';

export const leapToJointJSON = (modules: LeapModules): JointJSData => {
  if (!modules || Object.keys(modules).length === 0) {
    return {
      nodes: { cells: [] },
      jsonData: {},
    };
  }

  const modulesKeys = Object.keys(modules);
  const cells: any[] = [];
  const jsonData: Record<string, ModuleData> = {};

  modulesKeys.forEach((id, index) => {
    const module = modules[id];
    const { type, coordinates, output = {}, settings, process } = module;
    const { x, y } = coordinates;

    const cell = getNodeConstant(
      { type, name: settings.nodeName, image: settings?.image },
      { x, y },
      id,
      index + 1
    );

    jsonData[id] = { output, settings, process, coordinates, type };

    if (type === 'choice') {
      handleChoiceNode({ id, x, y, process, cells, cell });
    }

    const customCodeIds = JSON.parse(JSON.stringify(output?.customCodeIds ?? {}));
    const conditionalLink = customCodeIds?.conditionalLink ?? [];

    if (conditionalLink.length) {
      handleConditionalLinks({ id, cell, conditionalLink, cells });
    }

    cells.push(cell);
  });

  assignTargetPorts(cells);

  return {
    nodes: { cells },
    jsonData,
  };
};

function handleChoiceNode({ id, x, y, process, cells, cell }: any) {
  const { match_conditions, no_match_module_id } = process;

  const portId = uuidv4().slice(0, 7);
  const portId1 = uuidv4().slice(0, 7);
  const portInId = uuidv4().slice(0, 7);

  cell.ports = {
    ...cell.ports,
    items: [
      {
        id: portId,
        group: 'out',
        args: { x: 12 },
        attrs: {
          portBody: {
            fill: jointJsEditorTheme.successColor,
            stroke: jointJsEditorTheme.successColor,
          },
        },
      } as NodePort,
      {
        id: portId1,
        group: 'out',
        args: { x: 40 },
        attrs: {
          portBody: {
            fill: jointJsEditorTheme.errorColor,
            stroke: jointJsEditorTheme.errorColor,
          },
        },
      } as NodePort,
      {
        group: 'in',
        id: portInId,
      } as NodePort,
    ],
  };

  match_conditions.forEach((matchCondition: any, index: number) => {
    const { id: optionId, ...rest } = matchCondition;
    if (!rest.key) return;

    const prev = match_conditions[index - 1] ?? {};
    const maxLen = Math.max(prev.condition?.length || 0, prev.value?.length || 0);
    const prevNodeX = prev.coordinates?.x ?? x;
    const nodeX = index === 0 ? x + 150 * (index + 1) : prevNodeX + maxLen * 9 + 80;
    const positionDetails = rest.coordinates?.x ? rest.coordinates : { x: nodeX, y: y + 100 };

    const choiceNodeCell = getNodeConstant(
      { type: 'choiceOption' },
      positionDetails,
      optionId,
      index + 1,
      rest as ChoiceDetails
    );

    const choiceOptionPortId = uuidv4().slice(0, 7);

    choiceNodeCell.ports = {
      ...choiceNodeCell.ports,
      items: [{ group: 'out', id: choiceOptionPortId }],
    };

    cells.push(choiceNodeCell);

    cells.push({
      ...linksConstant,
      id: uuidv4().slice(0, 7),
      source: { ...linksConstant.source, id, port: portId },
      target: { ...linksConstant.target, id: optionId },
    });

    if (rest.moduleId) {
      cells.push({
        ...linksConstant,
        id: uuidv4().slice(0, 7),
        source: { ...linksConstant.source, id: optionId, port: choiceOptionPortId },
        target: { ...linksConstant.target, id: rest.moduleId },
      });
    }
  });

  if (no_match_module_id) {
    cells.push({
      ...linksConstant,
      id: uuidv4().slice(0, 7),
      source: { ...linksConstant.source, id, port: portId1 },
      target: { ...linksConstant.target, id: no_match_module_id },
    });
  }
}

function handleConditionalLinks({
  id,
  cell,
  conditionalLink,
  cells,
}: {
  id: string;
  cell: any;
  conditionalLink: string[];
  cells: any[];
}) {
  const portId = uuidv4().slice(0, 7);
  cell.ports = {
    ...cell.ports,
    items: [
      { group: 'out', id: portId },
      { group: 'in', id: uuidv4().slice(0, 7) },
    ],
  };

  const links = conditionalLink.map(targetId => ({
    ...linksConstant,
    id: uuidv4().slice(0, 7),
    source: { ...linksConstant.source, id, port: portId },
    target: { ...linksConstant.target, id: targetId },
  }));

  cells.push(...links);
}

function assignTargetPorts(cells: any[]) {
  cells.forEach((cellData: any, index: number) => {
    if (cellData.type !== 'standard.Link') return;

    const { target } = cellData;
    const targetIndex = cells.findIndex(({ id: nodeId }) => nodeId === target.id);

    if (targetIndex === -1) return;

    const targetCell = cells[targetIndex];
    const portIn = targetCell?.ports?.items?.find(({ group }: { group: string }) => group === 'in');

    const portInId = portIn?.id ?? uuidv4().slice(0, 7);

    cells[index].target = {
      ...cells[index].target,
      port: portInId,
    };

    targetCell.ports.items = targetCell.ports.items.filter(
      ({ group }: { group: string }) => group !== 'in'
    );

    targetCell.ports.items.push({
      group: 'in',
      id: portInId,
    });
  });
}
