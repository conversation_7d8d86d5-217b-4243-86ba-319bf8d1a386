import { dia } from 'rappid';
import { StencilState, Position } from '../types';

export const alignStencil = (
  setState: React.Dispatch<React.SetStateAction<StencilState>>,
  position: Position,
  isFullscreen: boolean
) => {
  const canvasElement = document.getElementsByClassName('canvas')[0];
  if (!canvasElement) return;

  const { width, height } = canvasElement.getBoundingClientRect();
  const rightLimit = width - 280;
  const leftLimit = 10;

  const definedY = isFullscreen ? -(height + 300) : -height;
  const newPosition = { ...position, y: definedY };

  if (position.x > rightLimit) {
    newPosition.x = rightLimit;
  } else if (position.x < leftLimit) {
    newPosition.x = leftLimit;
  }

  setState(prev => ({ ...prev, position: newPosition }));

  const isMovedToRight = width / 2 < position.x;
  setTimeout(() => {
    setState(prev => ({
      ...prev,
      floatPosition: isMovedToRight ? 'right' : 'left',
    }));
  }, 100);
};

export const canDrag = (cellView: any, graph: dia.Graph) => {
  const {
    model: {
      attributes: { type },
    },
  } = cellView;

  if (type === 'appStart' || type === 'appEnd') {
    const isNodePresentAlready =
      graph.getCells().findIndex(({ attributes: { type: nodeType } }) => nodeType === type) !== -1;
    return !isNodePresentAlready;
  }
  return true;
};
