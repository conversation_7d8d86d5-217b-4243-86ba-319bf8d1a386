import { dia, linkTools, elementTools } from 'rappid';

if (!document.getElementById('modal-root')) {
  const modalRoot = document.createElement('div');
  modalRoot.id = 'modal-root';
  document.body.appendChild(modalRoot);
}

const removeButton = new linkTools.Remove();

export const customLinkTools = new dia.ToolsView({
  tools: [removeButton],
});

const removeElement = new elementTools.Remove({
  focusOpacity: 0.5,
  rotate: true,
  // top-mid
  x: 45,
  y: 8,
  offset: { x: 0, y: 0 },
  action(evt, elementView, toolView) {
    if (
      elementView.model.attributes.type === 'choiceOption' ||
      elementView.model.attributes.type === 'choice'
    ) {
      const childs = elementView.model.graph.getSuccessors(elementView.model);

      childs.forEach(child => {
        if (child.attributes.type !== 'appEnd') {
          child.remove();
        }
      });
    }
    elementView.model.remove({ ui: true, tool: toolView.cid });
  },
});

export const customElementTools = new dia.ToolsView({
  tools: [removeElement],
});

export const getElementTools = (type: string, width: number) => {
  let x = 45;

  const xMapping: { [key: string]: { x: number } } = {
    choice: {
      x: 100,
    },
  };

  if (xMapping[type]) {
    x = xMapping[type].x;
  }

  if (type === 'choiceOption') {
    x = width;
  }

  const removeElement = new elementTools.Remove({
    focusOpacity: 0.5,
    rotate: true,
    // top-mid
    x,
    y: 8,
    offset: { x: 0, y: 0 },
    action(evt, elementView, toolView) {
      if (elementView.model.attributes.type === 'choiceOption') return;
      if (
        elementView.model.attributes.type === 'choiceOption' ||
        elementView.model.attributes.type === 'choice'
      ) {
        const childs = elementView.model.graph.getSuccessors(elementView.model);

        childs.forEach(child => {
          if (child.attributes.type !== 'appEnd') {
            child.remove();
          }
        });
      }
      elementView.model.remove({ ui: true, tool: toolView.cid });
    },
  });

  return new dia.ToolsView({
    tools: [removeElement],
  });
};
