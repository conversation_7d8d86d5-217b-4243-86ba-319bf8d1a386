import { dia } from 'rappid';
import React, { useCallback, useEffect, useRef, useState } from 'react';

import Stencil from './widget/Stencil/index';

import { useGetApplicationDetailsQuery } from '@/store/api/studioApi';
import { useEditor } from './hooks/useEditor';
import { joinToLeapJSON } from './utils/jointJsToLeap';
import { leapToJointJSON } from './utils/leapToJointJs';
import { useUndoRedo } from '@/modules/editor/hooks/useUndoRedo';
import useNodeHandler from '@/modules/editor/hooks/useEditorNodeHandler';
import NodeConfigSheet from './NodeConfigSheet';
import { useFormHandler } from './hooks/useFormHandler';
import { ApplicationData, ApplicationErrorDetails, ErrorModalId, SettingDetails } from './types';
import { useTranslation } from 'react-i18next';
import { useToast } from '@/hooks/use-toast';
import { Loader } from '@/components/Loader';

interface IProps {
  id: string;
  jsonDetails?: ApplicationData;
  readOnly?: boolean;
}

const NeuraTalkEditor = ({ id, jsonDetails, readOnly = false }: IProps) => {
  const { t } = useTranslation();
  const { toast } = useToast();

  const [settingDetails, setSettingDetails] = useState<SettingDetails>({});
  const [isEditorLoading, setEditorLoading] = useState(false);
  const [error, setError] = useState<ApplicationErrorDetails | null>(null);
  const canvasWithPallette = useRef<HTMLDivElement>(null);
  const isEdit = !readOnly;

  const { data: applicationData, isFetching: isApplicationDataLoading } =
    useGetApplicationDetailsQuery({ appId: id }, { skip: !id || !!jsonDetails });

  const changeJointJsonToLeapOnDrop = useCallback(
    (graph: dia.Graph, updateData = null) => {
      //TODO: add Proper Type
      if (graph) {
        const data = joinToLeapJSON(
          JSON.parse(JSON.stringify(graph.toJSON())),
          updateData ?? settingDetails,
          graph
        ).modules;

        const { nodes, jsonData } = leapToJointJSON(data);

        graph.fromJSON(nodes);

        setSettingDetails(prev => {
          return {
            ...prev,
            ...jsonData,
          };
        });
      }
    },
    [settingDetails, setSettingDetails]
  );

  const {
    canvas,
    graphInstance,
    paperInstance,
    initializeEditor,
    loadApplicationData,
    setAppDetails,
    autoUpdateHandler,
    modalTypeDetails,
    setModalTypeDetails,
    scrollInstance,
    currentElementView,
    setCurrentElementView,
  } = useEditor({
    canvasWithPallette,
    isEdit,
    changeJointJsonToLeapOnDrop,
    id,
    settingDetails,
    setSettingDetails,
    setEditorLoading,
  });

  const { handleFormClose } = useFormHandler({
    setModalTypeDetails,
    settingDetails,
    scrollInstance,
    graphInstance,
    currentElementView,
    setCurrentElementView,
  });

  const { updateInitialState } = useUndoRedo();

  const { handleNodeAdd, isMaxNodeReached, handleSettingsUpdate } = useNodeHandler({
    graphInstance,
    changeJointJsonToLeapOnDrop,
    autoUpdateHandler,
    settingDetails,
    setError,
    setSettingDetails,
    modalTypeDetails,
  });

  useEffect(() => {
    const cleanup = initializeEditor();
    return () => {
      initialUpdateRef.current = '';
      cleanup?.();
    };
  }, []);

  useEffect(() => {
    return () => {
      initialUpdateRef.current = '';
    };
  }, [id]);

  const initialUpdateRef = useRef('');
  const showLoader = (isEditorLoading || isApplicationDataLoading) && !initialUpdateRef.current;

  useEffect(() => {
    if (!graphInstance || !paperInstance || initialUpdateRef.current) return;

    const setEditorData = (editorData: ApplicationData) => {
      setEditorLoading(true);
      initialUpdateRef.current = id;
      updateInitialState({
        payload: editorData,
        action: 'initial',
      });
      setAppDetails(editorData);
      loadApplicationData(editorData);
    };

    if (jsonDetails) setEditorData(jsonDetails);
    else if (applicationData) setEditorData(applicationData);
  }, [applicationData, graphInstance, paperInstance, jsonDetails]);

  useEffect(() => {
    switch (error?.modalId) {
      case 'settings':
        break;
      case ErrorModalId.MAX_NODE:
        toast({ description: t('editor.maxNodeError'), variant: 'destructive' });
        break;
      default:
        break;
    }
  }, [error]);

  const isStencilEnabled = isEdit && paperInstance && graphInstance;

  return (
    <div className="studio-root">
      <div
        className="canvas-wrapper min-h-screen full-screen"
        ref={canvasWithPallette}
        style={{ position: 'relative', height: 'calc(100% - 80px)' }}
      >
        <div className="canvas" ref={canvas}>
          <div id="react-portal-modal-container" />
        </div>

        {showLoader && <Loader className="fixed top-0" />}
        {isStencilEnabled && (
          <div>
            <Stencil
              paper={paperInstance}
              graph={graphInstance}
              handleNodeAdd={handleNodeAdd}
              isMaxNodeReached={isMaxNodeReached}
            />
          </div>
        )}

        {isEdit && (
          <NodeConfigSheet
            handleClose={handleFormClose}
            modalTypeDetails={modalTypeDetails}
            settingDetails={settingDetails}
            handleSave={handleSettingsUpdate}
            isEdit={isEdit}
          />
        )}
      </div>
    </div>
  );
};

export default NeuraTalkEditor;
