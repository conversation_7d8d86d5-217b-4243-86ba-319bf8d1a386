import React, { useState, useEffect, KeyboardEvent } from 'react';
import {
  Plus,
  Search,
  ChevronsLeft,
  ChevronsRight,
  MoreVertical,
  X,
  Copy,
  FileClock,
  Trash,
} from 'lucide-react';
import { FlowNode, FlowType, OrderDirection, ModalState } from '@/types';
import { useToast } from '@/hooks/use-toast';
import { cn } from '@/lib/utils';
import {
  DropdownMenu,
  DropdownMenuTrigger,
  DropdownMenuContent,
  DropdownMenuItem,
} from '@/components/ui/dropdown-menu';
import { useTranslation } from 'react-i18next';
import {
  useCreateFlowMutation,
  useDeleteFlowMutation,
  useCloneFlowMutation,
  useGetFlowsQuery,
  useUpdateFlowMutation,
} from '@/store/api/flowApi';
import { useBotIdParam } from '@/hooks/useRouterParam';
import {
  PaginationError,
  PaginationLoader,
  PaginationProvider,
  PaginationRenderItems,
  PaginationSearch,
  usePagination,
} from '@/components/Pagination';
import { useTabPersistence } from '@/hooks/useTabPersistence';
import FlowVersionModal from '@/components/FlowVersionModal';
import DeleteConfirmationModal from '@/components/DeleteConfirmationModal';

interface FlowsPanelProps {
  onFlowSelect?: (flow: FlowNode) => void;
}

interface FlowItemProps {
  flow: FlowNode;
  activeFlowId: string;
  editingFlowId: string | null;
  editingName: string;
  onSelect: (flow: FlowNode) => void;
  onEdit: (flow: FlowNode) => void;
  onChangeName: (name: string) => void;
  onSaveEdit: () => void;
  onCancelEdit: () => void;
  onDuplicate: (flowId: string) => void;
  onOpenVersionHistory: (appId: string) => void;
  onConfirmDelete: (flow: FlowNode) => void;
}

const FlowItem: React.FC<FlowItemProps> = ({
  flow,
  activeFlowId,
  editingFlowId,
  editingName,
  onSelect,
  onEdit,
  onChangeName,
  onSaveEdit,
  onCancelEdit,
  onDuplicate,
  onOpenVersionHistory,
  onConfirmDelete,
}) => {
  const { t } = useTranslation();

  const isEditing = editingFlowId === flow.id;
  const isActive = activeFlowId === flow.id;

  return (
    <div
      className={cn(
        'flex items-center my-0.5 justify-between rounded-md transition-colors cursor-pointer',
        isActive ? 'bg-tertiary/10' : 'hover:bg-secondary-50',
        isEditing ? 'p-0' : 'px-1 h-11'
      )}
      onClick={() => onSelect(flow)}
      onDoubleClick={() => onEdit(flow)}
    >
      <div className="flex-1 relative">
        {isEditing ? (
          <>
            <input
              value={editingName}
              onChange={e => onChangeName(e.target.value)}
              className="font-medium text-secondary-900 text-sm border border-primary-500 rounded pl-2 pr-9 py-3 w-full"
              onKeyDown={(e: KeyboardEvent<HTMLInputElement>) => {
                if (e.key === 'Enter') onSaveEdit();
                if (e.key === 'Escape') onCancelEdit();
              }}
              onBlur={onSaveEdit}
              autoFocus
            />
            {editingName && (
              <button
                type="button"
                className="absolute right-2 top-1/2 -translate-y-1/2 text-secondary-400 hover:text-secondary-600"
                onMouseDown={e => {
                  e.preventDefault();
                  onCancelEdit();
                }}
              >
                <X className="pr-1 w-7 h-7" />
              </button>
            )}
          </>
        ) : (
          <div className="flex justify-start gap-2 items-center">
            <div
              className={cn(
                'text-xs text-tertiary-600 truncate w-auto max-w-24',
                isActive ? 'font-medium' : 'opacity-50'
              )}
              title={flow.name}
            >
              {flow.name}
            </div>
            {flow.type === FlowType.DEFAULT && (
              <div className="text-xs bg-primary-400/10 px-2 py-1 rounded-md text-primary-400">
                {flow.type}
              </div>
            )}
          </div>
        )}
      </div>
      {!isEditing && (
        <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <button data-testid="more-options-btn">
              <MoreVertical className="w-4 h-4 text-tertiary-600" />
            </button>
          </DropdownMenuTrigger>
          <DropdownMenuContent align="start" className="-top-20 w-48 z-50">
            <DropdownMenuItem
              onClick={() => onDuplicate(flow.id)}
              className="flex items-center gap-2"
              disabled={flow.type === FlowType.DEFAULT}
            >
              <Copy className="w-4 h-4" />
              <span>{t('common.duplicate')}</span>
            </DropdownMenuItem>
            <DropdownMenuItem
              className="flex items-center gap-2"
              onClick={() => onOpenVersionHistory(flow.appId)}
            >
              <FileClock className="w-4 h-4" />
              <span>{t('common.versionHistory')}</span>
            </DropdownMenuItem>
            <DropdownMenuItem
              aria-label="delete-flow-btn"
              onClick={() => onConfirmDelete(flow)}
              className={cn(
                'flex items-center gap-2',
                flow.type === FlowType.DEFAULT
                  ? 'text-error-200 cursor-not-allowed'
                  : 'text-error-400 hover:bg-error-50'
              )}
              disabled={flow.type === FlowType.DEFAULT}
            >
              <Trash className="w-4 h-4" />
              <span>{t('common.delete')}</span>
            </DropdownMenuItem>
          </DropdownMenuContent>
        </DropdownMenu>
      )}
    </div>
  );
};

const FlowsPanel: React.FC<FlowsPanelProps> = ({ onFlowSelect }) => {
  const { t } = useTranslation();
  const { toast } = useToast();
  const { botId } = useBotIdParam();
  const [flowVersionHistoryId, setFlowVersionHistoryId] = useState<string | null>(null);

  const pagination = usePagination({
    useQueryHook: query =>
      useGetFlowsQuery(
        {
          botId,
          ...query,
          order: [['updatedAt', OrderDirection.DESC]],
        },
        { skip: !botId }
      ),
  });

  const [createFlow] = useCreateFlowMutation();
  const [deleteFlow] = useDeleteFlowMutation();
  const [cloneFlow] = useCloneFlowMutation();
  const [updateFlow] = useUpdateFlowMutation();

  const {
    queryState: { data: flowsData, isFetching },
  } = pagination;

  const flows = flowsData?.data?.items ?? [];

  const [activeFlowId, setActiveFlowId] = useTabPersistence({
    defaultTab: flows[0]?.id ?? '',
    queryParamName: 'flowId',
  });
  const [editingFlowId, setEditingFlowId] = useState<string | null>(null);
  const [editingName, setEditingName] = useState('');
  const [isSearchActive, setIsSearchActive] = useState(false);
  const [isCollapsed, setIsCollapsed] = useState(false);
  const [modalState, setModalState] = useState<ModalState<FlowNode> | null>(null);

  useEffect(() => {
    if (flows.length > 0 && activeFlowId) {
      const selectedFlow = flows.find(flow => flow.id === activeFlowId);
      if (selectedFlow) {
        onFlowSelect?.(selectedFlow);
      } else {
        // If the flowId in the URL doesn't exist, default to the first flow
        setActiveFlowId(flows[0].id);
        onFlowSelect?.(flows[0]);
      }
    } else if (flows.length > 0 && !activeFlowId && !isFetching) {
      // If no flowId in URL and no activeFlowId, set to first flow
      setActiveFlowId(flows[0].id);
      onFlowSelect?.(flows[0]);
    }
  }, [flows, activeFlowId, isFetching, onFlowSelect]);

  const handleEditFlow = (flow: FlowNode) => {
    const { id, name, type } = flow;
    if (type === FlowType.CUSTOM) {
      setEditingFlowId(id);
      setEditingName(name);
    }
  };

  const handleSaveEdit = async () => {
    if (!editingFlowId || !botId) return;
    const flow = flows.find(f => f.id === editingFlowId);

    if (!flow) return;

    const updatedName = editingName.trim() || flow.name;
    try {
      await updateFlow({
        appId: botId,
        id: editingFlowId, //TODO: change id to flowId to match with optimised tag
        payload: { name: updatedName, metadata: {} },
      }).unwrap();
      setEditingFlowId(null);
      setEditingName('');
      toast({ title: t('flows.flowRenamed'), variant: 'default' });
    } catch (err: any) {
      toast({ title: t('flows.flowNotRenamed'), description: err, variant: 'destructive' });
      console.error('Update flow failed:', err);
    }
  };

  const handleDeleteFlow = async (flowId: string, appId: string, type?: FlowType) => {
    if (type === FlowType.DEFAULT || !botId) return;

    try {
      await deleteFlow({ appId, id: flowId }).unwrap();
      if (activeFlowId === flowId) {
        setActiveFlowId('');
      }

      toast({ title: t('flows.flowDeleted'), variant: 'default' });
    } catch (err) {
      toast({ title: t('flows.flowNotDeleted'), variant: 'destructive' });
      console.error('Failed to delete flow:', err);
    }
  };

  const handleDuplicateFlow = async (flowId: string) => {
    try {
      await cloneFlow({ flowId }).unwrap();
      toast({ title: t('flows.flowDuplicated'), variant: 'default' });
    } catch (err: any) {
      console.error('Failed to duplicate flow:', err);
      toast({
        title: t('common.error'),
        description: err,
        variant: 'destructive',
      });
    }
  };

  const handleCreateFlow = async () => {
    if (!botId) return;

    const payload = {
      botId,
    };

    try {
      const res = await createFlow(payload).unwrap();
      setActiveFlowId(res?.data?.id ?? '');
      onFlowSelect?.(res.data!);
      toast({ title: t('flows.flowCreated'), variant: 'default' });
    } catch (err) {
      toast({ title: t('flows.flowNotCreated'), variant: 'destructive' });
      console.error('Failed to create flow:', err);
    }
  };

  const handleSelectFlow = (flow: FlowNode) => {
    setActiveFlowId(flow.id);
    onFlowSelect?.(flow);
  };

  const confirmDelete = async () => {
    if (modalState?.type !== 'delete') return;

    const { id, appId, type } = modalState.item;

    try {
      await handleDeleteFlow(id, appId, type);
      toast({ title: t('flows.flowDeleted'), variant: 'default' });
    } catch (err: any) {
      toast({ title: t('flows.flowNotDeleted'), description: err, variant: 'destructive' });
    } finally {
      handleCloseModal();
    }
  };

  const handleCloseModal = () => {
    setModalState(null);
  };

  const flowHeaderWithSearch = isSearchActive ? (
    <div className="relative">
      <PaginationSearch
        placeholder={t('common.searchFlows')}
        className="w-full text-sm text-secondary-900 border border-secondary-300 rounded-md px-3 py-2 pr-10"
        debounceMs={300}
        onClear={() => setIsSearchActive(false)}
        closeButton
        autoFocus
      />
    </div>
  ) : (
    <div className="flex justify-between items-center">
      <h3 className="font-medium text-tertiary-600 text-sm">{t('common.flows')}</h3>
      <div className="flex items-center space-x-2 p-1">
        <button
          onClick={handleCreateFlow}
          data-testid="add-flow-btn"
          className="w-6 flex justify-center items-center h-6 bg-primary-500 rounded-md"
        >
          <Plus className="w-4 h-4 text-white" />
        </button>
        <button onClick={() => setIsSearchActive(true)} data-testid="search-btn">
          <Search className="w-4 h-4 text-secondary-400 hover:text-secondary-600" />
        </button>
        <button onClick={() => setIsCollapsed(true)} aria-label="collapse-panel">
          <ChevronsLeft className="w-4 h-4 text-secondary-400 hover:text-secondary-600" />
        </button>
      </div>
    </div>
  );

  return (
    <div
      className={cn(
        'absolute left-4 top-3 max-h-[60vh] bg-white rounded-lg shadow-floating flex flex-col overflow-hidden z-10 transition-all duration-300',
        isCollapsed ? 'w-22' : 'w-52'
      )}
    >
      <PaginationProvider value={pagination}>
        {/* Header */}
        <div className="p-4 border-b border-secondary-200">
          {isCollapsed ? (
            <div className="flex justify-between items-center">
              <h3 className="font-medium text-tertiary-600 text-sm">{t('common.flows')}</h3>
              <button onClick={() => setIsCollapsed(false)} aria-label="expand-panel">
                <ChevronsRight className="w-4 h-4 text-secondary-400 hover:text-secondary-600" />
              </button>
            </div>
          ) : (
            flowHeaderWithSearch
          )}
        </div>

        {/* Flow List */}
        {!isCollapsed && (
          <div className="overflow-y-auto p-2 flex-1">
            {!flows.length ? (
              <div className="col-span-full text-center text-muted-foreground">
                {t('flows.noFlows')}
              </div>
            ) : (
              <PaginationRenderItems<FlowNode>
                renderItems={items =>
                  items.map(flow => (
                    <FlowItem
                      key={flow.id}
                      flow={flow}
                      activeFlowId={activeFlowId}
                      editingFlowId={editingFlowId}
                      editingName={editingName}
                      onSelect={handleSelectFlow}
                      onEdit={handleEditFlow}
                      onChangeName={setEditingName}
                      onSaveEdit={handleSaveEdit}
                      onCancelEdit={() => setEditingFlowId(null)}
                      onDuplicate={handleDuplicateFlow}
                      onOpenVersionHistory={setFlowVersionHistoryId}
                      onConfirmDelete={flow => setModalState({ type: 'delete', item: flow })}
                    />
                  ))
                }
              />
            )}
            <PaginationLoader className="p-0" />
            <PaginationError />
          </div>
        )}
      </PaginationProvider>

      {flowVersionHistoryId && (
        <FlowVersionModal
          isOpen={!!flowVersionHistoryId}
          onClose={() => setFlowVersionHistoryId(null)}
          appId={flowVersionHistoryId}
        />
      )}
      <DeleteConfirmationModal
        isOpen={modalState?.type === 'delete'}
        onClose={handleCloseModal}
        onConfirm={confirmDelete}
        title={t('flows.confirmDeleteTitle')}
        description={
          modalState?.item?.name
            ? `${t('flows.deleteConfirmationMessage')} "${modalState.item.name}"?`
            : t('flows.deleteConfirmationMessage')
        }
      />
    </div>
  );
};

export default FlowsPanel;
