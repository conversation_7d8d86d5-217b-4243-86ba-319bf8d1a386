import React, { useState } from 'react';
import { Search, X, ChevronsRight, ChevronsLeft } from 'lucide-react';
import { cn } from '@/lib/utils';
import { useTranslation } from 'react-i18next';
import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';

interface StencilHeaderProps {
  handleSearch: (event: React.ChangeEvent<HTMLInputElement>) => void;
  isCollapsed: boolean;
  toggleCollapse: () => void;
}

const StencilHeader: React.FC<StencilHeaderProps> = ({
  handleSearch,
  isCollapsed,
  toggleCollapse,
}) => {
  const { t } = useTranslation();
  const [toggle, setToggle] = useState(false);
  const [searchQuery, setSearchQuery] = useState('');

  const handleClearSearch = () => {
    setSearchQuery('');
    setToggle(false);
    handleSearch({
      target: { value: '' },
    } as React.ChangeEvent<HTMLInputElement>);
  };

  return (
    <div
      className={cn(
        'overflow-hidden w-full opacity-100 h-auto',
        isCollapsed ? 'border-none' : 'border-b'
      )}
    >
      <div
        className={cn(
          'relative flex items-center w-full justify-between px-5 rounded-sm min-h-[65px] py-2'
        )}
      >
        {!toggle ? (
          <>
            <h3 className={cn(' text-secondary-600 text-sm')}>{t('stencil.nodes')}</h3>
            <div className="flex items-center space-x-4">
              {!isCollapsed && (
                <button
                  className="w-6 h-6 text-secondary-400 hover:text-secondary-600 flex items-center justify-center"
                  onClick={() => setToggle(true)}
                >
                  <Search className="w-4 h-4" />
                </button>
              )}
              <button
                className="w-6 h-6 text-secondary-400 hover:text-secondary-600 flex items-center justify-center"
                onClick={toggleCollapse}
              >
                {isCollapsed ? (
                  <ChevronsLeft className="w-4 h-4" />
                ) : (
                  <ChevronsRight className="w-4 h-4" />
                )}
              </button>
            </div>
          </>
        ) : (
          <div className="absolute inset-0 flex items-center justify-center px-4 py-4 z-10 rounded-sm">
            <div className="relative w-full max-w-md">
              <Input
                type="text"
                value={searchQuery}
                onChange={e => {
                  setSearchQuery(e.target.value);
                  handleSearch(e);
                }}
                placeholder={t('stencil.searchNodes')}
                className="w-full text-sm text-secondary-900 border border-secondary-300 rounded px-3 py-2 focus:outline-none focus:ring-1 focus:ring-primary-600"
                autoFocus
              />
              <Button
                variant="ghost"
                variantColor="primary"
                onClick={handleClearSearch}
                className="absolute right-2 top-1/2 px-0 py-0 transform -translate-y-1/2 text-secondary-400 hover:text-secondary-600"
              >
                <X className="w-4 h-4" />
              </Button>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default StencilHeader;
