export interface NodeBase {
  settings: {
    timeout?: number;
    title: string;
    nodeName: string;
    image?: string;
  };
  input: Record<string, any>;
  process: Record<string, any>;
  output: {
    codeModuleMapping: Array<{
      code: string;
      moduleId: string;
    }>;
    conditions: Record<
      string,
      {
        statement: Array<{ expr: string[] }>;
        fallbackcode: string;
        isActive: boolean;
      }
    >;
    fallbackcode: string;
    codeActive: boolean;
    customCode: string;
    customCodeIds: {
      conditionalLink: string[];
    };
  };
  coordinates: {
    x: number;
    y: number;
    nodeData: {
      title: string;
      name: string;
      id: string;
      isEditable: boolean;
      canDelete: boolean;
      status: string;
      moduleType: string;
    };
  };
  type: string;
  typeId: string;
}

interface NodeData {
  title: string;
  name: string;
  id: string;
  isEditable: boolean;
  canDelete: boolean;
  status: string;
  moduleType: string;
}

interface Coordinates {
  x: number;
  y: number;
  nodeData: NodeData;
}

interface OutputCondition {
  statement: Array<{ expr: [string, string, string] }>;
  fallbackcode: string;
  isActive: boolean;
}

interface CodeModuleMapping {
  code: string;
  moduleId: string;
}

interface Output {
  codeModuleMapping?: CodeModuleMapping[];
  conditions?: Record<string, OutputCondition>;
  fallbackcode?: string;
  codeActive?: boolean;
  customCode?: string;
  customCodeIds?: { conditionalLink: string[] };
}

// Generic interface for nodes to allow flexible settings and process types
interface Node<TSettings = any, TProcess = any, TOutput = Output> {
  settings: TSettings;
  process: TProcess;
  output: TOutput;
  input: Record<string, any>;
  type: string;
  typeId: string;
  coordinates: Coordinates;
  isChoiceLinked?: boolean; // Optional for choice node
}

// Specific node settings and process types
interface AppStartSettings {
  aparty: string;
  nodeName: string;
}

interface AppStartProcess {
  cronjob: string;
  params: string[];
  trigger: string;
}

interface AppEndSettings {
  nodeName: string;
}

interface AppEndProcess {
  success: { code: string[]; message: string };
  customErrors: Array<{ code: string[]; message: string }>;
  defaultError: { code: string; message: string };
}

interface WhatsappSettings {
  nodeName: string;
}

interface WhatsappProcess {
  senderId: string;
  receiverNumber: string;
  message: string;
}

interface AgentTransferSettings {
  nodeName: string;
}

interface AgentTransferProcess {
  agentId: string;
  queueId: string;
  transferReason: string;
}

interface HttpSettings {
  timeout: number;
  title: string;
  nodeName: string;
  image: string;
}

interface HttpProcess {
  URL: string;
  requestType: string;
  headers: Array<{ headerKey: string; headerValue: string }>;
  requestBody: string;
  responseCache: string;
  callReference: string;
  responseType: string;
  Value: string;
}

interface ChoiceSettings {
  nodeName: string;
}

interface ChoiceProcess {
  match_conditions: Array<{
    key: string;
    condition: string;
    value: string;
    moduleId: string | null;
    coordinates: { x: number | null; y: number | null };
    id: string;
  }>;
  no_match_module_id: string | null;
}

interface FlowConnectorSettings {
  nodeName: string;
}

interface FlowConnectorProcess {
  targetFlowId: string;
}

interface LanguageSettings {
  nodeName: string;
}

interface LanguageProcess {
  languageCode: string;
}

interface PaymentSettings {
  nodeName: string;
}

interface PaymentProcess {
  amount: string;
  currency: string;
  paymentMethod: string;
  merchantId: string;
}

interface ScriptSettings {
  nodeName: string;
}

interface ScriptProcess {
  code: string;
  connectedNodes: string[];
}

interface WaitDelaySettings {
  nodeName: string;
}

interface WaitDelayProcess {
  delaySeconds: string;
}

interface MessageSettings {
  nodeName: string;
}

interface MessageProcess {
  messageContent: string;
  channel: string;
}

interface InteractiveMessageSettings {
  nodeName: string;
}

interface InteractiveMessageProcess {
  messageContent: string;
  channel: string;
  interactiveType: string;
  options: string[];
}

interface FeedbackSettings {
  nodeName: string;
}

interface FeedbackProcess {
  question: string;
  responseType: string;
  options: string[];
}

interface NotificationSettings {
  nodeName: string;
}

interface NotificationProcess {
  notificationContent: string;
  channel: string;
  priority: string;
}

// Export specific node types for use in leapJsonConstant
export type AppStartNode = Node<AppStartSettings, AppStartProcess>;
export type AppEndNode = Node<AppEndSettings, AppEndProcess, { conditions: Record<string, never> }>;
export type WhatsappNode = Node<WhatsappSettings, WhatsappProcess>;
export type AgentTransferNode = Node<AgentTransferSettings, AgentTransferProcess>;
export type HttpNode = Node<HttpSettings, HttpProcess>;
export type ChoiceNode = Node<ChoiceSettings, ChoiceProcess, Record<string, never>>;
export type FlowConnectorNode = Node<FlowConnectorSettings, FlowConnectorProcess>;
export type LanguageNode = Node<LanguageSettings, LanguageProcess>;
export type PaymentNode = Node<PaymentSettings, PaymentProcess>;
export type ScriptNode = Node<ScriptSettings, ScriptProcess>;
export type WaitDelayNode = Node<WaitDelaySettings, WaitDelayProcess>;
export type MessageNode = Node<MessageSettings, MessageProcess>;
export type InteractiveMessageNode = Node<InteractiveMessageSettings, InteractiveMessageProcess>;
export type FeedbackNode = Node<FeedbackSettings, FeedbackProcess>;
export type NotificationNode = Node<NotificationSettings, NotificationProcess>;

export interface ChoiceDetails {
  condition: string;
  value: string;
}

// Type for node ports
export interface PortGroup {
  position: {
    name: string;
    args: Record<string, number>;
  };
  attrs: {
    portBody: {
      magnet: boolean;
      r: number;
      fill: string;
      stroke: string;
      x?: number;
      y?: number;
    };
  };
  label: {
    position: {
      name: string;
      args: Record<string, number>;
    };
    markup: Array<{ tagName: string; selector: string; className?: string }>;
  };
  markup: Array<{ tagName: string; selector: string }>;
}
