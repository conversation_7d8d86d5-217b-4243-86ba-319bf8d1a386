import React from 'react';
import Default from './PluginForms/Default';
import BaseModal from './PluginForms/BaseModal';
import AppStart from './PluginForms/appStart';
import FlowConnector from './PluginForms/flowConnector';
import Form from './PluginForms/form';
import Message from './PluginForms/message';
import { StencilNodes, StencilNodesType } from './utils/constants';
import AgentTransfer from './PluginForms/agentTransfer';
import Choice from './PluginForms/choice';
import HTTP from './PluginForms/http';
import FeedbackNode from './PluginForms/feedbackForm';
import Notification from './PluginForms/notification';
import { Sheet, SheetContent } from '@/components/ui/sheet';
import { ModalTypeDetails } from '@/types';
import { SettingDetails } from './types';

interface NodeConfigSheetProps {
  modalTypeDetails: ModalTypeDetails | null;
  handleClose: () => void;
  handleSave: (data: any, id: string, validate?: boolean) => void;
  settingDetails: SettingDetails;
  isEdit?: boolean;
  isPublishedEnabled?: boolean;
}

// Map of node types to their corresponding components
const nodeComponentMap: Record<StencilNodesType, React.ComponentType | null> = {
  [StencilNodes.APP_START]: AppStart,
  [StencilNodes.FLOW_CONNECTOR]: FlowConnector,
  [StencilNodes.FORM]: Form,
  [StencilNodes.MESSAGE]: Message,
  [StencilNodes.AGENT_TRANSFER]: AgentTransfer,
  [StencilNodes.CHOICE]: Choice,
  [StencilNodes.HTTP]: HTTP,
  [StencilNodes.FEEDBACK]: FeedbackNode,
  [StencilNodes.NOTIFICATION]: Notification,

  // Add other components as they become available
  [StencilNodes.APP_END]: null,
  [StencilNodes.WHATSAPP]: Default,
  [StencilNodes.LANGUAGE]: Default,
  [StencilNodes.PAYMENT]: Default,
  [StencilNodes.SCRIPT]: Default,
  [StencilNodes.WAIT_DELAY]: Default,
  [StencilNodes.INTERACTIVE_MESSAGE]: Default,
};

const NodeConfigSheet: React.FC<NodeConfigSheetProps> = ({
  handleClose,
  handleSave,
  settingDetails,
  isEdit = false,
  isPublishedEnabled = false,
  modalTypeDetails,
}) => {
  const { type } = modalTypeDetails || {};
  if (type && !nodeComponentMap[type]) return null;

  return (
    <Sheet open={!!type} onOpenChange={handleClose}>
      <SheetContent hideOverlay side="left" className="w-[408px] p-0 flex flex-col">
        {modalTypeDetails && settingDetails[modalTypeDetails.id] && (
          <NodeConfigSheetContent
            handleClose={handleClose}
            handleSave={handleSave}
            moduleData={settingDetails[modalTypeDetails.id]}
            isEdit={isEdit}
            isPublishedEnabled={isPublishedEnabled}
            modalTypeDetails={modalTypeDetails}
          />
        )}
      </SheetContent>
    </Sheet>
  );
};

export default NodeConfigSheet;

type NodeConfigSheetContentProps = Omit<
  NodeConfigSheetProps,
  'modalTypeDetails' | 'settingDetails'
> & {
  modalTypeDetails: ModalTypeDetails;
  moduleData: any;
};

const NodeConfigSheetContent: React.FC<NodeConfigSheetContentProps> = ({
  handleClose,
  handleSave,
  moduleData,
  isEdit = false,
  isPublishedEnabled = false,
  modalTypeDetails,
}) => {
  const { type, id } = modalTypeDetails;
  const ModuleComponent = nodeComponentMap[type] ?? Default;

  return (
    <BaseModal
      handleSave={handleSave}
      handleClose={handleClose}
      moduleData={moduleData}
      isEdit={isEdit}
      id={id}
      type={type}
      isPublishedEnabled={isPublishedEnabled}
    >
      <ModuleComponent />
    </BaseModal>
  );
};
