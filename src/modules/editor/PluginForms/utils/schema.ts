import z from 'zod';
import { StencilNodesType } from '../../utils/constants';
import { uniqueId } from 'lodash';
import { FormFieldData } from '@/modules/Preview/types/types';
import { NotificationChannel } from '../../types/notificationOptions';
import { t } from 'i18next';

export const appStart = z.object({
  process: z.object({
    intentId: z.string().min(1, 'Intent ID is required'),
  }),
});

const baseSettingsSchema = z.object({
  nodeName: z
    .string()
    .trim()
    .min(1, 'Node name is required')
    .max(50, 'Node name is too long, max 50 characters.'),
});

export const form = z.object({
  settings: baseSettingsSchema,
  process: z.object({
    channelData: z
      .record(
        z.record(
          z.object({
            formConfig: z
              .object({
                prompt: z.string().optional(),
                formType: z.string().optional(),
                fields: z
                  .array(
                    z.object({
                      name: z.string().min(1, t('form.lableRequired')),
                      type: z.string().optional(),
                      label: z.string().optional(),
                      required: z.boolean().optional(),
                      options: z.array(z.string()).optional(),
                    })
                  )
                  .optional()
                  .refine(
                    fields => {
                      if (!fields || fields.length === 0) return true;
                      return !!fields[0].label && fields[0].label.trim().length > 0;
                    },
                    {
                      message: t('form.promptRequired'),
                      path: ['0', 'label'],
                    }
                  ),
              })
              .optional(),
          })
        )
      )
      .optional(),
  }),
});

export const message = z.object({
  settings: baseSettingsSchema,
  process: z.object({
    channelData: z
      .record(
        z.record(
          z.object({
            messageText: z.string().trim().min(1, 'Message text is required'),
            images: z.array(z.string()).optional(),
            videos: z.array(z.string()).optional(),
            files: z.array(z.string()).optional(),
          })
        )
      )
      .optional(),
  }),
});

export const flowConnector = z.object({
  settings: baseSettingsSchema,
  process: z.object({
    flowConfig: z.object({
      targetFlowId: z
        .string({ required_error: 'Target Flow is required' })
        .min(1, 'Target Flow is required'),
      passExistingContext: z.boolean().optional(),
      resumeFromThisContext: z.boolean().optional(),
      journeyId: z.string(),
    }),
  }),
});

export const http = z.object({
  settings: baseSettingsSchema.extend({
    timeout: z.coerce.number().min(1, 'Timeout must be a positive number').optional(),
  }),
  process: z
    .object({
      URL: z.string().trim().min(1, 'URL is required').url('Please enter a valid URL'),
      requestType: z.string().trim().min(1, 'Request type is required'),
      headers: z
        .array(
          z.object({
            headerKey: z.string().trim().min(1, 'Header key is required'),
            headerValue: z.string().trim().min(1, 'Header value is required'),
          })
        )
        .optional(),
      requestBody: z.string().trim().optional().default('{}'),
    })
    .passthrough(),
});
const validateFeedbackPlatform = (data: any, ctx: z.RefinementCtx) => {
  const { platform, language } = data.settings;
  const channelConfig = data.process.channelData?.[platform]?.[language];
  if (!channelConfig) return; // nothing to validate

  const type = channelConfig.feedbackConfig.feedbackType;

  if ((type === 'Star' || type === 'Thumbs') && !['web', 'mobile'].includes(platform)) {
    ctx.addIssue({
      code: z.ZodIssueCode.custom,
      path: ['process', 'channelData', platform, language, 'feedbackConfig', 'feedbackType'],
      message: `Configuration Error: ${type} feedback is only supported on Web and Mobile channels`,
    });
  }
};

const feedback = z
  .object({
    settings: baseSettingsSchema.extend({
      language: z.string().trim().min(1, 'Language is required'),
      platform: z.enum(['web', 'mobile', 'whatsapp'], {
        errorMap: () => ({ message: 'Invalid platform type' }),
      }),
    }),
    process: z
      .object({
        channelData: z.record(
          // platform (e.g., 'web', 'whatsapp')
          z.record(
            // language (e.g., 'english', 'hindi')
            z.object({
              feedbackConfig: z.object({
                feedbackPrompt: z
                  .string()
                  .trim()
                  .min(1, 'Feedback prompt is required')
                  .max(320, 'Feedback prompt must be at most 320 characters'),
                feedbackType: z.enum(['Star', 'Thumbs', 'Text'], {
                  errorMap: () => ({ message: 'Invalid feedback type' }),
                }),
              }),
            })
          )
        ),
      })
      .passthrough(),
  })
  .superRefine(validateFeedbackPlatform);

const validateCommonMessage = (
  data: any,
  ctx: z.RefinementCtx,
  isSMS: boolean,
  isEmail: boolean
) => {
  if (isSMS || isEmail) {
    if (!data.commonMessage) {
      ctx.addIssue({
        path: ['commonMessage'],
        code: z.ZodIssueCode.custom,
        message: 'Message is required when a notification channel is selected',
      });
    } else if (data.commonMessage.length > 320) {
      ctx.addIssue({
        path: ['commonMessage'],
        code: z.ZodIssueCode.custom,
        message: 'Message cannot exceed 320 characters',
      });
    }
  }
};

const validateSMS = (data: any, ctx: z.RefinementCtx, isSMS: boolean) => {
  if (!isSMS) return;

  if (!data.senderId) {
    ctx.addIssue({
      path: ['senderId'],
      code: z.ZodIssueCode.custom,
      message: 'Sender ID is required when SMS channel is selected',
    });
  }

  if (!data.recipientMSISDN) {
    ctx.addIssue({
      path: ['recipientMSISDN'],
      code: z.ZodIssueCode.custom,
      message: 'Recipient MSISDN is required when SMS channel is selected',
    });
  } else if (!/^\+?[1-9]\d{1,14}$/.test(data.recipientMSISDN)) {
    ctx.addIssue({
      path: ['recipientMSISDN'],
      code: z.ZodIssueCode.custom,
      message: 'Invalid phone number format',
    });
  }
};

const validateEmail = (data: any, ctx: z.RefinementCtx, isEmail: boolean) => {
  if (!isEmail) return;

  if (!data.recipientEmail) {
    ctx.addIssue({
      path: ['recipientEmail'],
      code: z.ZodIssueCode.custom,
      message: 'Recipient email is required when email channel is selected',
    });
  } else if (!/^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/.test(data.recipientEmail)) {
    ctx.addIssue({
      path: ['recipientEmail'],
      code: z.ZodIssueCode.custom,
      message: 'Invalid email format',
    });
  }

  if (!data.emailSubject) {
    ctx.addIssue({
      path: ['emailSubject'],
      code: z.ZodIssueCode.custom,
      message: 'Email subject is required when email channel is selected',
    });
  }

  if (!data.emailAddresses) {
    ctx.addIssue({
      path: ['emailAddresses'],
      code: z.ZodIssueCode.custom,
      message: 'Email address is required when email channel is selected',
    });
  }
};

// Main schema
export const notificationSchema = z.object({
  settings: baseSettingsSchema.extend({
    platform: z.string().trim().optional(),
    language: z.string().trim().optional(),
  }),
  process: z.object({
    channelData: z.record(
      z.record(
        z
          .object({
            notificationChannel: z
              .array(z.nativeEnum(NotificationChannel))
              .min(1, 'At least one notification channel is required'),
            recipientEmail: z.string().trim().optional(),
            emailSubject: z.string().trim().optional(),
            commonMessage: z.string().trim().optional(),
            senderId: z.string().trim().optional(),
            recipientMSISDN: z.string().trim().optional(),
            emailAddresses: z.string().trim().optional(),
          })
          .superRefine((data, ctx) => {
            const isSMS = data.notificationChannel.includes(NotificationChannel.SMS);
            const isEmail = data.notificationChannel.includes(NotificationChannel.EMAIL);

            validateCommonMessage(data, ctx, isSMS, isEmail);
            validateSMS(data, ctx, isSMS);
            validateEmail(data, ctx, isEmail);
          })
      )
    ),
  }),
});

// Basic schema for other node types

// Basic schema for other node types
const basicSchema = z.object({
  settings: baseSettingsSchema,

  process: z.object({}).passthrough(),
});

export const choice = z.object({
  settings: baseSettingsSchema,
  process: z.object({
    match_conditions: z
      .array(
        z.object({
          key: z.string().trim().min(1, 'Variable is required.'),
          condition: z.string().trim().min(1, 'Condition is required.'),
          value: z.string().trim().min(1, 'Value is required.'),
          moduleId: z.string().nullable().optional(),
          id: z.string().optional().default(uniqueId().slice(0, 7)),
          coordinates: z.object({ x: z.number().nullable(), y: z.number().nullable() }).optional(),
        })
      )
      .optional(),
    no_match_module_id: z.string().nullable().optional(),
  }),
});

const schemaMap: Record<StencilNodesType, z.ZodTypeAny> = {
  appStart,
  form,
  message,
  appEnd: basicSchema,
  whatsapp: basicSchema,
  agentTransfer: basicSchema,

  http: http,

  choice: choice,

  flowConnector: flowConnector,

  language: basicSchema,
  payment: basicSchema,
  script: basicSchema,
  waitDelay: basicSchema,
  interactiveMessage: basicSchema,
  feedback,
  notification: notificationSchema,
};

export const getFormSchema = (type: StencilNodesType) => {
  return { schema: schemaMap[type] || basicSchema };
};

export const formSchema = z.object({
  email: z.string().trim().email('Invalid email address'),
  name: z.string().trim().min(1, 'Name is required'),
});

export const createTextFieldSchema = (field: FormFieldData) =>
  field.required
    ? z
        .string()
        .trim()
        .min(1, `${field.label ?? field.fieldName} is required`)
    : z.string().trim().optional();

export const createPastDateSchema = (field: FormFieldData) =>
  field.required
    ? z.union([z.date(), z.string().regex(/^\d{4}-\d{2}-\d{2}$/)]).refine(
        val => {
          const date = val instanceof Date ? val : new Date(val);
          return !isNaN(date.getTime()) && date < new Date();
        },
        { message: `${field.label ?? field.fieldName} must be a past date` }
      )
    : z.union([z.date(), z.string().regex(/^\d{4}-\d{2}-\d{2}$/)]).optional();

const isValidDate = (val: string | Date): boolean => {
  const date = val instanceof Date ? val : new Date(val);
  return !isNaN(date.getTime());
};

const parseDate = (val: string | Date): Date => (val instanceof Date ? val : new Date(val));

const isStartDateBeforeEndDate = (start: Date, end: Date): boolean => start <= end;

const isDateWithinRange = (
  start: Date,
  end: Date,
  rangeStart?: string | Date,
  rangeEnd?: string | Date
): boolean => {
  const rangeStartDate = rangeStart ? parseDate(rangeStart) : new Date(0);
  const rangeEndDate = rangeEnd ? parseDate(rangeEnd) : new Date();
  return start >= rangeStartDate && end <= rangeEndDate;
};

const createDateField = (fieldName: string) =>
  z.union([z.date(), z.string().regex(/^\d{4}-\d{2}-\d{2}$/)]).refine(isValidDate, {
    message: `${fieldName} must be a valid date`,
  });

export const createCustomDateSchema = (field: FormFieldData) => {
  const label = field.label ?? field.fieldName;
  const startField = createDateField(`${label}.start`);
  const endField = createDateField(`${label}.end`);

  const optionalSchema = z
    .object({
      start: startField.optional(),
      end: endField.optional(),
    })
    .optional();

  if (!field.required) return optionalSchema;

  const requiredSchema = z
    .object({
      start: startField,
      end: endField,
    })
    .refine(({ start, end }) => isStartDateBeforeEndDate(parseDate(start), parseDate(end)), {
      message: `${label} end date must be after start date`,
    })
    .refine(
      ({ start, end }) =>
        isDateWithinRange(parseDate(start), parseDate(end), field.rangeStart, field.rangeEnd),
      {
        message: `${label} must be between ${field.rangeStart ?? 'start'} and ${
          field.rangeEnd ?? 'end'
        }`,
      }
    );

  return requiredSchema;
};

export const createTimeSchema = (field: FormFieldData) =>
  field.required
    ? z
        .string()
        .trim()
        .min(1, `${field.label ?? field.fieldName} is required`)
        .refine(val => /^([0-1]?[0-9]|2[0-3]):[0-5][0-9]$/.exec(val), {
          message: `${field.label ?? field.fieldName} must be a valid time (HH:MM)`,
        })
    : z
        .string()
        .trim()
        .refine(val => /^([0-1]?[0-9]|2[0-3]):[0-5][0-9]$/.exec(val))
        .optional();

export const createNumberSchema = (field: FormFieldData) =>
  field.required
    ? z.number().min(0, `${field.label ?? field.fieldName} must be a positive number`)
    : z.number().min(0).optional();

export const createPasswordSchema = (field: FormFieldData) =>
  field.required
    ? z
        .string()
        .trim()
        .min(6, `${field.label ?? field.fieldName} must be at least 6 characters`)
    : z.string().trim().min(6).optional();

export const createEmailSchema = (field: FormFieldData) =>
  field.required
    ? z
        .string()
        .trim()
        .email(`${field.label ?? field.fieldName} is invalid`)
    : z.string().trim().email().optional();

// Dynamic schema generator combining individual schemas
export const createFormValidationSchema = (fields: FormFieldData[]) => {
  return z.object(
    fields.reduce(
      (schema, field) => {
        switch (field.fieldType) {
          case 'text_field':
            schema[field.fieldName] = createTextFieldSchema(field);
            break;
          case 'past_date':
            schema[field.fieldName] = createPastDateSchema(field);
            break;
          // case 'custom_date':
          //   schema[field.fieldName] = createCustomDateSchema(field);
          //   break;
          case 'time':
            schema[field.fieldName] = createTimeSchema(field);
            break;
          case 'password':
            schema[field.fieldName] = createPasswordSchema(field);
            break;
          case 'email':
            schema[field.fieldName] = createEmailSchema(field);
            break;
        }
        return schema;
      },
      {} as Record<string, z.ZodTypeAny>
    )
  );
};

export const validateFormData = <T extends Record<string, any>>(
  data: T,
  schema: z.ZodObject<any>
): { isValid: boolean; errors: z.ZodError['errors'] } => {
  const result = schema.safeParse(data);
  return {
    isValid: result.success,
    errors: result.success ? [] : result.error.errors,
  };
};
