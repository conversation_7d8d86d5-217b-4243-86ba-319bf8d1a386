import { useEffect, useMemo } from 'react';
import { FloatingField, FloatingType } from '@/components/ui/floating-label';
import { useAssignFlowToIntentMutation, useGetIntentItemsQuery } from '@/store/api';
import { useToast } from '@/hooks/use-toast';
import { useBotIdParam } from '@/hooks/useRouterParam';
import { useSelector } from 'react-redux';
import { useFormContext } from 'react-hook-form';
import { FormControl, FormField, FormItem } from '@/components/ui/form';
import { useTranslation } from 'react-i18next';
import { IntentType } from '@/types';

export default function AppStartForm() {
  const { t } = useTranslation();
  const { toast } = useToast();
  const activeFlowId = useSelector((state: any) => state.flows.activeFlowId);
  const {
    control,
    watch,
    formState: { defaultValues },
  } = useFormContext();

  const initialIntentId = defaultValues?.process.intentId as string;

  const { botId } = useBotIdParam();

  const [assignFlowToIntent] = useAssignFlowToIntentMutation();
  const currentIntentId = watch('process.intentId');

  const { data: intentsData } = useGetIntentItemsQuery({
    filter: {
      botId: {
        eq: botId,
      },
      or: [
        {
          flowId: {
            isNull: true,
          },
        },
        {
          id: {
            eq: initialIntentId,
          },
        },
      ],
      type: {
        eq: IntentType.CUSTOM,
      },
    },
  });
  const intentItems = useMemo(
    () =>
      intentsData?.data?.items
        .filter(item => item.type !== IntentType.DEFAULT)
        .map(item => ({
          label: item.name,
          value: item.id,
        })) || [],
    [intentsData]
  );

  useEffect(() => {
    if (currentIntentId !== initialIntentId && activeFlowId) {
      const assign = async () => {
        try {
          await assignFlowToIntent({
            intentId: currentIntentId,
            flowId: activeFlowId.id,
          }).unwrap();
          // No success toast needed for this background operation, as it's part of a larger flow
        } catch (error: any) {
          console.error('Failed to assign flow to intent:', error);
          toast({
            title: t('common.error'),
            description: error,
            variant: 'destructive',
          });
        }
      };
      assign();
    }
  }, [currentIntentId, activeFlowId, assignFlowToIntent, toast, t]);

  return (
    <div className="flex-1 px-4 space-y-4 py-4 overflow-auto">
      <FormField
        control={control}
        name="process.intentId"
        render={({ field }) => (
          <FormItem>
            <FormControl>
              <FloatingField
                as={FloatingType.SELECT}
                label={t('common.intent')}
                value={field.value || ''}
                onChange={field.onChange}
                options={intentItems}
                className="mt-4"
              />
            </FormControl>
          </FormItem>
        )}
      />
    </div>
  );
}
