import React from 'react';
import { render, screen, fireEvent } from '@testing-library/react';
import { describe, it, expect, vi, beforeEach } from 'vitest';
import { DatePicker } from '../date-picker';
import { DateType } from '../types/date-picker.enums';

// Mock i18next
vi.mock('react-i18next', () => ({
  useTranslation: () => ({
    t: (key: string) => (key === 'common.date' ? 'Select date' : 'Select date range'),
  }),
}));

describe('DatePicker', () => {
  const mockDate = new Date('2025-09-19');
  const mockDateString = '19-09-2025';

  beforeEach(() => {
    vi.setSystemTime(mockDate);
  });

  it('renders date picker with default props', () => {
    render(<DatePicker onSelect={() => {}} />);

    expect(screen.getByRole('button')).toHaveTextContent('Select date');
    expect(screen.getByRole('button')).toHaveClass('text-muted-foreground');
  });

  it('renders with selected date', () => {
    render(<DatePicker selected={mockDate} onSelect={() => {}} />);

    expect(screen.getByRole('button')).toHaveTextContent(mockDateString);
    expect(screen.getByRole('button')).not.toHaveClass('text-muted-foreground');
  });

  it('opens calendar on button click', () => {
    render(<DatePicker onSelect={() => {}} />);

    const button = screen.getByRole('button');
    fireEvent.click(button);

    expect(screen.getByRole('dialog')).toBeInTheDocument();
    expect(screen.getByRole('grid')).toBeInTheDocument();
  });

  it('calls onSelect when a date is selected', () => {
    const onSelect = vi.fn();
    render(<DatePicker onSelect={onSelect} />);

    const button = screen.getByRole('button');
    fireEvent.click(button);

    const dayButton = screen.getByRole('button', { name: /september 19th/i });
    fireEvent.click(dayButton);
    expect(onSelect).toHaveBeenCalled();
    const selectedDate = onSelect.mock.calls[0][0];
    expect(selectedDate.getDate()).toBe(mockDate.getDate());
    expect(selectedDate.getMonth()).toBe(mockDate.getMonth());
    expect(selectedDate.getFullYear()).toBe(mockDate.getFullYear());
  });

  describe('Future dates', () => {
    it('disables past dates when type is FUTURE_DATE', () => {
      render(<DatePicker fieldType={DateType.FUTURE_DATE} onSelect={() => {}} />);

      const button = screen.getByRole('button');
      fireEvent.click(button);

      const yesterdayButton = screen.getByRole('button', { name: /september 18th/i });

      expect(yesterdayButton).toHaveAttribute('disabled');
    });

    it('enables future dates when type is FUTURE_DATE', () => {
      render(<DatePicker fieldType={DateType.FUTURE_DATE} onSelect={() => {}} />);

      const button = screen.getByRole('button', { name: /select date/i });
      fireEvent.click(button);

      const tomorrowButton = screen.getByRole('button', { name: /september 20th/i });
      expect(tomorrowButton).not.toHaveAttribute('disabled');
    });
  });

  describe('Past dates', () => {
    it('disables future dates when type is PAST_DATE', () => {
      render(<DatePicker fieldType={DateType.PAST_DATE} onSelect={() => {}} />);

      const button = screen.getByRole('button');
      fireEvent.click(button);

      const tomorrowButton = screen.getByRole('button', { name: /september 20th/i });
      expect(tomorrowButton).toHaveAttribute('disabled');
    });

    it('enables past dates when type is PAST_DATE', () => {
      render(<DatePicker fieldType={DateType.PAST_DATE} onSelect={() => {}} />);

      const button = screen.getByRole('button');
      fireEvent.click(button);

      const yesterdayButton = screen.getByRole('button', { name: /september 18th/i });
      expect(yesterdayButton).not.toHaveAttribute('disabled');
    });
  });

  describe('Custom date range', () => {
    const customRange = {
      from: new Date('2025-09-15'),
      to: new Date('2025-09-25'),
    };

    it('renders date range picker when type is CUSTOM_DATE', () => {
      render(
        <DatePicker
          fieldType={DateType.CUSTOM_DATE}
          onSelect={() => {}}
          customRange={customRange}
        />
      );

      const trigger = screen.getByRole('button', { name: /select date range/i });
      expect(trigger).toBeInTheDocument();
    });

    it('initializes with custom range', () => {
      const range = {
        from: new Date('2025-09-20'),
        to: new Date('2025-09-22'),
      };

      render(
        <DatePicker
          fieldType={DateType.CUSTOM_DATE}
          onSelect={() => {}}
          selected={range.from}
          customRange={range}
        />
      );

      const trigger = screen.getByRole('button', { name: /select date range/i });
      expect(trigger).toBeInTheDocument();
    });

    it('disables dates outside custom range', () => {
      render(
        <DatePicker
          fieldType={DateType.CUSTOM_DATE}
          onSelect={() => {}}
          customRange={customRange}
        />
      );

      const button = screen.getByRole('button');
      fireEvent.click(button);

      const outsideRangeButton = screen.getByRole('button', { name: /september 14th/i });
      expect(outsideRangeButton).toHaveAttribute('disabled');

      const insideRangeButton = screen.getByRole('button', { name: /september 20th/i });
      expect(insideRangeButton).not.toHaveAttribute('disabled');
    });

    it('calls onSelect with range when dates are selected', async () => {
      const onSelect = vi.fn();
      render(
        <DatePicker
          fieldType={DateType.CUSTOM_DATE}
          onSelect={onSelect}
          customRange={customRange}
        />
      );

      const trigger = screen.getByRole('button', { name: /select date range/i });
      fireEvent.click(trigger);

      // Select the start date
      const startDate = screen.getByRole('button', { name: /september 20th/i });
      fireEvent.click(startDate);

      // Get the most recent onSelect call
      const firstCall = onSelect.mock.calls.slice(-1)[0];
      expect(firstCall[0].from.getDate()).toBe(20);

      // Select the end date (September 22nd)
      const endDate = screen.getByRole('button', { name: /september 22nd/i });
      fireEvent.click(endDate);

      // Get the most recent onSelect call
      const lastCall = onSelect.mock.calls.slice(-1)[0];
      expect(lastCall[0].from.getDate()).toBe(20);
      expect(lastCall[0].to.getDate()).toBe(22);
    });
  });

  it('applies custom className to button', () => {
    const customClass = 'custom-class';
    render(<DatePicker onSelect={() => {}} className={customClass} />);

    expect(screen.getByRole('button')).toHaveClass(customClass);
  });
});
