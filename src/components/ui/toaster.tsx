import { useToast } from '@/hooks/use-toast';
import {
  Toast,
  ToastClose,
  ToastDescription,
  ToastProvider,
  ToastTitle,
  ToastViewport,
} from '@/components/ui/toast';
import TailwindWrapper from '../TailwindWrapper';

export function Toaster() {
  const { toasts } = useToast();

  return (
    <TailwindWrapper fullscreen={false}>
      <ToastProvider>
        {toasts.map(function ({ id, title, description, action, ...props }) {
          return (
            <Toast key={id} {...props}>
              <div className="flex justify-between gap-2 items-center flex-1">
                <div className="grid gap-1 flex-1">
                  {title && <ToastTitle>{title}</ToastTitle>}
                  {description && <ToastDescription>{description}</ToastDescription>}
                </div>
                {action}
                <ToastClose />
              </div>
            </Toast>
          );
        })}
        <ToastViewport />
      </ToastProvider>
    </TailwindWrapper>
  );
}
