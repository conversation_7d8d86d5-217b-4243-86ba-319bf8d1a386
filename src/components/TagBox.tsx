import { cn } from '@/lib/utils';
import React from 'react';

interface TagBoxProps extends React.HTMLAttributes<HTMLDivElement> {
  text: string;
  className?: string;
}

const TagBox = React.forwardRef<HTMLDivElement, TagBoxProps>(
  ({ text, className, ...props }, ref) => {
    return (
      <div
        ref={ref}
        className={cn(
          'text-xs text-tertiary-500 py-1 px-2 rounded bg-tertiary-100 cursor-default',
          className
        )}
        {...props}
      >
        {text}
      </div>
    );
  }
);

TagBox.displayName = 'TagBox';

export default TagBox;
