import React from 'react';
import { render, screen } from '@testing-library/react';
import { describe, it, expect, vi, beforeEach } from 'vitest';
import EmptyState from '../EmptyState';

// Mock react-i18next
vi.mock('react-i18next', () => ({
  useTranslation: () => ({
    t: (key: string) => {
      if (key === 'emptyState.title') return 'No Data Available';
      if (key === 'emptyState.description') return 'There is currently no data to display.';
      return key;
    },
  }),
}));

// Mock the empty state icon
vi.mock('@/assets/icons/empty-state.svg', () => ({
  default: '/mock-empty-state.svg',
}));

// Mock the cn utility function
vi.mock('@/lib/utils', () => ({
  cn: (...classes: string[]) => classes.filter(Boolean).join(' '),
}));

describe('EmptyState', () => {
  beforeEach(() => {
    vi.clearAllMocks();
  });

  describe('Basic Rendering', () => {
    it('should render with default props', () => {
      render(<EmptyState />);

      // Check for default translated title and description
      expect(screen.getByText('No Data Available')).toBeInTheDocument();
      expect(screen.getByText('There is currently no data to display.')).toBeInTheDocument();

      // Check for default icon
      const defaultIcon = screen.getByRole('img');
      expect(defaultIcon).toHaveAttribute('src', '/mock-empty-state.svg');
      expect(defaultIcon).toHaveClass('w-full');
    });

    it('should render with custom title', () => {
      const customTitle = 'Custom Empty Title';
      render(<EmptyState title={customTitle} />);

      expect(screen.getByText(customTitle)).toBeInTheDocument();
      expect(screen.queryByText('No Data Available')).not.toBeInTheDocument();
    });

    it('should render with custom description', () => {
      const customDescription = 'Custom empty description message';
      render(<EmptyState description={customDescription} />);

      expect(screen.getByText(customDescription)).toBeInTheDocument();
      expect(screen.queryByText('There is currently no data to display.')).not.toBeInTheDocument();
    });

    it('should render with both custom title and description', () => {
      const customTitle = 'No Items Found';
      const customDescription = 'Try adjusting your search criteria';

      render(<EmptyState title={customTitle} description={customDescription} />);

      expect(screen.getByText(customTitle)).toBeInTheDocument();
      expect(screen.getByText(customDescription)).toBeInTheDocument();
      expect(screen.queryByText('No Data Available')).not.toBeInTheDocument();
      expect(screen.queryByText('There is currently no data to display.')).not.toBeInTheDocument();
    });
  });

  describe('Icon Handling', () => {
    it('should render default icon when no custom icon is provided', () => {
      render(<EmptyState />);

      const defaultIcon = screen.getByRole('img');
      expect(defaultIcon).toHaveAttribute('src', '/mock-empty-state.svg');
      expect(defaultIcon).toHaveClass('w-full');
    });

    it('should render custom icon when provided', () => {
      const customIcon = <div data-testid="custom-icon">📁</div>;
      render(<EmptyState icon={customIcon} />);

      expect(screen.getByTestId('custom-icon')).toBeInTheDocument();
      expect(screen.queryByRole('img')).not.toBeInTheDocument();
    });

    it('should render custom icon with complex content', () => {
      const customIcon = (
        <div data-testid="complex-icon" className="custom-icon-class">
          <span>🔍</span>
          <span>Search</span>
        </div>
      );
      render(<EmptyState icon={customIcon} />);

      expect(screen.getByTestId('complex-icon')).toBeInTheDocument();
      expect(screen.getByText('🔍')).toBeInTheDocument();
      expect(screen.getByText('Search')).toBeInTheDocument();
    });

    it('should render custom React component as icon', () => {
      const CustomIconComponent = () => (
        <svg data-testid="custom-svg-icon" width="40" height="40">
          <circle cx="20" cy="20" r="10" fill="blue" />
        </svg>
      );

      render(<EmptyState icon={<CustomIconComponent />} />);

      expect(screen.getByTestId('custom-svg-icon')).toBeInTheDocument();
      expect(screen.queryByRole('img')).not.toBeInTheDocument();
    });
  });

  describe('Children Handling', () => {
    it('should render children when provided', () => {
      const children = <button data-testid="action-button">Try Again</button>;
      render(<EmptyState>{children}</EmptyState>);

      expect(screen.getByTestId('action-button')).toBeInTheDocument();
      expect(screen.getByText('Try Again')).toBeInTheDocument();
    });

    it('should render complex children content', () => {
      const children = (
        <div data-testid="complex-children">
          <button className="primary-btn">Create New</button>
          <p className="help-text">Need help? Contact support</p>
        </div>
      );

      render(<EmptyState>{children}</EmptyState>);

      expect(screen.getByTestId('complex-children')).toBeInTheDocument();
      expect(screen.getByText('Create New')).toBeInTheDocument();
      expect(screen.getByText('Need help? Contact support')).toBeInTheDocument();
    });

    it('should render multiple children elements', () => {
      render(
        <EmptyState>
          <button data-testid="btn-1">Action 1</button>
          <button data-testid="btn-2">Action 2</button>
          <p data-testid="help-text">Additional help text</p>
        </EmptyState>
      );

      expect(screen.getByTestId('btn-1')).toBeInTheDocument();
      expect(screen.getByTestId('btn-2')).toBeInTheDocument();
      expect(screen.getByTestId('help-text')).toBeInTheDocument();
    });

    it('should not render children section when no children provided', () => {
      const { container } = render(<EmptyState />);

      // Should only have the title and description, no additional content
      const innerContainer = container.querySelector('.text-center.max-w-md');
      expect(innerContainer?.children).toHaveLength(3); // icon div, h3, p
    });
  });

  describe('CSS Classes and Styling', () => {
    it('should apply default CSS classes', () => {
      const { container } = render(<EmptyState />);

      const mainContainer = container.firstChild;
      expect(mainContainer).toHaveClass(
        'flex-1',
        'flex',
        'items-center',
        'justify-center',
        'bg-background'
      );

      const contentContainer = container.querySelector('.text-center.max-w-md');
      expect(contentContainer).toBeInTheDocument();
    });

    it('should apply custom className alongside default classes', () => {
      const customClass = 'custom-empty-state';
      const { container } = render(<EmptyState className={customClass} />);

      const mainContainer = container.firstChild;
      expect(mainContainer).toHaveClass(
        'flex-1',
        'flex',
        'items-center',
        'justify-center',
        'bg-background',
        customClass
      );
    });

    it('should apply correct classes to icon container', () => {
      const { container } = render(<EmptyState />);

      const iconContainer = container.querySelector(
        '.w-40.h-4w-40.mx-auto.mb-6.rounded-full.flex.items-center.justify-center'
      );
      expect(iconContainer).toBeInTheDocument();
    });

    it('should apply correct classes to title', () => {
      render(<EmptyState />);

      const title = screen.getByRole('heading', { level: 3 });
      expect(title).toHaveClass('text-lg', 'font-semibold', 'mb-1');
    });

    it('should apply correct classes to description', () => {
      render(<EmptyState />);

      const description = screen.getByText('There is currently no data to display.');
      expect(description).toHaveClass('text-secondary-700');
    });

    it('should handle multiple custom classes', () => {
      const customClasses = 'class1 class2 class3';
      const { container } = render(<EmptyState className={customClasses} />);

      const mainContainer = container.firstChild;
      expect(mainContainer).toHaveClass(
        'flex-1',
        'flex',
        'items-center',
        'justify-center',
        'bg-background',
        'class1',
        'class2',
        'class3'
      );
    });
  });

  describe('Translation Integration', () => {
    it('should use translated text for default title and description', () => {
      render(<EmptyState />);

      // Should display translated versions
      expect(screen.getByText('No Data Available')).toBeInTheDocument();
      expect(screen.getByText('There is currently no data to display.')).toBeInTheDocument();
    });

    it('should prefer custom title over translation', () => {
      const customTitle = 'Custom Title';
      render(<EmptyState title={customTitle} />);

      expect(screen.getByText(customTitle)).toBeInTheDocument();
      expect(screen.queryByText('No Data Available')).not.toBeInTheDocument();
    });

    it('should prefer custom description over translation', () => {
      const customDescription = 'Custom description text';
      render(<EmptyState description={customDescription} />);

      expect(screen.getByText(customDescription)).toBeInTheDocument();
      expect(screen.queryByText('There is currently no data to display.')).not.toBeInTheDocument();
    });

    it('should handle empty string title and description', () => {
      render(<EmptyState title="" description="" />);

      // Empty strings are falsy, so component falls back to defaults
      expect(screen.getByText('No Data Available')).toBeInTheDocument();
      expect(screen.getByText('There is currently no data to display.')).toBeInTheDocument();
    });
  });

  describe('Component Structure', () => {
    it('should maintain proper component structure', () => {
      const { container } = render(<EmptyState />);

      // Main container
      const mainContainer = container.firstChild;
      expect(mainContainer).toHaveClass(
        'flex-1',
        'flex',
        'items-center',
        'justify-center',
        'bg-background'
      );

      // Content container
      const contentContainer = container.querySelector('.text-center.max-w-md');
      expect(contentContainer).toBeInTheDocument();

      // Icon container
      const iconContainer = container.querySelector(
        '.w-40.h-4w-40.mx-auto.mb-6.rounded-full.flex.items-center.justify-center'
      );
      expect(iconContainer).toBeInTheDocument();

      // Title
      const title = screen.getByRole('heading', { level: 3 });
      expect(title).toBeInTheDocument();

      // Description
      const description = screen.getByText('There is currently no data to display.');
      expect(description).toBeInTheDocument();
    });

    it('should render elements in correct order', () => {
      const { container } = render(
        <EmptyState>
          <button>Action Button</button>
        </EmptyState>
      );

      const contentContainer = container.querySelector('.text-center.max-w-md');
      const children = Array.from(contentContainer?.children || []);

      // Should have icon container, title, description, and children in that order
      expect(children).toHaveLength(4);
      expect(children[0]).toHaveClass('w-40'); // icon container
      expect(children[1].tagName).toBe('H3'); // title
      expect(children[2].tagName).toBe('P'); // description
      expect(children[3].tagName).toBe('BUTTON'); // children
    });
  });

  describe('Edge Cases', () => {
    it('should handle null icon gracefully', () => {
      render(<EmptyState icon={null} />);

      // null icon should render the default image (using ?? operator)
      expect(screen.getByRole('img')).toBeInTheDocument();
      expect(screen.getByRole('img')).toHaveAttribute('src', '/mock-empty-state.svg');

      // Should still render title and description
      expect(screen.getByText('No Data Available')).toBeInTheDocument();
      expect(screen.getByText('There is currently no data to display.')).toBeInTheDocument();
    });

    it('should handle undefined values for all props', () => {
      render(
        <EmptyState
          title={undefined}
          description={undefined}
          icon={undefined}
          className={undefined}
        />
      );

      // Should render with defaults
      expect(screen.getByText('No Data Available')).toBeInTheDocument();
      expect(screen.getByText('There is currently no data to display.')).toBeInTheDocument();
      expect(screen.getByRole('img')).toBeInTheDocument();
    });

    it('should handle very long title and description', () => {
      const longTitle =
        'This is a very long title that might cause layout issues if not handled properly in the empty state component';
      const longDescription =
        'This is an extremely long description that goes on and on to test how the component handles very long text content that might wrap to multiple lines and affect the overall layout and styling of the empty state component.';

      render(<EmptyState title={longTitle} description={longDescription} />);

      expect(screen.getByText(longTitle)).toBeInTheDocument();
      expect(screen.getByText(longDescription)).toBeInTheDocument();
    });

    it('should handle special characters in title and description', () => {
      const specialTitle = 'Title with émojis 🎉 & spéçial chars @#$%';
      const specialDescription = 'Description with 中文 characters & symbols ±×÷≠';

      render(<EmptyState title={specialTitle} description={specialDescription} />);

      expect(screen.getByText(specialTitle)).toBeInTheDocument();
      expect(screen.getByText(specialDescription)).toBeInTheDocument();
    });
  });

  describe('Accessibility', () => {
    it('should have proper heading structure', () => {
      render(<EmptyState />);

      const title = screen.getByRole('heading', { level: 3 });
      expect(title).toBeInTheDocument();
      expect(title).toHaveTextContent('No Data Available');
    });

    it('should have accessible image with proper attributes', () => {
      render(<EmptyState />);

      const image = screen.getByRole('img');
      expect(image).toBeInTheDocument();
      expect(image).toHaveAttribute('src', '/mock-empty-state.svg');
    });

    it('should maintain semantic structure with custom content', () => {
      render(
        <EmptyState>
          <button>Accessible Action</button>
        </EmptyState>
      );

      const heading = screen.getByRole('heading', { level: 3 });
      const button = screen.getByRole('button');

      expect(heading).toBeInTheDocument();
      expect(button).toBeInTheDocument();
      expect(button).toHaveTextContent('Accessible Action');
    });

    it('should work with screen readers when using custom icon', () => {
      const accessibleIcon = (
        <div role="img" aria-label="No items found">
          📁
        </div>
      );

      render(<EmptyState icon={accessibleIcon} />);

      const iconElement = screen.getByRole('img', { name: /no items found/i });
      expect(iconElement).toBeInTheDocument();
    });
  });

  describe('Integration Tests', () => {
    it('should work correctly with all props combined', () => {
      const customTitle = 'No Search Results';
      const customDescription = 'Try different keywords';
      const customIcon = <div data-testid="search-icon">🔍</div>;
      const customClass = 'search-empty-state';
      const children = <button data-testid="clear-search">Clear Search</button>;

      render(
        <EmptyState
          title={customTitle}
          description={customDescription}
          icon={customIcon}
          className={customClass}
        >
          {children}
        </EmptyState>
      );

      // All custom content should be rendered
      expect(screen.getByText(customTitle)).toBeInTheDocument();
      expect(screen.getByText(customDescription)).toBeInTheDocument();
      expect(screen.getByTestId('search-icon')).toBeInTheDocument();
      expect(screen.getByTestId('clear-search')).toBeInTheDocument();

      // Default content should not be rendered
      expect(screen.queryByText('No Data Available')).not.toBeInTheDocument();
      expect(screen.queryByText('There is currently no data to display.')).not.toBeInTheDocument();
      expect(screen.queryByRole('img')).not.toBeInTheDocument();

      // Custom class should be applied
      const container = screen.getByTestId('search-icon').closest('.search-empty-state');
      expect(container).toBeInTheDocument();
    });

    it('should handle dynamic content updates', () => {
      const { rerender } = render(<EmptyState title="Initial Title" />);

      expect(screen.getByText('Initial Title')).toBeInTheDocument();

      rerender(<EmptyState title="Updated Title" />);

      expect(screen.getByText('Updated Title')).toBeInTheDocument();
      expect(screen.queryByText('Initial Title')).not.toBeInTheDocument();
    });
  });
});
