import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import { describe, it, expect, vi, beforeEach } from 'vitest';
import TimePicker from '../time-picker';
import { TimeFormat, Meridian } from '@/modules/Preview/types/enums';

// Mock react-i18next
vi.mock('react-i18next', () => ({
  useTranslation: () => ({
    t: (key: string) => {
      const translations: Record<string, string> = {
        'timePicker.hour': 'Hour',
        'timePicker.min': 'Min',
        'timePicker.amPm': 'AM/PM',
      };
      return translations[key] || key;
    },
  }),
}));

// Mock lucide-react
vi.mock('lucide-react', () => ({
  Clock: ({ className }: any) => <div data-testid="clock-icon" className={className} />,
}));

// Mock UI components
vi.mock('@/components/ui/popover', () => ({
  Popover: ({ children }: any) => <div data-testid="popover">{children}</div>,
  PopoverTrigger: ({ children, disabled, asChild }: any) => (
    <div data-testid="popover-trigger" data-disabled={disabled}>
      {children}
    </div>
  ),
  PopoverContent: ({ children, className }: any) => (
    <div data-testid="popover-content" className={className}>
      {children}
    </div>
  ),
}));

// Mock enums
vi.mock('@/modules/Preview/types/enums', () => ({
  TimeFormat: {
    TWELVE_HOUR: '12',
    TWENTY_FOUR_HOUR: '24',
  },
  Meridian: {
    AM: 'AM',
    PM: 'PM',
  },
}));

// Mock pad utility
vi.mock('@/utils/uiUtil', () => ({
  pad: (num: number) => num.toString().padStart(2, '0'),
}));

// Mock cn utility
vi.mock('@/lib/utils', () => ({
  cn: (...classes: string[]) => classes.filter(Boolean).join(' '),
}));

describe('TimePicker', () => {
  const mockOnChange = vi.fn();

  const defaultProps = {
    value: '',
    onChange: mockOnChange,
  };

  beforeEach(() => {
    vi.clearAllMocks();
  });

  describe('Basic Rendering', () => {
    it('should render with 12-hour format by default', () => {
      render(<TimePicker {...defaultProps} value="02:30 PM" />);

      expect(screen.getByTestId('popover')).toBeInTheDocument();
      expect(screen.getByTestId('popover-trigger')).toBeInTheDocument();
      expect(screen.getByText('02:30 PM')).toBeInTheDocument();
      expect(screen.getByTestId('clock-icon')).toBeInTheDocument();
    });

    it('should render with 24-hour format', () => {
      render(<TimePicker {...defaultProps} value="14:30" format={TimeFormat.TWENTY_FOUR_HOUR} />);

      expect(screen.getByText('14:30')).toBeInTheDocument();
    });

    it('should show hours, minutes, and AM/PM sections in 12-hour format', () => {
      render(<TimePicker {...defaultProps} value="02:30 PM" />);

      expect(screen.getByText('Hour')).toBeInTheDocument();
      expect(screen.getByText('Min')).toBeInTheDocument();
      expect(screen.getByText('AM/PM')).toBeInTheDocument();
    });

    it('should not show AM/PM section in 24-hour format', () => {
      render(<TimePicker {...defaultProps} value="14:30" format={TimeFormat.TWENTY_FOUR_HOUR} />);

      expect(screen.getByText('Hour')).toBeInTheDocument();
      expect(screen.getByText('Min')).toBeInTheDocument();
      expect(screen.queryByText('AM/PM')).not.toBeInTheDocument();
    });
  });

  describe('Time Selection', () => {
    it('should handle hour selection in 12-hour format', () => {
      render(<TimePicker {...defaultProps} value="12:00 AM" />);

      // Hours 1-12 should be available
      expect(screen.getByText('01')).toBeInTheDocument();
      expect(screen.getByText('12')).toBeInTheDocument();

      fireEvent.click(screen.getByText('03'));
      expect(mockOnChange).toHaveBeenCalledWith('03:00 AM');
    });

    it('should render minute options', () => {
      render(<TimePicker {...defaultProps} value="12:00 AM" />);

      // Check that minutes section exists with options
      expect(screen.getByText('Min')).toBeInTheDocument();

      // Find minutes section
      const minutesSection = screen.getByText('Min').parentElement;
      expect(minutesSection).toBeInTheDocument();

      // Should have minute options (00, 05, 10, etc.)
      const minuteOptions = minutesSection?.querySelectorAll('div[class*="cursor-pointer"]');
      expect(minuteOptions?.length).toBe(12); // 0-55 in steps of 5
    });

    it('should handle AM/PM selection', () => {
      render(<TimePicker {...defaultProps} value="12:00 AM" />);

      expect(screen.getByText('AM')).toBeInTheDocument();
      expect(screen.getByText('PM')).toBeInTheDocument();

      fireEvent.click(screen.getByText('PM'));
      expect(mockOnChange).toHaveBeenCalledWith('12:00 PM');
    });

    it('should render hour options in 24-hour format', () => {
      render(<TimePicker {...defaultProps} value="00:00" format={TimeFormat.TWENTY_FOUR_HOUR} />);

      // Check that hours section exists
      expect(screen.getByText('Hour')).toBeInTheDocument();

      // Find hours section
      const hoursSection = screen.getByText('Hour').parentElement;
      expect(hoursSection).toBeInTheDocument();

      // Should have 24 hour options (0-23)
      const hourOptions = hoursSection?.querySelectorAll('div[class*="cursor-pointer"]');
      expect(hourOptions?.length).toBe(24);
    });
  });

  describe('Disabled State', () => {
    it('should handle disabled state', () => {
      render(<TimePicker {...defaultProps} disabled />);

      const trigger = screen.getByTestId('popover-trigger');
      expect(trigger).toHaveAttribute('data-disabled', 'true');

      const triggerDiv = trigger.querySelector('div');
      expect(triggerDiv).toHaveClass('opacity-50', 'pointer-events-none', 'cursor-not-allowed');
    });

    it('should not update time when disabled', () => {
      render(<TimePicker {...defaultProps} value="12:00 AM" disabled />);

      fireEvent.click(screen.getByText('03'));
      expect(mockOnChange).not.toHaveBeenCalled();
    });
  });

  describe('Value Initialization', () => {
    it('should initialize with 12:00 AM for 12-hour format when empty', () => {
      render(<TimePicker {...defaultProps} value="" />);

      expect(mockOnChange).toHaveBeenCalledWith('12:00 AM');
    });

    it('should initialize with 00:00 for 24-hour format when empty', () => {
      render(<TimePicker {...defaultProps} value="" format={TimeFormat.TWENTY_FOUR_HOUR} />);

      expect(mockOnChange).toHaveBeenCalledWith('00:00');
    });
  });

  describe('Time Parsing', () => {
    it('should parse valid 12-hour time', () => {
      render(<TimePicker {...defaultProps} value="03:45 PM" />);

      expect(screen.getByText('03:45 PM')).toBeInTheDocument();
    });

    it('should parse valid 24-hour time', () => {
      render(<TimePicker {...defaultProps} value="15:45" format={TimeFormat.TWENTY_FOUR_HOUR} />);

      expect(screen.getByText('15:45')).toBeInTheDocument();
    });

    it('should handle invalid time values gracefully', () => {
      render(<TimePicker {...defaultProps} value="invalid time" />);

      // Should fallback to default time
      expect(screen.getByText('12:00 AM')).toBeInTheDocument();
    });
  });

  describe('CSS Classes and Styling', () => {
    it('should apply default CSS classes', () => {
      render(<TimePicker {...defaultProps} value="12:00 AM" />);

      const triggerDiv = document.querySelector('.flex.items-center.justify-between');
      expect(triggerDiv).toHaveClass(
        'flex',
        'items-center',
        'justify-between',
        'px-3',
        'py-2',
        'border',
        'border-tertiary-300',
        'rounded-md',
        'bg-white',
        'cursor-pointer'
      );
    });

    it('should apply custom className', () => {
      render(<TimePicker {...defaultProps} value="12:00 AM" className="custom-class" />);

      const triggerDiv = document.querySelector('.custom-class');
      expect(triggerDiv).toBeInTheDocument();
    });

    it('should apply correct classes to clock icon', () => {
      render(<TimePicker {...defaultProps} value="12:00 AM" />);

      expect(screen.getByTestId('clock-icon')).toHaveClass('w-4', 'h-4', 'text-tertiary-500');
    });
  });
});
