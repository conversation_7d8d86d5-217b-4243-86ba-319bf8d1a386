import React from 'react';
import { render, screen, fireEvent } from '@testing-library/react';
import { describe, it, expect, vi, beforeEach } from 'vitest';
import LanguageDropdown from '../LanguageDropdown';

// Mock API hooks
vi.mock('@/store/api', () => ({
  useGetBotLanguagesQuery: vi.fn(),
  useGetLanguagesQuery: vi.fn(),
}));

// Mock router hook
vi.mock('@/hooks/useRouterParam', () => ({
  useBotIdParam: vi.fn(),
}));

// Mock DropdownButton component
vi.mock('../dropdownButton', () => ({
  default: ({ options, value, onChange, triggerClassName }: any) => (
    <div data-testid="dropdown-button">
      <select
        data-testid="language-select"
        value={value}
        onChange={e => onChange(e.target.value)}
        data-trigger-class={triggerClassName}
      >
        {options.map((option: any) => (
          <option key={option.value} value={option.value}>
            {option.label}
          </option>
        ))}
      </select>
    </div>
  ),
}));

// Import mocked modules
import * as storeApi from '@/store/api';
import * as routerHooks from '@/hooks/useRouterParam';

const mockUseGetBotLanguagesQuery = vi.mocked(storeApi.useGetBotLanguagesQuery);
const mockUseGetLanguagesQuery = vi.mocked(storeApi.useGetLanguagesQuery);
const mockUseBotIdParam = vi.mocked(routerHooks.useBotIdParam);

describe('LanguageDropdown', () => {
  const mockOnChange = vi.fn();

  const mockBotLanguages = [
    { langId: 'en', isDefault: true, botId: 'bot-123' },
    { langId: 'es', isDefault: false, botId: 'bot-123' },
  ];

  const mockLanguages = [
    { id: 'en', name: 'English', code: 'en' },
    { id: 'es', name: 'Spanish', code: 'es' },
    { id: 'fr', name: 'French', code: 'fr' },
  ];

  beforeEach(() => {
    vi.clearAllMocks();
    mockUseBotIdParam.mockReturnValue({ botId: 'bot-123' });
  });

  describe('Basic Rendering', () => {
    it('should render dropdown with language options', () => {
      mockUseGetBotLanguagesQuery.mockReturnValue({
        data: { data: { items: mockBotLanguages } },
      } as any);

      mockUseGetLanguagesQuery.mockReturnValue({
        data: { data: { items: mockLanguages.slice(0, 2) } }, // Only en and es
      } as any);

      render(<LanguageDropdown onChange={mockOnChange} />);

      expect(screen.getByTestId('dropdown-button')).toBeInTheDocument();
      expect(screen.getByTestId('language-select')).toBeInTheDocument();
      expect(screen.getByText('English')).toBeInTheDocument();
      expect(screen.getByText('Spanish')).toBeInTheDocument();
    });

    it('should render empty dropdown when no languages available', () => {
      mockUseGetBotLanguagesQuery.mockReturnValue({
        data: { data: { items: [] } },
      } as any);

      mockUseGetLanguagesQuery.mockReturnValue({
        data: { data: { items: [] } },
      } as any);

      render(<LanguageDropdown onChange={mockOnChange} />);

      expect(screen.getByTestId('dropdown-button')).toBeInTheDocument();
    });
  });

  describe('Language Selection Logic', () => {
    it('should select initialValue when provided', () => {
      const botLanguagesNoDefault = [
        { langId: 'es', isDefault: false, botId: 'bot-123' },
        { langId: 'en', isDefault: false, botId: 'bot-123' },
      ];

      // Put Spanish first so it's found before English fallback
      const languagesEsFirst = [mockLanguages[1], mockLanguages[0]]; // Spanish, English

      mockUseGetBotLanguagesQuery.mockReturnValue({
        data: { data: { items: botLanguagesNoDefault } },
      } as any);

      mockUseGetLanguagesQuery.mockReturnValue({
        data: { data: { items: languagesEsFirst } },
      } as any);

      render(<LanguageDropdown onChange={mockOnChange} initialValue="es" />);

      expect(mockOnChange).toHaveBeenCalledWith(
        'es',
        expect.objectContaining({
          id: 'es',
          name: 'Spanish',
        })
      );
    });

    it('should select bot default language when no initialValue', () => {
      mockUseGetBotLanguagesQuery.mockReturnValue({
        data: { data: { items: mockBotLanguages } },
      } as any);

      mockUseGetLanguagesQuery.mockReturnValue({
        data: { data: { items: mockLanguages.slice(0, 2) } },
      } as any);

      render(<LanguageDropdown onChange={mockOnChange} />);

      expect(mockOnChange).toHaveBeenCalledWith(
        'en',
        expect.objectContaining({
          id: 'en',
          name: 'English',
        })
      );
    });

    it('should fallback to English when available', () => {
      const botLanguagesNoDefault = [
        { langId: 'es', isDefault: false, botId: 'bot-123' },
        { langId: 'en', isDefault: false, botId: 'bot-123' },
      ];

      mockUseGetBotLanguagesQuery.mockReturnValue({
        data: { data: { items: botLanguagesNoDefault } },
      } as any);

      mockUseGetLanguagesQuery.mockReturnValue({
        data: { data: { items: [mockLanguages[1], mockLanguages[0]] } }, // es, en order
      } as any);

      render(<LanguageDropdown onChange={mockOnChange} />);

      expect(mockOnChange).toHaveBeenCalledWith(
        'en',
        expect.objectContaining({
          code: 'en',
        })
      );
    });

    it('should select first available language as last resort', () => {
      const botLanguagesNoDefault = [{ langId: 'fr', isDefault: false, botId: 'bot-123' }];

      mockUseGetBotLanguagesQuery.mockReturnValue({
        data: { data: { items: botLanguagesNoDefault } },
      } as any);

      mockUseGetLanguagesQuery.mockReturnValue({
        data: { data: { items: [mockLanguages[2]] } }, // Only French
      } as any);

      render(<LanguageDropdown onChange={mockOnChange} />);

      expect(mockOnChange).toHaveBeenCalledWith(
        'fr',
        expect.objectContaining({
          id: 'fr',
          name: 'French',
        })
      );
    });
  });

  describe('User Interaction', () => {
    it('should handle language change', () => {
      mockUseGetBotLanguagesQuery.mockReturnValue({
        data: { data: { items: mockBotLanguages } },
      } as any);

      mockUseGetLanguagesQuery.mockReturnValue({
        data: { data: { items: mockLanguages.slice(0, 2) } },
      } as any);

      render(<LanguageDropdown onChange={mockOnChange} />);

      const select = screen.getByTestId('language-select');
      fireEvent.change(select, { target: { value: 'es' } });

      expect(mockOnChange).toHaveBeenCalledWith(
        'es',
        expect.objectContaining({
          id: 'es',
          name: 'Spanish',
        })
      );
    });
  });

  describe('API Integration', () => {
    it('should call API hooks with correct parameters', () => {
      mockUseGetBotLanguagesQuery.mockReturnValue({
        data: { data: { items: [] } },
      } as any);

      mockUseGetLanguagesQuery.mockReturnValue({
        data: { data: { items: [] } },
      } as any);

      render(<LanguageDropdown onChange={mockOnChange} />);

      expect(mockUseGetBotLanguagesQuery).toHaveBeenCalledWith({
        filter: { botId: { eq: 'bot-123' } },
        limit: 100,
      });
    });

    it('should skip languages query when no bot languages', () => {
      mockUseGetBotLanguagesQuery.mockReturnValue({
        data: { data: { items: [] } },
      } as any);

      mockUseGetLanguagesQuery.mockReturnValue({
        data: { data: { items: [] } },
      } as any);

      render(<LanguageDropdown onChange={mockOnChange} />);

      expect(mockUseGetLanguagesQuery).toHaveBeenCalledWith(expect.any(Object), { skip: true });
    });
  });
});
