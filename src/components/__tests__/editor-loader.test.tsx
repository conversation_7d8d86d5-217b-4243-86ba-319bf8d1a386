import React from 'react';
import { render, screen, waitFor, act } from '@testing-library/react';
import { describe, it, expect, vi, beforeEach, afterEach } from 'vitest';
import EditorLoader from '../editor-loader';

// Mock react-i18next
vi.mock('react-i18next', () => ({
  useTranslation: () => ({
    t: (key: string) => {
      if (key === 'loading.hangTight') return 'Hang tight...';
      if (key === 'loading.almostThere') return 'Almost there!';
      return key;
    },
  }),
}));

// Mock @lottiefiles/react-lottie-player
vi.mock('@lottiefiles/react-lottie-player', () => ({
  Player: ({ src, className, autoplay, loop, ...props }: any) => (
    <div
      data-testid="lottie-player"
      data-src={JSON.stringify(src)}
      data-autoplay={autoplay}
      data-loop={loop}
      className={className}
      {...props}
    />
  ),
}));

// Mock the lottie loader constant
vi.mock('@/lib/lottie/loader', () => ({
  CONCENTRIC_CIRCLE_LOADER: {
    v: '4.10.1',
    fr: 60,
    ip: 0,
    op: 120,
    w: 800,
    h: 800,
    nm: 'loading_animation',
    // Simplified mock object
  },
}));

// Mock the loader image
vi.mock('@/assets/pngs/loader.png', () => ({
  default: '/mock-loader-image.png',
}));

// Mock the LoadingText constant
vi.mock('@/lib/constant', () => ({
  LoadingText: {
    LOADING_1: 'loading.hangTight',
    LOADING_2: 'loading.almostThere',
  },
}));

describe('EditorLoader', () => {
  beforeEach(() => {
    vi.useFakeTimers();
  });

  afterEach(() => {
    vi.runOnlyPendingTimers();
    vi.useRealTimers();
  });

  describe('Basic Rendering', () => {
    it('should render all main components', () => {
      act(() => {
        render(<EditorLoader />);
      });

      // Check for main container
      const container = screen.getByRole('img', { name: /loader/i }).parentElement;
      expect(container).toHaveClass(
        'relative',
        'flex',
        'flex-col',
        'justify-center',
        'items-center',
        'w-screen',
        'h-screen'
      );

      // Check for loader image
      expect(screen.getByRole('img', { name: /loader/i })).toBeInTheDocument();

      // Check for lottie player
      expect(screen.getByTestId('lottie-player')).toBeInTheDocument();

      // Check for loading text
      expect(screen.getByText('Hang tight...')).toBeInTheDocument();
    });

    it('should render loader image with correct properties', () => {
      act(() => {
        render(<EditorLoader />);
      });

      const loaderImage = screen.getByRole('img', { name: /loader/i });
      expect(loaderImage).toHaveAttribute('src', '/mock-loader-image.png');
      expect(loaderImage).toHaveAttribute('alt', 'Loader');
    });

    it('should render lottie player with correct properties', () => {
      act(() => {
        render(<EditorLoader />);
      });

      const lottiePlayer = screen.getByTestId('lottie-player');
      expect(lottiePlayer).toHaveAttribute('data-autoplay', 'true');
      expect(lottiePlayer).toHaveAttribute('data-loop', 'true');

      // Check that it has the correct lottie source
      const srcData = lottiePlayer.getAttribute('data-src');
      expect(srcData).toBeTruthy();
      const parsedSrc = JSON.parse(srcData!);
      expect(parsedSrc).toHaveProperty('v', '4.10.1');
      expect(parsedSrc).toHaveProperty('nm', 'loading_animation');
    });

    it('should display initial loading text', () => {
      act(() => {
        render(<EditorLoader />);
      });

      expect(screen.getByText('Hang tight...')).toBeInTheDocument();
      expect(screen.queryByText('Almost there!')).not.toBeInTheDocument();
    });
  });

  describe('Text State Management', () => {
    it('should change text after 2 seconds', async () => {
      act(() => {
        render(<EditorLoader />);
      });

      // Initially shows first loading text
      expect(screen.getByText('Hang tight...')).toBeInTheDocument();
      expect(screen.queryByText('Almost there!')).not.toBeInTheDocument();

      // Fast-forward 2 seconds
      act(() => {
        vi.advanceTimersByTime(2000);
      });

      // Should now show second loading text
      expect(screen.getByText('Almost there!')).toBeInTheDocument();
      expect(screen.queryByText('Hang tight...')).not.toBeInTheDocument();
    });

    it('should not change text before 2 seconds', () => {
      act(() => {
        render(<EditorLoader />);
      });

      // Initially shows first loading text
      expect(screen.getByText('Hang tight...')).toBeInTheDocument();

      // Fast-forward 1.9 seconds (just before 2 seconds)
      act(() => {
        vi.advanceTimersByTime(1900);
      });

      // Should still show first loading text
      expect(screen.getByText('Hang tight...')).toBeInTheDocument();
      expect(screen.queryByText('Almost there!')).not.toBeInTheDocument();
    });

    it('should handle multiple renders without duplicating timeouts', () => {
      let component: ReturnType<typeof render>;
      act(() => {
        component = render(<EditorLoader />);
      });

      // Initial state
      expect(screen.getByText('Hang tight...')).toBeInTheDocument();

      // Rerender the component
      act(() => {
        component.rerender(<EditorLoader />);
      });

      // Fast-forward 2 seconds
      act(() => {
        vi.advanceTimersByTime(2000);
      });

      // Should still work correctly
      expect(screen.getByText('Almost there!')).toBeInTheDocument();
    });
  });

  describe('CSS Classes and Styling', () => {
    it('should apply correct classes to main container', () => {
      act(() => {
        render(<EditorLoader />);
      });

      const container = screen.getByRole('img', { name: /loader/i }).parentElement;
      expect(container).toHaveClass(
        'relative',
        'flex',
        'flex-col',
        'justify-center',
        'items-center',
        'w-screen',
        'h-screen'
      );
    });

    it('should apply correct classes to loader image', () => {
      act(() => {
        render(<EditorLoader />);
      });

      const loaderImage = screen.getByRole('img', { name: /loader/i });
      expect(loaderImage).toHaveClass(
        'absolute',
        'h-[125px]',
        'top-[calc(50%+35px)]',
        'left-1/2',
        'transform',
        '-translate-x-1/2',
        '-translate-y-1/2',
        'z-10'
      );
    });

    it('should apply correct classes to lottie player', () => {
      act(() => {
        render(<EditorLoader />);
      });

      const lottiePlayer = screen.getByTestId('lottie-player');
      expect(lottiePlayer).toHaveClass(
        'absolute',
        'h-[300px]',
        'w-[300px]',
        'top-1/2',
        'left-1/2',
        'transform',
        '-translate-x-1/2',
        '-translate-y-1/2',
        'z-1'
      );
    });

    it('should apply correct classes to loading text', () => {
      act(() => {
        render(<EditorLoader />);
      });

      const loadingText = screen.getByText('Hang tight...');
      expect(loadingText).toHaveClass(
        'absolute',
        'top-[calc(50%+150px)]',
        'left-1/2',
        'transform',
        '-translate-x-1/2',
        '-translate-y-1/2',
        'text-center',
        'text-md'
      );
    });
  });

  describe('Translation Integration', () => {
    it('should use translated text for loading messages', () => {
      act(() => {
        render(<EditorLoader />);
      });

      // Should display translated version of LOADING_1
      expect(screen.getByText('Hang tight...')).toBeInTheDocument();

      // After timeout, should display translated version of LOADING_2
      act(() => {
        vi.advanceTimersByTime(2000);
      });

      expect(screen.getByText('Almost there!')).toBeInTheDocument();
    });

    it('should handle translation function correctly', () => {
      // This tests that the component is properly calling the t function
      act(() => {
        render(<EditorLoader />);
      });

      // The mock translation function should be called with the correct keys
      expect(screen.getByText('Hang tight...')).toBeInTheDocument();
    });
  });

  describe('Component Structure and Layout', () => {
    it('should maintain proper z-index layering', () => {
      act(() => {
        render(<EditorLoader />);
      });

      const loaderImage = screen.getByRole('img', { name: /loader/i });
      const lottiePlayer = screen.getByTestId('lottie-player');

      // Image should be on top (z-10) and lottie behind (z-1)
      expect(loaderImage).toHaveClass('z-10');
      expect(lottiePlayer).toHaveClass('z-1');
    });

    it('should center all elements properly', () => {
      act(() => {
        render(<EditorLoader />);
      });

      const loaderImage = screen.getByRole('img', { name: /loader/i });
      const lottiePlayer = screen.getByTestId('lottie-player');
      const loadingText = screen.getByText('Hang tight...');

      // All elements should have centering classes
      [loaderImage, lottiePlayer, loadingText].forEach(element => {
        expect(element).toHaveClass(
          'absolute',
          'left-1/2',
          'transform',
          '-translate-x-1/2',
          '-translate-y-1/2'
        );
      });
    });

    it('should position elements at different vertical positions', () => {
      act(() => {
        render(<EditorLoader />);
      });

      const loaderImage = screen.getByRole('img', { name: /loader/i });
      const lottiePlayer = screen.getByTestId('lottie-player');
      const loadingText = screen.getByText('Hang tight...');

      // Each element should have different top positioning
      expect(loaderImage).toHaveClass('top-[calc(50%+35px)]');
      expect(lottiePlayer).toHaveClass('top-1/2');
      expect(loadingText).toHaveClass('top-[calc(50%+150px)]');
    });
  });

  describe('Lottie Player Configuration', () => {
    it('should configure lottie player with autoplay and loop', () => {
      act(() => {
        render(<EditorLoader />);
      });

      const lottiePlayer = screen.getByTestId('lottie-player');
      expect(lottiePlayer).toHaveAttribute('data-autoplay', 'true');
      expect(lottiePlayer).toHaveAttribute('data-loop', 'true');
    });

    it('should use the correct lottie animation source', () => {
      act(() => {
        render(<EditorLoader />);
      });

      const lottiePlayer = screen.getByTestId('lottie-player');
      const srcData = lottiePlayer.getAttribute('data-src');
      expect(srcData).toBeTruthy();

      const parsedSrc = JSON.parse(srcData!);
      expect(parsedSrc).toMatchObject({
        v: '4.10.1',
        fr: 60,
        ip: 0,
        op: 120,
        w: 800,
        h: 800,
        nm: 'loading_animation',
      });
    });

    it('should have correct size for lottie player', () => {
      act(() => {
        render(<EditorLoader />);
      });

      const lottiePlayer = screen.getByTestId('lottie-player');
      expect(lottiePlayer).toHaveClass('h-[300px]', 'w-[300px]');
    });
  });

  describe('Image Handling', () => {
    it('should load the correct loader image', () => {
      act(() => {
        render(<EditorLoader />);
      });

      const loaderImage = screen.getByRole('img', { name: /loader/i });
      expect(loaderImage).toHaveAttribute('src', '/mock-loader-image.png');
      expect(loaderImage).toHaveAttribute('alt', 'Loader');
    });

    it('should have correct dimensions for loader image', () => {
      act(() => {
        render(<EditorLoader />);
      });

      const loaderImage = screen.getByRole('img', { name: /loader/i });
      expect(loaderImage).toHaveClass('h-[125px]');
    });
  });

  describe('Edge Cases and Error Handling', () => {
    it('should handle component unmounting before timeout', () => {
      let unmount: (() => void) | undefined;
      act(() => {
        const result = render(<EditorLoader />);
        unmount = result.unmount;
      });

      // Unmount before timeout
      unmount!();

      // Fast-forward time - should not cause errors
      act(() => {
        vi.advanceTimersByTime(2000);
      });

      // No errors should be thrown
      expect(true).toBe(true);
    });

    it('should handle rapid re-renders gracefully', () => {
      let rerender: ReturnType<typeof render>['rerender'] | undefined;
      act(() => {
        const result = render(<EditorLoader />);
        rerender = result.rerender;
      });

      // Multiple rapid re-renders
      for (let i = 0; i < 5; i++) {
        rerender!(<EditorLoader />);
      }

      expect(screen.getByText('Hang tight...')).toBeInTheDocument();

      // Timeout should still work
      act(() => {
        vi.advanceTimersByTime(2000);
      });

      expect(screen.getByText('Almost there!')).toBeInTheDocument();
    });

    it('should maintain state consistency during timeout transition', () => {
      act(() => {
        render(<EditorLoader />);
      });

      // Verify initial state
      expect(screen.getByText('Hang tight...')).toBeInTheDocument();

      // Fast-forward to just before timeout
      act(() => {
        vi.advanceTimersByTime(1999);
      });

      expect(screen.getByText('Hang tight...')).toBeInTheDocument();

      // Complete the timeout
      act(() => {
        vi.advanceTimersByTime(1);
      });

      expect(screen.getByText('Almost there!')).toBeInTheDocument();
    });
  });

  describe('Full Screen Layout', () => {
    it('should occupy full screen dimensions', () => {
      act(() => {
        render(<EditorLoader />);
      });

      const container = screen.getByRole('img', { name: /loader/i }).parentElement;
      expect(container).toHaveClass('w-screen', 'h-screen');
    });

    it('should use flexbox for centering', () => {
      act(() => {
        render(<EditorLoader />);
      });

      const container = screen.getByRole('img', { name: /loader/i }).parentElement;
      expect(container).toHaveClass('flex', 'flex-col', 'justify-center', 'items-center');
    });
  });

  describe('Accessibility', () => {
    it('should have proper alt text for the loader image', () => {
      act(() => {
        render(<EditorLoader />);
      });

      const loaderImage = screen.getByRole('img', { name: /loader/i });
      expect(loaderImage).toHaveAttribute('alt', 'Loader');
    });

    it('should display loading text for screen readers', () => {
      act(() => {
        render(<EditorLoader />);
      });

      const loadingText = screen.getByText('Hang tight...');
      expect(loadingText).toBeInTheDocument();
      expect(loadingText.tagName).toBe('P');
    });

    it('should update loading text for accessibility', () => {
      act(() => {
        render(<EditorLoader />);
      });

      // Initial accessible text
      expect(screen.getByText('Hang tight...')).toBeInTheDocument();

      // Updated accessible text after timeout
      act(() => {
        vi.advanceTimersByTime(2000);
      });

      expect(screen.getByText('Almost there!')).toBeInTheDocument();
    });
  });
});
