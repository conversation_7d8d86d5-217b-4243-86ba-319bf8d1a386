import React from 'react';
import { render, screen } from '@testing-library/react';
import { describe, it, expect, vi } from 'vitest';
import TailwindWrapper from '../TailwindWrapper';

// Mock cn utility
vi.mock('@/lib/utils', () => ({
  cn: (...classes: string[]) => classes.filter(Boolean).join(' '),
}));

describe('TailwindWrapper', () => {
  describe('Basic Rendering', () => {
    it('should render children', () => {
      render(
        <TailwindWrapper>
          <div data-testid="test-child">Test Content</div>
        </TailwindWrapper>
      );

      expect(screen.getByTestId('test-child')).toBeInTheDocument();
      expect(screen.getByText('Test Content')).toBeInTheDocument();
    });

    it('should apply default classes with fullscreen enabled', () => {
      render(
        <TailwindWrapper>
          <div>Test</div>
        </TailwindWrapper>
      );

      const wrapper = screen.getByText('Test').parentElement;
      expect(wrapper).toHaveClass('mfe-app', 'h-full', 'w-full');
    });

    it('should apply default classes without fullscreen when disabled', () => {
      render(
        <TailwindWrapper fullscreen={false}>
          <div>Test</div>
        </TailwindWrapper>
      );

      const wrapper = screen.getByText('Test').parentElement;
      expect(wrapper).toHaveClass('mfe-app');
      expect(wrapper).not.toHaveClass('h-full', 'w-full');
    });

    it('should apply custom className alongside defaults', () => {
      render(
        <TailwindWrapper className="custom-class">
          <div>Test</div>
        </TailwindWrapper>
      );

      const wrapper = screen.getByText('Test').parentElement;
      expect(wrapper).toHaveClass('mfe-app', 'h-full', 'w-full', 'custom-class');
    });

    it('should combine custom className with non-fullscreen mode', () => {
      render(
        <TailwindWrapper className="custom-class" fullscreen={false}>
          <div>Test</div>
        </TailwindWrapper>
      );

      const wrapper = screen.getByText('Test').parentElement;
      expect(wrapper).toHaveClass('mfe-app', 'custom-class');
      expect(wrapper).not.toHaveClass('h-full', 'w-full');
    });
  });

  describe('forwardRef Functionality', () => {
    it('should forward ref correctly', () => {
      const ref = React.createRef<HTMLDivElement>();

      render(
        <TailwindWrapper ref={ref}>
          <div>Test Content</div>
        </TailwindWrapper>
      );

      expect(ref.current).toBeInstanceOf(HTMLDivElement);
      expect(ref.current).toHaveClass('mfe-app');
      expect(ref.current).toHaveTextContent('Test Content');
    });
  });

  describe('Children Handling', () => {
    it('should render multiple children', () => {
      render(
        <TailwindWrapper>
          <div data-testid="child-1">Child 1</div>
          <div data-testid="child-2">Child 2</div>
          <span data-testid="child-3">Child 3</span>
        </TailwindWrapper>
      );

      expect(screen.getByTestId('child-1')).toBeInTheDocument();
      expect(screen.getByTestId('child-2')).toBeInTheDocument();
      expect(screen.getByTestId('child-3')).toBeInTheDocument();
    });

    it('should render complex nested children', () => {
      render(
        <TailwindWrapper>
          <div>
            <header>Header</header>
            <main>
              <section>Content</section>
            </main>
            <footer>Footer</footer>
          </div>
        </TailwindWrapper>
      );

      expect(screen.getByText('Header')).toBeInTheDocument();
      expect(screen.getByText('Content')).toBeInTheDocument();
      expect(screen.getByText('Footer')).toBeInTheDocument();
    });
  });
});
