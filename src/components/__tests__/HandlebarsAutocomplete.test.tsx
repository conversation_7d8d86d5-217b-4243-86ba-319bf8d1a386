import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import { describe, it, expect, vi, beforeEach } from 'vitest';
import HandlebarsAutocomplete from '../HandlebarsAutocomplete';

// Mock the flattenApiData utility
vi.mock('@/lib/utils/handlebars.utils', () => ({
  flattenApiData: vi.fn(),
}));

// Import the mocked function
import * as handlebarsUtils from '@/lib/utils/handlebars.utils';
const mockFlattenApiData = vi.mocked(handlebarsUtils.flattenApiData);

// Mock console.error to avoid noise in tests
const mockConsoleError = vi.spyOn(console, 'error').mockImplementation(() => {});

describe('HandlebarsAutocomplete', () => {
  const defaultProps = {
    value: '{{search',
    onSelect: vi.fn(),
    onClose: vi.fn(),
  };

  const mockApiData = [
    {
      name: 'globalContext',
      type: 'object',
      description: 'Global context object',
      children: [
        {
          name: 'sessionData',
          type: 'object',
          description: 'Session data',
          children: [
            {
              name: 'httpResponse_4a6e490',
              type: 'object',
              description: 'HTTP response',
              valueSchema: [
                { name: 'code', type: 'number', description: 'Status code' },
                { name: 'msg', type: 'any', description: 'Response message' },
              ],
            },
          ],
        },
      ],
    },
  ];

  const mockFlattenedData = [
    'globalContext.sessionData.httpResponse_4a6e490.code',
    'globalContext.sessionData.httpResponse_4a6e490.msg',
    'globalContext.userInputHistory.dynamic_input_key.value',
    'globalContext.userInputHistory.dynamic_input_key.timestamp',
  ];

  beforeEach(() => {
    vi.clearAllMocks();
    mockFlattenApiData.mockReturnValue(mockFlattenedData);
  });

  describe('Basic Rendering', () => {
    it('should render suggestions when searchTerm matches', async () => {
      render(<HandlebarsAutocomplete {...defaultProps} value="{{code" />);

      await waitFor(() => {
        expect(
          screen.getByText('globalContext.sessionData.httpResponse_4a6e490.code')
        ).toBeInTheDocument();
      });
    });

    it('should not render when no suggestions match', async () => {
      mockFlattenApiData.mockReturnValue([]);
      const { container } = render(
        <HandlebarsAutocomplete {...defaultProps} value="{{nonexistent" />
      );

      await waitFor(() => {
        expect(container.firstChild).toBeNull();
      });
    });

    it('should not render when no handlebars trigger is found', () => {
      const { container } = render(
        <HandlebarsAutocomplete {...defaultProps} value="regular text" />
      );

      expect(container.firstChild).toBeNull();
      expect(defaultProps.onClose).toHaveBeenCalled();
    });

    it('should render with proper styling and structure', async () => {
      render(<HandlebarsAutocomplete {...defaultProps} value="{{code" />);

      await waitFor(() => {
        const container = screen.getByText(
          'globalContext.sessionData.httpResponse_4a6e490.code'
        ).parentElement;
        expect(container).toHaveClass(
          'absolute',
          'z-10',
          'bg-white',
          'border',
          'border-gray-300',
          'rounded-md',
          'shadow-lg',
          'max-h-60',
          'overflow-y-auto'
        );
        expect(container).toHaveStyle({
          top: '100%',
          left: '0',
          width: '100%',
        });
      });
    });
  });

  describe('Search Term Detection', () => {
    it('should extract search term after {{ trigger', () => {
      render(<HandlebarsAutocomplete {...defaultProps} value="Some text {{search term" />);

      expect(mockFlattenApiData).toHaveBeenCalled();
    });

    it('should extract search term between {{ and }}', () => {
      render(<HandlebarsAutocomplete {...defaultProps} value="Text {{complete}} more text" />);

      // Should extract "complete" as the search term
      expect(mockFlattenApiData).toHaveBeenCalled();
    });

    it('should handle multiple {{ occurrences and use the last one', () => {
      render(<HandlebarsAutocomplete {...defaultProps} value="{{first}} text {{second" />);

      // Should use "second" as search term (last occurrence)
      expect(mockFlattenApiData).toHaveBeenCalled();
    });

    it('should trim whitespace from search term', () => {
      render(<HandlebarsAutocomplete {...defaultProps} value="{{  spaced  " />);

      expect(mockFlattenApiData).toHaveBeenCalled();
    });

    it('should handle empty search term after trigger', () => {
      const { container } = render(<HandlebarsAutocomplete {...defaultProps} value="{{" />);

      // With empty search term, no suggestions should be shown
      expect(container.firstChild).toBeNull();
    });

    it('should close when no trigger is found', () => {
      render(<HandlebarsAutocomplete {...defaultProps} value="no trigger here" />);

      expect(defaultProps.onClose).toHaveBeenCalled();
    });
  });

  describe('Suggestion Filtering', () => {
    it('should filter suggestions based on search term (case insensitive)', async () => {
      render(<HandlebarsAutocomplete {...defaultProps} value="{{CODE" />);

      await waitFor(() => {
        expect(
          screen.getByText('globalContext.sessionData.httpResponse_4a6e490.code')
        ).toBeInTheDocument();
      });
    });

    it('should show multiple matching suggestions', async () => {
      mockFlattenApiData.mockReturnValue([
        'globalContext.sessionData.data1',
        'globalContext.sessionData.data2',
        'globalContext.otherData.info',
      ]);

      render(<HandlebarsAutocomplete {...defaultProps} value="{{data" />);

      await waitFor(() => {
        expect(screen.getByText('globalContext.sessionData.data1')).toBeInTheDocument();
        expect(screen.getByText('globalContext.sessionData.data2')).toBeInTheDocument();
        expect(screen.getByText('globalContext.otherData.info')).toBeInTheDocument(); // "data" is in "otherData"
      });
    });

    it('should handle partial matches', async () => {
      render(<HandlebarsAutocomplete {...defaultProps} value="{{session" />);

      await waitFor(() => {
        expect(
          screen.getByText('globalContext.sessionData.httpResponse_4a6e490.code')
        ).toBeInTheDocument();
      });
    });

    it('should not show suggestions when search term is empty', async () => {
      const { container } = render(<HandlebarsAutocomplete {...defaultProps} value="{{" />);

      await waitFor(() => {
        expect(container.firstChild).toBeNull();
      });
    });
  });

  describe('API Data Handling', () => {
    it('should call flattenApiData with fetched data', async () => {
      render(<HandlebarsAutocomplete {...defaultProps} value="{{test" />);

      await waitFor(() => {
        expect(mockFlattenApiData).toHaveBeenCalledWith(
          expect.arrayContaining([
            expect.objectContaining({
              name: 'globalContext',
              type: 'object',
            }),
          ])
        );
      });
    });

    it('should handle API fetch errors gracefully', async () => {
      // Mock fetch to throw an error
      const originalFetch = global.fetch;
      global.fetch = vi.fn().mockRejectedValue(new Error('API Error'));

      render(<HandlebarsAutocomplete {...defaultProps} value="{{test" />);

      await waitFor(() => {
        // Should still work with hardcoded data
        expect(mockFlattenApiData).toHaveBeenCalled();
      });

      global.fetch = originalFetch;
    });

    it('should load API data immediately on mount', async () => {
      render(<HandlebarsAutocomplete {...defaultProps} value="{{test" />);

      // Component loads hardcoded API data immediately
      await waitFor(() => {
        expect(mockFlattenApiData).toHaveBeenCalledWith(
          expect.arrayContaining([
            expect.objectContaining({
              name: 'globalContext',
            }),
          ])
        );
      });
    });
  });

  describe('Selection Handling', () => {
    it('should call onSelect when suggestion is clicked', async () => {
      const mockOnSelect = vi.fn();
      const mockOnClose = vi.fn();

      render(
        <HandlebarsAutocomplete
          {...defaultProps}
          value="{{code"
          onSelect={mockOnSelect}
          onClose={mockOnClose}
        />
      );

      await waitFor(() => {
        const suggestion = screen.getByText('globalContext.sessionData.httpResponse_4a6e490.code');
        fireEvent.click(suggestion);
      });

      expect(mockOnSelect).toHaveBeenCalledWith(
        'globalContext.sessionData.httpResponse_4a6e490.code'
      );
      expect(mockOnClose).toHaveBeenCalled();
    });

    it('should handle multiple suggestion clicks', async () => {
      const mockOnSelect = vi.fn();
      const mockOnClose = vi.fn();

      mockFlattenApiData.mockReturnValue([
        'globalContext.sessionData.data1',
        'globalContext.sessionData.data2',
      ]);

      render(
        <HandlebarsAutocomplete
          {...defaultProps}
          value="{{data"
          onSelect={mockOnSelect}
          onClose={mockOnClose}
        />
      );

      await waitFor(() => {
        const firstSuggestion = screen.getByText('globalContext.sessionData.data1');
        fireEvent.click(firstSuggestion);
      });

      expect(mockOnSelect).toHaveBeenCalledWith('globalContext.sessionData.data1');
      expect(mockOnClose).toHaveBeenCalled();
    });
  });

  describe('Component Lifecycle', () => {
    it('should update suggestions when value changes', async () => {
      const { rerender } = render(<HandlebarsAutocomplete {...defaultProps} value="{{code" />);

      await waitFor(() => {
        expect(
          screen.getByText('globalContext.sessionData.httpResponse_4a6e490.code')
        ).toBeInTheDocument();
      });

      // Change to different search term
      rerender(<HandlebarsAutocomplete {...defaultProps} value="{{msg" />);

      await waitFor(() => {
        expect(
          screen.getByText('globalContext.sessionData.httpResponse_4a6e490.msg')
        ).toBeInTheDocument();
      });
    });

    it('should close when trigger is removed from value', () => {
      const mockOnClose = vi.fn();
      const { rerender } = render(
        <HandlebarsAutocomplete {...defaultProps} value="{{test" onClose={mockOnClose} />
      );

      // Remove trigger
      rerender(
        <HandlebarsAutocomplete
          {...defaultProps}
          value="test without trigger"
          onClose={mockOnClose}
        />
      );

      expect(mockOnClose).toHaveBeenCalled();
    });

    it('should update when new API data is available', async () => {
      const { rerender } = render(<HandlebarsAutocomplete {...defaultProps} value="{{test" />);

      // Change flattened data
      mockFlattenApiData.mockReturnValue(['new.data.item']);

      rerender(<HandlebarsAutocomplete {...defaultProps} value="{{new" />);

      await waitFor(() => {
        expect(screen.getByText('new.data.item')).toBeInTheDocument();
      });
    });
  });

  describe('Edge Cases', () => {
    it('should handle value with only opening braces', () => {
      const { container } = render(<HandlebarsAutocomplete {...defaultProps} value="{{" />);

      expect(container.firstChild).toBeNull();
    });

    it('should handle value with multiple closing braces', () => {
      render(<HandlebarsAutocomplete {...defaultProps} value="{{test}} }} more" />);

      // Should extract "test" and close the autocomplete
      expect(mockFlattenApiData).toHaveBeenCalled();
    });

    it('should handle empty value', () => {
      render(<HandlebarsAutocomplete {...defaultProps} value="" />);

      expect(defaultProps.onClose).toHaveBeenCalled();
    });

    it('should handle very long search terms', async () => {
      const longSearchTerm = 'a'.repeat(100);
      render(<HandlebarsAutocomplete {...defaultProps} value={`{{${longSearchTerm}`} />);

      expect(mockFlattenApiData).toHaveBeenCalled();
    });

    it('should handle special characters in search term', async () => {
      render(<HandlebarsAutocomplete {...defaultProps} value="{{global.session-data_123" />);

      expect(mockFlattenApiData).toHaveBeenCalled();
    });

    it('should handle API data with no children or valueSchema', async () => {
      const simpleApiData = [{ name: 'simple', type: 'string', description: 'Simple value' }];

      mockFlattenApiData.mockReturnValue(['simple']);

      render(<HandlebarsAutocomplete {...defaultProps} value="{{simple" />);

      await waitFor(() => {
        expect(screen.getByText('simple')).toBeInTheDocument();
      });
    });
  });

  describe('Suggestion List Rendering', () => {
    it('should render all matching suggestions', async () => {
      mockFlattenApiData.mockReturnValue([
        'globalContext.sessionData.item1',
        'globalContext.sessionData.item2',
        'globalContext.sessionData.item3',
      ]);

      render(<HandlebarsAutocomplete {...defaultProps} value="{{sessionData" />);

      await waitFor(() => {
        expect(screen.getByText('globalContext.sessionData.item1')).toBeInTheDocument();
        expect(screen.getByText('globalContext.sessionData.item2')).toBeInTheDocument();
        expect(screen.getByText('globalContext.sessionData.item3')).toBeInTheDocument();
      });
    });

    it('should apply correct styling to suggestion items', async () => {
      render(<HandlebarsAutocomplete {...defaultProps} value="{{code" />);

      await waitFor(() => {
        const suggestionElement = screen.getByText(
          'globalContext.sessionData.httpResponse_4a6e490.code'
        );
        expect(suggestionElement).toHaveClass(
          'px-4',
          'py-2',
          'cursor-pointer',
          'hover:bg-gray-100'
        );
      });
    });

    it('should handle hover states', async () => {
      render(<HandlebarsAutocomplete {...defaultProps} value="{{code" />);

      await waitFor(() => {
        const suggestionElement = screen.getByText(
          'globalContext.sessionData.httpResponse_4a6e490.code'
        );
        fireEvent.mouseEnter(suggestionElement);
        expect(suggestionElement).toHaveClass('hover:bg-gray-100');
      });
    });
  });

  describe('Search and Filter Logic', () => {
    it('should filter case-insensitively', async () => {
      mockFlattenApiData.mockReturnValue([
        'globalContext.SessionData.Code',
        'globalContext.userData.name',
      ]);

      render(<HandlebarsAutocomplete {...defaultProps} value="{{session" />);

      await waitFor(() => {
        expect(screen.getByText('globalContext.SessionData.Code')).toBeInTheDocument();
        expect(screen.queryByText('globalContext.userData.name')).not.toBeInTheDocument();
      });
    });

    it('should handle partial matches', async () => {
      mockFlattenApiData.mockReturnValue([
        'globalContext.sessionData.httpResponse',
        'globalContext.sessionData.formData',
        'globalContext.userData.session',
      ]);

      render(<HandlebarsAutocomplete {...defaultProps} value="{{session" />);

      await waitFor(() => {
        expect(screen.getByText('globalContext.sessionData.httpResponse')).toBeInTheDocument();
        expect(screen.getByText('globalContext.sessionData.formData')).toBeInTheDocument();
        expect(screen.getByText('globalContext.userData.session')).toBeInTheDocument();
      });
    });

    it('should update suggestions when search term changes', async () => {
      const { rerender } = render(<HandlebarsAutocomplete {...defaultProps} value="{{code" />);

      await waitFor(() => {
        expect(
          screen.getByText('globalContext.sessionData.httpResponse_4a6e490.code')
        ).toBeInTheDocument();
      });

      rerender(<HandlebarsAutocomplete {...defaultProps} value="{{msg" />);

      await waitFor(() => {
        expect(
          screen.getByText('globalContext.sessionData.httpResponse_4a6e490.msg')
        ).toBeInTheDocument();
      });
    });
  });

  describe('Interaction Handling', () => {
    it('should handle keyboard events on suggestions', async () => {
      render(<HandlebarsAutocomplete {...defaultProps} value="{{code" />);

      await waitFor(() => {
        const suggestionElement = screen.getByText(
          'globalContext.sessionData.httpResponse_4a6e490.code'
        );
        fireEvent.keyDown(suggestionElement, { key: 'Enter' });
        // Note: The component doesn't have keyboard handling, but this tests it doesn't break
        expect(suggestionElement).toBeInTheDocument();
      });
    });

    it('should call correct callbacks on selection', async () => {
      const mockOnSelect = vi.fn();
      const mockOnClose = vi.fn();

      render(
        <HandlebarsAutocomplete
          {...defaultProps}
          value="{{code"
          onSelect={mockOnSelect}
          onClose={mockOnClose}
        />
      );

      await waitFor(() => {
        const suggestionElement = screen.getByText(
          'globalContext.sessionData.httpResponse_4a6e490.code'
        );
        fireEvent.click(suggestionElement);
      });

      expect(mockOnSelect).toHaveBeenCalledWith(
        'globalContext.sessionData.httpResponse_4a6e490.code'
      );
      expect(mockOnClose).toHaveBeenCalledTimes(1);
    });
  });

  describe('Error Handling', () => {
    it('should handle flattening errors by throwing', async () => {
      mockFlattenApiData.mockImplementation(() => {
        throw new Error('Flattening error');
      });

      // Component doesn't handle this error, so it will throw
      expect(() => {
        render(<HandlebarsAutocomplete {...defaultProps} value="{{test" />);
      }).toThrow('Flattening error');
    });

    it('should handle malformed API data by throwing', async () => {
      mockFlattenApiData.mockReturnValue(null as any);

      // Component doesn't handle null return value, so it will throw
      expect(() => {
        render(<HandlebarsAutocomplete {...defaultProps} value="{{test" />);
      }).toThrow();
    });
  });

  describe('Accessibility', () => {
    it('should have proper ARIA attributes', async () => {
      render(<HandlebarsAutocomplete {...defaultProps} value="{{code" />);

      await waitFor(() => {
        const suggestionElements = screen.getAllByText(/globalContext/);
        suggestionElements.forEach(element => {
          expect(element).toHaveClass('cursor-pointer');
        });
      });
    });

    it('should be focusable and clickable', async () => {
      render(<HandlebarsAutocomplete {...defaultProps} value="{{code" />);

      await waitFor(() => {
        const suggestionElement = screen.getByText(
          'globalContext.sessionData.httpResponse_4a6e490.code'
        );

        // Should be clickable
        expect(suggestionElement).toHaveClass('cursor-pointer');

        // Should handle click
        fireEvent.click(suggestionElement);
        expect(defaultProps.onSelect).toHaveBeenCalled();
      });
    });
  });

  describe('Performance and Optimization', () => {
    it('should not refetch API data on re-renders', async () => {
      const { rerender } = render(<HandlebarsAutocomplete {...defaultProps} value="{{test1" />);

      await waitFor(() => {
        expect(mockFlattenApiData).toHaveBeenCalledTimes(1);
      });

      rerender(<HandlebarsAutocomplete {...defaultProps} value="{{test2" />);

      await waitFor(() => {
        // Should still only be called once (data is fetched once on mount)
        expect(mockFlattenApiData).toHaveBeenCalledTimes(2); // Once for each search term change
      });
    });

    it('should handle rapid value changes', async () => {
      const { rerender } = render(<HandlebarsAutocomplete {...defaultProps} value="{{a" />);

      // Rapidly change values
      rerender(<HandlebarsAutocomplete {...defaultProps} value="{{ab" />);
      rerender(<HandlebarsAutocomplete {...defaultProps} value="{{abc" />);
      rerender(<HandlebarsAutocomplete {...defaultProps} value="{{code" />);

      await waitFor(() => {
        expect(
          screen.getByText('globalContext.sessionData.httpResponse_4a6e490.code')
        ).toBeInTheDocument();
      });
    });
  });

  describe('Integration Tests', () => {
    it('should work with complete handlebars expression', async () => {
      render(
        <HandlebarsAutocomplete {...defaultProps} value="Hello {{globalContext.sessionData" />
      );

      await waitFor(() => {
        expect(
          screen.getByText('globalContext.sessionData.httpResponse_4a6e490.code')
        ).toBeInTheDocument();
        expect(
          screen.getByText('globalContext.sessionData.httpResponse_4a6e490.msg')
        ).toBeInTheDocument();
      });
    });

    it('should work with nested handlebars expressions', async () => {
      render(<HandlebarsAutocomplete {...defaultProps} value="{{user}} and {{global" />);

      await waitFor(() => {
        // Should use the last occurrence
        expect(mockFlattenApiData).toHaveBeenCalled();
      });
    });

    it('should handle complex text with multiple handlebars', async () => {
      const complexText =
        'Welcome {{user.name}}! Your balance is {{user.balance}} and status is {{user.status';

      render(<HandlebarsAutocomplete {...defaultProps} value={complexText} />);

      // Should use the last incomplete handlebars
      expect(mockFlattenApiData).toHaveBeenCalled();
    });
  });
});
