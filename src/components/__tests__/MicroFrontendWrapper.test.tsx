import React from 'react';
import { render, screen } from '@testing-library/react';
import { describe, it, expect, vi, beforeEach } from 'vitest';
import MicroFrontendWrapper from '../MicroFrontendWrapper';

// Mock react-i18next
const mockChangeLanguage = vi.fn();
vi.mock('react-i18next', () => ({
  useTranslation: () => ({
    i18n: {
      language: 'en',
      changeLanguage: mockChangeLanguage,
    },
  }),
}));

// Mock Redux Provider
vi.mock('react-redux', () => ({
  Provider: ({ children, store }: any) => (
    <div data-testid="redux-provider" data-store={!!store}>
      {children}
    </div>
  ),
}));

// Mock store
vi.mock('@/store/store', () => ({
  store: { mockStore: true },
}));

// Mock TailwindWrapper
vi.mock('../TailwindWrapper', () => ({
  default: ({ children, className, fullscreen }: any) => (
    <div data-testid="tailwind-wrapper" className={className} data-fullscreen={fullscreen}>
      {children}
    </div>
  ),
}));

// Mock Toaster
vi.mock('../ui/toaster', () => ({
  Toaster: () => <div data-testid="toaster" />,
}));

// Mock localStorage
const mockLocalStorage = {
  getItem: vi.fn(),
  setItem: vi.fn(),
  removeItem: vi.fn(),
  clear: vi.fn(),
};

Object.defineProperty(window, 'localStorage', {
  value: mockLocalStorage,
});

describe('MicroFrontendWrapper', () => {
  beforeEach(() => {
    vi.clearAllMocks();
    mockLocalStorage.getItem.mockReturnValue('en');
  });

  describe('Basic Rendering', () => {
    it('should render children with all wrapper components', () => {
      render(
        <MicroFrontendWrapper>
          <div data-testid="test-child">Test Content</div>
        </MicroFrontendWrapper>
      );

      expect(screen.getByTestId('redux-provider')).toBeInTheDocument();
      expect(screen.getByTestId('tailwind-wrapper')).toBeInTheDocument();
      expect(screen.getByTestId('toaster')).toBeInTheDocument();
      expect(screen.getByTestId('test-child')).toBeInTheDocument();
    });

    it('should pass props to TailwindWrapper', () => {
      render(
        <MicroFrontendWrapper className="custom-class" fullscreen={false}>
          <div>Test</div>
        </MicroFrontendWrapper>
      );

      const tailwindWrapper = screen.getByTestId('tailwind-wrapper');
      expect(tailwindWrapper).toHaveClass('custom-class');
      expect(tailwindWrapper).toHaveAttribute('data-fullscreen', 'false');
    });

    it('should use default fullscreen prop', () => {
      render(
        <MicroFrontendWrapper>
          <div>Test</div>
        </MicroFrontendWrapper>
      );

      const tailwindWrapper = screen.getByTestId('tailwind-wrapper');
      expect(tailwindWrapper).toHaveAttribute('data-fullscreen', 'true');
    });
  });

  describe('Language Handling', () => {
    it('should change language when localStorage differs from current', () => {
      mockLocalStorage.getItem.mockReturnValue('es');

      render(
        <MicroFrontendWrapper>
          <div>Test</div>
        </MicroFrontendWrapper>
      );

      expect(mockChangeLanguage).toHaveBeenCalledWith('es');
    });

    it('should not change language when localStorage matches current', () => {
      mockLocalStorage.getItem.mockReturnValue('en');

      render(
        <MicroFrontendWrapper>
          <div>Test</div>
        </MicroFrontendWrapper>
      );

      expect(mockChangeLanguage).not.toHaveBeenCalled();
    });

    it('should fallback to English when no localStorage value', () => {
      mockLocalStorage.getItem.mockReturnValue(null);

      // When localStorage returns null, the component uses 'en' as fallback
      // but since i18n.language is already 'en', changeLanguage won't be called
      render(
        <MicroFrontendWrapper>
          <div>Test</div>
        </MicroFrontendWrapper>
      );

      // The component should still render properly
      expect(screen.getByTestId('redux-provider')).toBeInTheDocument();
    });
  });
});
