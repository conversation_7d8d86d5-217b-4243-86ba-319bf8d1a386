import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import { describe, it, expect, vi, beforeEach } from 'vitest';
import DropdownButtonWithDivider from '../dropdownWithDivider';
import { DropdownOption } from '@/types';

// Mock react-i18next
vi.mock('react-i18next', () => ({
  useTranslation: () => ({
    t: (key: string) => {
      if (key === 'common.selectOption') return 'Select an option';
      return key;
    },
  }),
}));

// Mock @radix-ui/react-select to make testing easier
vi.mock('@radix-ui/react-select', () => ({
  Root: ({ children, value, onValueChange, disabled, ...props }: any) => (
    <div data-testid="select-root" data-value={value} data-disabled={disabled} {...props}>
      {children}
    </div>
  ),
  Group: ({ children, ...props }: any) => (
    <div data-testid="select-group" {...props}>
      {children}
    </div>
  ),
  Trigger: React.forwardRef<HTMLButtonElement, any>(({ children, className, ...props }, ref) => (
    <button ref={ref} className={className} data-testid="select-trigger" {...props}>
      {children}
    </button>
  )),
  Content: ({ children, ...props }: any) => (
    <div data-testid="select-content" {...props}>
      {children}
    </div>
  ),
  Item: ({ children, value, className, ...props }: any) => (
    <div
      data-testid="select-item"
      data-value={value}
      className={className}
      role="option"
      {...props}
    >
      {children}
    </div>
  ),
  Value: ({ placeholder, ...props }: any) => (
    <span data-testid="select-value" data-placeholder={placeholder} {...props}>
      {placeholder}
    </span>
  ),
  Portal: ({ children }: any) => <div data-testid="select-portal">{children}</div>,
  Viewport: ({ children, className, ...props }: any) => (
    <div data-testid="select-viewport" className={className} {...props}>
      {children}
    </div>
  ),
  ScrollUpButton: (props: any) => <div data-testid="select-scroll-up" {...props} />,
  ScrollDownButton: (props: any) => <div data-testid="select-scroll-down" {...props} />,
  Icon: ({ children, ...props }: any) => (
    <span data-testid="select-icon" {...props}>
      {children}
    </span>
  ),
  ItemIndicator: ({ children, ...props }: any) => (
    <span data-testid="select-item-indicator" {...props}>
      {children}
    </span>
  ),
  ItemText: ({ children, ...props }: any) => (
    <span data-testid="select-item-text" {...props}>
      {children}
    </span>
  ),
  Label: ({ children, className, ...props }: any) => (
    <div data-testid="select-label" className={className} {...props}>
      {children}
    </div>
  ),
  Separator: ({ className, ...props }: any) => (
    <div data-testid="select-separator" className={className} {...props} />
  ),
}));

describe('DropdownButtonWithDivider', () => {
  const mockOnChange = vi.fn();

  const defaultOptions: DropdownOption[] = [
    { value: 'option1', label: 'Option 1' },
    { value: 'option2', label: 'Option 2' },
    { value: 'option3', label: 'Option 3' },
  ];

  const optionsWithIcons: DropdownOption[] = [
    {
      value: 'option1',
      label: 'Option 1',
      icon: <span data-testid="option1-icon">🎯</span>,
    },
    {
      value: 'option2',
      label: 'Option 2',
      icon: <span data-testid="option2-icon">⭐</span>,
    },
  ];

  beforeEach(() => {
    vi.clearAllMocks();
  });

  describe('Basic Rendering', () => {
    it('should render correctly with required props', () => {
      render(
        <DropdownButtonWithDivider
          value="option1"
          onChange={mockOnChange}
          options={defaultOptions}
        />
      );

      expect(screen.getByTestId('select-root')).toBeInTheDocument();
      expect(screen.getByTestId('select-trigger')).toBeInTheDocument();
      expect(screen.getByTestId('select-value')).toBeInTheDocument();
    });

    it('should render with default placeholder when no custom placeholder is provided', () => {
      render(
        <DropdownButtonWithDivider value="" onChange={mockOnChange} options={defaultOptions} />
      );

      expect(screen.getByTestId('select-value')).toHaveAttribute(
        'data-placeholder',
        'Select an option'
      );
    });

    it('should render with custom placeholder when provided', () => {
      const customPlaceholder = 'Choose your option';
      render(
        <DropdownButtonWithDivider
          value=""
          onChange={mockOnChange}
          options={defaultOptions}
          placeholder={customPlaceholder}
        />
      );

      expect(screen.getByTestId('select-value')).toHaveAttribute(
        'data-placeholder',
        customPlaceholder
      );
    });

    it('should render with selected value', () => {
      render(
        <DropdownButtonWithDivider
          value="option2"
          onChange={mockOnChange}
          options={defaultOptions}
        />
      );

      expect(screen.getByTestId('select-root')).toHaveAttribute('data-value', 'option2');
    });

    it('should render all options correctly', () => {
      render(
        <DropdownButtonWithDivider value="" onChange={mockOnChange} options={defaultOptions} />
      );

      const items = screen.getAllByTestId('select-item');
      expect(items).toHaveLength(3);
      expect(items[0]).toHaveAttribute('data-value', 'option1');
      expect(items[1]).toHaveAttribute('data-value', 'option2');
      expect(items[2]).toHaveAttribute('data-value', 'option3');
    });
  });

  describe('Props Handling', () => {
    it('should apply custom className to wrapper div', () => {
      const customClass = 'custom-dropdown-class';
      render(
        <DropdownButtonWithDivider
          value=""
          onChange={mockOnChange}
          options={defaultOptions}
          className={customClass}
        />
      );

      const wrapper = screen.getByTestId('select-root').parentElement;
      expect(wrapper).toHaveClass('relative', customClass);
    });

    it('should apply triggerClassName to SelectTrigger', () => {
      const triggerClass = 'custom-trigger-class';
      render(
        <DropdownButtonWithDivider
          value=""
          onChange={mockOnChange}
          options={defaultOptions}
          triggerClassName={triggerClass}
        />
      );

      expect(screen.getByTestId('select-trigger')).toHaveClass(triggerClass);
    });

    it('should render icon when provided', () => {
      const iconTestId = 'custom-icon';
      render(
        <DropdownButtonWithDivider
          value=""
          onChange={mockOnChange}
          options={defaultOptions}
          icon={<span data-testid={iconTestId}>📦</span>}
        />
      );

      expect(screen.getByTestId(iconTestId)).toBeInTheDocument();
    });

    it('should handle disabled state', () => {
      render(
        <DropdownButtonWithDivider
          value=""
          onChange={mockOnChange}
          options={defaultOptions}
          disabled
        />
      );

      expect(screen.getByTestId('select-root')).toHaveAttribute('data-disabled', 'true');
    });

    it('should render icon after SelectValue in trigger', () => {
      const iconTestId = 'trigger-icon';
      render(
        <DropdownButtonWithDivider
          value=""
          onChange={mockOnChange}
          options={defaultOptions}
          icon={<span data-testid={iconTestId}>📦</span>}
        />
      );

      const trigger = screen.getByTestId('select-trigger');
      const selectValue = screen.getByTestId('select-value');
      const icon = screen.getByTestId(iconTestId);

      // Check that both elements are present in the trigger
      expect(trigger).toContainElement(selectValue);
      expect(trigger).toContainElement(icon);
    });
  });

  describe('Divider and Layout Structure', () => {
    it('should render options with proper divider structure', () => {
      render(
        <DropdownButtonWithDivider value="" onChange={mockOnChange} options={optionsWithIcons} />
      );

      const items = screen.getAllByTestId('select-item');
      expect(items).toHaveLength(2);

      // Check that each item has the expected structure
      items.forEach(item => {
        // Should have the main flex container
        const flexContainer = item.querySelector('.flex.gap-3.items-center');
        expect(flexContainer).toBeInTheDocument();
      });
    });

    it('should render label section with correct styling', () => {
      render(
        <DropdownButtonWithDivider value="" onChange={mockOnChange} options={defaultOptions} />
      );

      const items = screen.getAllByTestId('select-item');
      items.forEach(item => {
        // Check for label section with specific classes
        const labelSection = item.querySelector('.text-tertiary-500.text-sm.min-w-20');
        expect(labelSection).toBeInTheDocument();
      });
    });

    it('should render divider with correct styling', () => {
      render(
        <DropdownButtonWithDivider value="" onChange={mockOnChange} options={defaultOptions} />
      );

      const items = screen.getAllByTestId('select-item');
      items.forEach(item => {
        // Check for divider with specific classes
        const divider = item.querySelector('.w-px.h-5.bg-tertiary-300');
        expect(divider).toBeInTheDocument();
      });
    });

    it('should render icon section with correct margin', () => {
      render(
        <DropdownButtonWithDivider value="" onChange={mockOnChange} options={optionsWithIcons} />
      );

      const items = screen.getAllByTestId('select-item');
      items.forEach(item => {
        // Check for icon section with ml-3 class
        const iconSection = item.querySelector('.ml-3');
        expect(iconSection).toBeInTheDocument();
      });
    });

    it('should render option icons when provided', () => {
      render(
        <DropdownButtonWithDivider value="" onChange={mockOnChange} options={optionsWithIcons} />
      );

      expect(screen.getByTestId('option1-icon')).toBeInTheDocument();
      expect(screen.getByTestId('option2-icon')).toBeInTheDocument();
    });

    it('should handle options without icons gracefully', () => {
      render(
        <DropdownButtonWithDivider value="" onChange={mockOnChange} options={defaultOptions} />
      );

      // Should render items without throwing error
      expect(screen.getAllByTestId('select-item')).toHaveLength(3);

      // Icon sections should still exist but be empty
      const items = screen.getAllByTestId('select-item');
      items.forEach(item => {
        const iconSection = item.querySelector('.ml-3');
        expect(iconSection).toBeInTheDocument();
      });
    });
  });

  describe('Generic Type Support', () => {
    it('should work with string values', () => {
      const stringOptions: DropdownOption[] = [
        { value: 'apple', label: 'Apple' },
        { value: 'banana', label: 'Banana' },
      ];

      render(
        <DropdownButtonWithDivider<string>
          value="apple"
          onChange={mockOnChange}
          options={stringOptions}
        />
      );

      expect(screen.getByTestId('select-root')).toHaveAttribute('data-value', 'apple');
    });

    it('should handle onChange callback correctly', () => {
      render(
        <DropdownButtonWithDivider value="" onChange={mockOnChange} options={defaultOptions} />
      );

      // Verify the select root is rendered and the onChange prop is passed
      expect(screen.getByTestId('select-root')).toBeInTheDocument();
    });
  });

  describe('Edge Cases', () => {
    it('should handle empty options array', () => {
      render(<DropdownButtonWithDivider value="" onChange={mockOnChange} options={[]} />);

      expect(screen.queryAllByTestId('select-item')).toHaveLength(0);
    });

    it('should handle undefined options gracefully', () => {
      render(
        <DropdownButtonWithDivider value="" onChange={mockOnChange} options={undefined as any} />
      );

      expect(screen.queryAllByTestId('select-item')).toHaveLength(0);
    });

    it('should handle options with complex label content', () => {
      const complexOptions: DropdownOption[] = [
        {
          value: 'complex1',
          label: (
            <div data-testid="complex-label-1">
              <strong>Bold Text</strong>
              <span>Regular Text</span>
            </div>
          ),
        },
      ];

      render(
        <DropdownButtonWithDivider value="" onChange={mockOnChange} options={complexOptions} />
      );

      expect(screen.getByTestId('complex-label-1')).toBeInTheDocument();
    });

    it('should handle very long option labels', () => {
      const longLabelOptions: DropdownOption[] = [
        {
          value: 'long',
          label:
            'This is a very long option label that might cause layout issues if not handled properly in the dropdown component with divider',
        },
      ];

      render(
        <DropdownButtonWithDivider value="" onChange={mockOnChange} options={longLabelOptions} />
      );

      expect(screen.getByTestId('select-item')).toBeInTheDocument();
    });

    it('should handle options with complex icon content', () => {
      const complexIconOptions: DropdownOption[] = [
        {
          value: 'complex-icon',
          label: 'Complex Icon',
          icon: (
            <div data-testid="complex-icon" className="flex items-center">
              <span>🎯</span>
              <span className="ml-1">Target</span>
            </div>
          ),
        },
      ];

      render(
        <DropdownButtonWithDivider value="" onChange={mockOnChange} options={complexIconOptions} />
      );

      expect(screen.getByTestId('complex-icon')).toBeInTheDocument();
    });
  });

  describe('Accessibility', () => {
    it('should have proper ARIA attributes', () => {
      render(
        <DropdownButtonWithDivider
          value="option1"
          onChange={mockOnChange}
          options={defaultOptions}
        />
      );

      const items = screen.getAllByRole('option');
      expect(items).toHaveLength(3);
    });

    it('should work with keyboard navigation', () => {
      render(
        <DropdownButtonWithDivider value="" onChange={mockOnChange} options={defaultOptions} />
      );

      const trigger = screen.getByTestId('select-trigger');

      // Should be focusable
      expect(trigger).toBeInTheDocument();

      // Test keyboard interaction
      fireEvent.keyDown(trigger, { key: 'Enter' });
      fireEvent.keyDown(trigger, { key: 'ArrowDown' });
      fireEvent.keyDown(trigger, { key: 'Escape' });

      // Should not throw errors
      expect(trigger).toBeInTheDocument();
    });

    it('should be focusable when not disabled', () => {
      render(
        <DropdownButtonWithDivider value="" onChange={mockOnChange} options={defaultOptions} />
      );

      const trigger = screen.getByTestId('select-trigger');
      trigger.focus();
      expect(trigger).toHaveFocus();
    });

    it('should not be focusable when disabled', () => {
      render(
        <DropdownButtonWithDivider
          value=""
          onChange={mockOnChange}
          options={defaultOptions}
          disabled
        />
      );

      expect(screen.getByTestId('select-root')).toHaveAttribute('data-disabled', 'true');
    });
  });

  describe('CSS Classes and Styling', () => {
    it('should apply default CSS classes to wrapper', () => {
      render(
        <DropdownButtonWithDivider value="" onChange={mockOnChange} options={defaultOptions} />
      );

      const wrapper = screen.getByTestId('select-root').parentElement;
      expect(wrapper).toHaveClass('relative');
    });

    it('should apply correct CSS classes to trigger (different from regular dropdown)', () => {
      render(
        <DropdownButtonWithDivider value="" onChange={mockOnChange} options={defaultOptions} />
      );

      const trigger = screen.getByTestId('select-trigger');
      expect(trigger).toHaveClass(
        'px-4',
        'py-1.5',
        'border',
        'border-tertiary-300',
        'rounded-md',
        'text-base',
        'text-tertiary-500',
        'min-w-24',
        'shadow-none',
        'focus:ring-0',
        'focus:ring-offset-0',
        'focus:outline-none',
        'gap-1',
        'h-11'
      );
    });

    it('should apply flex gap class to items', () => {
      render(
        <DropdownButtonWithDivider value="" onChange={mockOnChange} options={defaultOptions} />
      );

      const items = screen.getAllByTestId('select-item');
      items.forEach(item => {
        expect(item).toHaveClass('flex', 'gap-1');
      });
    });

    it('should have specific styling for label sections', () => {
      render(
        <DropdownButtonWithDivider value="" onChange={mockOnChange} options={defaultOptions} />
      );

      const items = screen.getAllByTestId('select-item');
      items.forEach(item => {
        const labelSection = item.querySelector('.text-tertiary-500.text-sm.min-w-20');
        expect(labelSection).toHaveClass('text-tertiary-500', 'text-sm', 'min-w-20');
      });
    });

    it('should have specific styling for dividers', () => {
      render(
        <DropdownButtonWithDivider value="" onChange={mockOnChange} options={defaultOptions} />
      );

      const items = screen.getAllByTestId('select-item');
      items.forEach(item => {
        const divider = item.querySelector('.w-px.h-5.bg-tertiary-300');
        expect(divider).toHaveClass('w-px', 'h-5', 'bg-tertiary-300');
      });
    });
  });

  describe('Translation Integration', () => {
    it('should use translation for default placeholder', () => {
      render(
        <DropdownButtonWithDivider value="" onChange={mockOnChange} options={defaultOptions} />
      );

      expect(screen.getByTestId('select-value')).toHaveAttribute(
        'data-placeholder',
        'Select an option'
      );
    });

    it('should prefer custom placeholder over translation', () => {
      const customPlaceholder = 'Custom placeholder';
      render(
        <DropdownButtonWithDivider
          value=""
          onChange={mockOnChange}
          options={defaultOptions}
          placeholder={customPlaceholder}
        />
      );

      expect(screen.getByTestId('select-value')).toHaveAttribute(
        'data-placeholder',
        customPlaceholder
      );
    });
  });

  describe('Component Structure', () => {
    it('should render complete component structure', () => {
      render(
        <DropdownButtonWithDivider value="" onChange={mockOnChange} options={defaultOptions} />
      );

      // Check for all major structural elements
      expect(screen.getByTestId('select-root')).toBeInTheDocument();
      expect(screen.getByTestId('select-trigger')).toBeInTheDocument();
      expect(screen.getByTestId('select-value')).toBeInTheDocument();
      expect(screen.getByTestId('select-content')).toBeInTheDocument();
      expect(screen.getAllByTestId('select-item')).toHaveLength(3);
    });

    it('should maintain proper component hierarchy', () => {
      render(
        <DropdownButtonWithDivider value="" onChange={mockOnChange} options={defaultOptions} />
      );

      const root = screen.getByTestId('select-root');
      const trigger = screen.getByTestId('select-trigger');

      expect(root).toContainElement(trigger);
    });

    it('should render option structure with label, divider, and icon sections', () => {
      render(
        <DropdownButtonWithDivider value="" onChange={mockOnChange} options={optionsWithIcons} />
      );

      const items = screen.getAllByTestId('select-item');
      items.forEach(item => {
        // Check for main container
        const mainContainer = item.querySelector('.flex.gap-3.items-center');
        expect(mainContainer).toBeInTheDocument();

        // Check for label section
        const labelSection = item.querySelector('.text-tertiary-500.text-sm.min-w-20');
        expect(labelSection).toBeInTheDocument();

        // Check for divider
        const divider = item.querySelector('.w-px.h-5.bg-tertiary-300');
        expect(divider).toBeInTheDocument();

        // Check for icon section
        const iconSection = item.querySelector('.ml-3');
        expect(iconSection).toBeInTheDocument();
      });
    });
  });
});
