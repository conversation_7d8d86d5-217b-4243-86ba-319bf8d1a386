import React from 'react';
import { render, screen } from '@testing-library/react';
import { describe, it, expect, vi } from 'vitest';
import { TooltipWrapper } from '../TooltipWrapper';

// Mock UI tooltip components
vi.mock('../ui/tooltip', () => ({
  TooltipProvider: ({ children, delayDuration }: any) => (
    <div data-testid="tooltip-provider" data-delay-duration={delayDuration}>
      {children}
    </div>
  ),
  Tooltip: ({ children }: any) => <div data-testid="tooltip">{children}</div>,
  TooltipTrigger: ({ children, asChild }: any) => (
    <div data-testid="tooltip-trigger" data-as-child={asChild}>
      {children}
    </div>
  ),
  TooltipContent: ({ children, side, sideOffset, className }: any) => (
    <div
      data-testid="tooltip-content"
      data-side={side}
      data-side-offset={sideOffset}
      className={className}
    >
      {children}
    </div>
  ),
}));

describe('TooltipWrapper', () => {
  describe('Basic Rendering', () => {
    it('should render children with tooltip structure', () => {
      render(
        <TooltipWrapper content="Tooltip text">
          <button>Hover me</button>
        </TooltipWrapper>
      );

      expect(screen.getByTestId('tooltip-provider')).toBeInTheDocument();
      expect(screen.getByTestId('tooltip')).toBeInTheDocument();
      expect(screen.getByTestId('tooltip-trigger')).toBeInTheDocument();
      expect(screen.getByTestId('tooltip-content')).toBeInTheDocument();
      expect(screen.getByText('Hover me')).toBeInTheDocument();
      expect(screen.getByText('Tooltip text')).toBeInTheDocument();
    });

    it('should render with default props', () => {
      render(
        <TooltipWrapper content="Default tooltip">
          <span>Child element</span>
        </TooltipWrapper>
      );

      const provider = screen.getByTestId('tooltip-provider');
      expect(provider).toHaveAttribute('data-delay-duration', '300');

      const content = screen.getByTestId('tooltip-content');
      expect(content).toHaveAttribute('data-side', 'top');
      expect(content).toHaveAttribute('data-side-offset', '4');

      const trigger = screen.getByTestId('tooltip-trigger');
      expect(trigger).toHaveAttribute('data-as-child', 'true');
    });
  });

  describe('Props Handling', () => {
    it('should handle custom side prop', () => {
      render(
        <TooltipWrapper content="Tooltip" side="bottom">
          <div>Test</div>
        </TooltipWrapper>
      );

      expect(screen.getByTestId('tooltip-content')).toHaveAttribute('data-side', 'bottom');
    });

    it('should handle custom sideOffset prop', () => {
      render(
        <TooltipWrapper content="Tooltip" sideOffset={10}>
          <div>Test</div>
        </TooltipWrapper>
      );

      expect(screen.getByTestId('tooltip-content')).toHaveAttribute('data-side-offset', '10');
    });

    it('should handle custom delayDuration prop', () => {
      render(
        <TooltipWrapper content="Tooltip" delayDuration={500}>
          <div>Test</div>
        </TooltipWrapper>
      );

      expect(screen.getByTestId('tooltip-provider')).toHaveAttribute('data-delay-duration', '500');
    });

    it('should handle custom className prop', () => {
      render(
        <TooltipWrapper content="Tooltip" className="custom-tooltip-class">
          <div>Test</div>
        </TooltipWrapper>
      );

      expect(screen.getByTestId('tooltip-content')).toHaveClass('custom-tooltip-class');
    });
  });

  describe('Hidden State', () => {
    it('should render only children when hidden is true', () => {
      render(
        <TooltipWrapper content="Tooltip text" hidden>
          <button data-testid="hidden-child">Hidden tooltip child</button>
        </TooltipWrapper>
      );

      expect(screen.getByTestId('hidden-child')).toBeInTheDocument();
      expect(screen.getByText('Hidden tooltip child')).toBeInTheDocument();
      expect(screen.queryByTestId('tooltip-provider')).not.toBeInTheDocument();
      expect(screen.queryByTestId('tooltip')).not.toBeInTheDocument();
      expect(screen.queryByText('Tooltip text')).not.toBeInTheDocument();
    });

    it('should render tooltip structure when hidden is false', () => {
      render(
        <TooltipWrapper content="Tooltip text" hidden={false}>
          <button>Visible tooltip</button>
        </TooltipWrapper>
      );

      expect(screen.getByTestId('tooltip-provider')).toBeInTheDocument();
      expect(screen.getByText('Tooltip text')).toBeInTheDocument();
    });
  });

  describe('Content Handling', () => {
    it('should render React node content', () => {
      const content = (
        <div data-testid="complex-content">
          <strong>Bold text</strong>
          <span>Regular text</span>
        </div>
      );

      render(
        <TooltipWrapper content={content}>
          <div>Child</div>
        </TooltipWrapper>
      );

      expect(screen.getByTestId('complex-content')).toBeInTheDocument();
      expect(screen.getByText('Bold text')).toBeInTheDocument();
      expect(screen.getByText('Regular text')).toBeInTheDocument();
    });

    it('should render string content', () => {
      render(
        <TooltipWrapper content="Simple string tooltip">
          <div>Child</div>
        </TooltipWrapper>
      );

      expect(screen.getByText('Simple string tooltip')).toBeInTheDocument();
    });
  });

  describe('Children Handling', () => {
    it('should render multiple children', () => {
      render(
        <TooltipWrapper content="Tooltip">
          <button>Button 1</button>
          <span>Span text</span>
          <div>Div content</div>
        </TooltipWrapper>
      );

      expect(screen.getByText('Button 1')).toBeInTheDocument();
      expect(screen.getByText('Span text')).toBeInTheDocument();
      expect(screen.getByText('Div content')).toBeInTheDocument();
    });
  });
});
