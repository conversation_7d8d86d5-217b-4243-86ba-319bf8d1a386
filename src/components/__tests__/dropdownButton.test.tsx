import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import { describe, it, expect, vi, beforeEach } from 'vitest';
import DropdownButton from '../dropdownButton';
import { DropdownOption } from '@/types';

// Mock react-i18next
vi.mock('react-i18next', () => ({
  useTranslation: () => ({
    t: (key: string) => {
      if (key === 'common.selectOption') return 'Select an option';
      return key;
    },
  }),
}));

// Mock @radix-ui/react-select to make testing easier
vi.mock('@radix-ui/react-select', () => ({
  Root: ({ children, value, onValueChange, disabled, ...props }: any) => (
    <div data-testid="select-root" data-value={value} data-disabled={disabled} {...props}>
      {children}
    </div>
  ),
  Group: ({ children, ...props }: any) => (
    <div data-testid="select-group" {...props}>
      {children}
    </div>
  ),
  Trigger: React.forwardRef<HTMLButtonElement, any>(({ children, className, ...props }, ref) => (
    <button ref={ref} className={className} data-testid="select-trigger" {...props}>
      {children}
    </button>
  )),
  Content: ({ children, ...props }: any) => (
    <div data-testid="select-content" {...props}>
      {children}
    </div>
  ),
  Item: ({ children, value, className, ...props }: any) => (
    <div
      data-testid="select-item"
      data-value={value}
      className={className}
      role="option"
      {...props}
    >
      {children}
    </div>
  ),
  Value: ({ placeholder, ...props }: any) => (
    <span data-testid="select-value" data-placeholder={placeholder} {...props}>
      {placeholder}
    </span>
  ),
  Portal: ({ children }: any) => <div data-testid="select-portal">{children}</div>,
  Viewport: ({ children, className, ...props }: any) => (
    <div data-testid="select-viewport" className={className} {...props}>
      {children}
    </div>
  ),
  ScrollUpButton: (props: any) => <div data-testid="select-scroll-up" {...props} />,
  ScrollDownButton: (props: any) => <div data-testid="select-scroll-down" {...props} />,
  Icon: ({ children, ...props }: any) => (
    <span data-testid="select-icon" {...props}>
      {children}
    </span>
  ),
  ItemIndicator: ({ children, ...props }: any) => (
    <span data-testid="select-item-indicator" {...props}>
      {children}
    </span>
  ),
  ItemText: ({ children, ...props }: any) => (
    <span data-testid="select-item-text" {...props}>
      {children}
    </span>
  ),
  Label: ({ children, className, ...props }: any) => (
    <div data-testid="select-label" className={className} {...props}>
      {children}
    </div>
  ),
  Separator: ({ className, ...props }: any) => (
    <div data-testid="select-separator" className={className} {...props} />
  ),
}));

describe('DropdownButton', () => {
  const mockOnChange = vi.fn();

  const defaultOptions: DropdownOption[] = [
    { value: 'option1', label: 'Option 1' },
    { value: 'option2', label: 'Option 2' },
    { value: 'option3', label: 'Option 3' },
  ];

  const optionsWithIcons: DropdownOption[] = [
    {
      value: 'option1',
      label: 'Option 1',
      icon: <span data-testid="option1-icon">🎯</span>,
    },
    {
      value: 'option2',
      label: 'Option 2',
      icon: <span data-testid="option2-icon">⭐</span>,
    },
  ];

  beforeEach(() => {
    vi.clearAllMocks();
  });

  describe('Basic Rendering', () => {
    it('should render correctly with required props', () => {
      render(<DropdownButton value="option1" onChange={mockOnChange} options={defaultOptions} />);

      expect(screen.getByTestId('select-root')).toBeInTheDocument();
      expect(screen.getByTestId('select-trigger')).toBeInTheDocument();
      expect(screen.getByTestId('select-value')).toBeInTheDocument();
    });

    it('should render with default placeholder when no custom placeholder is provided', () => {
      render(<DropdownButton value="" onChange={mockOnChange} options={defaultOptions} />);

      expect(screen.getByTestId('select-value')).toHaveAttribute(
        'data-placeholder',
        'Select an option'
      );
    });

    it('should render with custom placeholder when provided', () => {
      const customPlaceholder = 'Choose your option';
      render(
        <DropdownButton
          value=""
          onChange={mockOnChange}
          options={defaultOptions}
          placeholder={customPlaceholder}
        />
      );

      expect(screen.getByTestId('select-value')).toHaveAttribute(
        'data-placeholder',
        customPlaceholder
      );
    });

    it('should render with selected value', () => {
      render(<DropdownButton value="option2" onChange={mockOnChange} options={defaultOptions} />);

      expect(screen.getByTestId('select-root')).toHaveAttribute('data-value', 'option2');
    });

    it('should render all options correctly', () => {
      render(<DropdownButton value="" onChange={mockOnChange} options={defaultOptions} />);

      const items = screen.getAllByTestId('select-item');
      expect(items).toHaveLength(3);
      expect(items[0]).toHaveAttribute('data-value', 'option1');
      expect(items[1]).toHaveAttribute('data-value', 'option2');
      expect(items[2]).toHaveAttribute('data-value', 'option3');
    });
  });

  describe('Props Handling', () => {
    it('should apply custom className to wrapper div', () => {
      const customClass = 'custom-dropdown-class';
      render(
        <DropdownButton
          value=""
          onChange={mockOnChange}
          options={defaultOptions}
          className={customClass}
        />
      );

      const wrapper = screen.getByTestId('select-root').parentElement;
      expect(wrapper).toHaveClass('relative', customClass);
    });

    it('should apply triggerClassName to SelectTrigger', () => {
      const triggerClass = 'custom-trigger-class';
      render(
        <DropdownButton
          value=""
          onChange={mockOnChange}
          options={defaultOptions}
          triggerClassName={triggerClass}
        />
      );

      expect(screen.getByTestId('select-trigger')).toHaveClass(triggerClass);
    });

    it('should render icon when provided', () => {
      const iconTestId = 'custom-icon';
      render(
        <DropdownButton
          value=""
          onChange={mockOnChange}
          options={defaultOptions}
          icon={<span data-testid={iconTestId}>📦</span>}
        />
      );

      expect(screen.getByTestId(iconTestId)).toBeInTheDocument();
    });

    it('should handle disabled state', () => {
      render(<DropdownButton value="" onChange={mockOnChange} options={defaultOptions} disabled />);

      expect(screen.getByTestId('select-root')).toHaveAttribute('data-disabled', 'true');
    });

    it('should render options with icons when provided', () => {
      render(<DropdownButton value="" onChange={mockOnChange} options={optionsWithIcons} />);

      expect(screen.getByTestId('option1-icon')).toBeInTheDocument();
      expect(screen.getByTestId('option2-icon')).toBeInTheDocument();
    });

    it('should render options without icons when not provided', () => {
      render(<DropdownButton value="" onChange={mockOnChange} options={defaultOptions} />);

      // Should not throw error and render normally
      expect(screen.getAllByTestId('select-item')).toHaveLength(3);
    });
  });

  describe('Generic Type Support', () => {
    it('should work with string values', () => {
      const stringOptions: DropdownOption[] = [
        { value: 'apple', label: 'Apple' },
        { value: 'banana', label: 'Banana' },
      ];

      render(
        <DropdownButton<string> value="apple" onChange={mockOnChange} options={stringOptions} />
      );

      expect(screen.getByTestId('select-root')).toHaveAttribute('data-value', 'apple');
    });

    it('should handle onChange callback correctly', () => {
      const { rerender } = render(
        <DropdownButton value="" onChange={mockOnChange} options={defaultOptions} />
      );

      // Simulate the radix select calling onValueChange
      const selectRoot = screen.getByTestId('select-root');

      // We'll test this by checking that the onChange prop is passed correctly
      expect(selectRoot.closest('[data-testid="select-root"]')).toBeInTheDocument();
    });
  });

  describe('Edge Cases', () => {
    it('should handle empty options array', () => {
      render(<DropdownButton value="" onChange={mockOnChange} options={[]} />);

      expect(screen.queryAllByTestId('select-item')).toHaveLength(0);
    });

    it('should handle undefined options gracefully', () => {
      render(<DropdownButton value="" onChange={mockOnChange} options={undefined as any} />);

      expect(screen.queryAllByTestId('select-item')).toHaveLength(0);
    });

    it('should handle options with complex label content', () => {
      const complexOptions: DropdownOption[] = [
        {
          value: 'complex1',
          label: (
            <div data-testid="complex-label-1">
              <strong>Bold Text</strong>
              <span>Regular Text</span>
            </div>
          ),
        },
      ];

      render(<DropdownButton value="" onChange={mockOnChange} options={complexOptions} />);

      expect(screen.getByTestId('complex-label-1')).toBeInTheDocument();
    });

    it('should handle very long option labels', () => {
      const longLabelOptions: DropdownOption[] = [
        {
          value: 'long',
          label:
            'This is a very long option label that might cause layout issues if not handled properly in the dropdown component',
        },
      ];

      render(<DropdownButton value="" onChange={mockOnChange} options={longLabelOptions} />);

      expect(screen.getByTestId('select-item')).toBeInTheDocument();
    });
  });

  describe('Accessibility', () => {
    it('should have proper ARIA attributes', () => {
      render(<DropdownButton value="option1" onChange={mockOnChange} options={defaultOptions} />);

      const items = screen.getAllByRole('option');
      expect(items).toHaveLength(3);
    });

    it('should work with keyboard navigation', () => {
      render(<DropdownButton value="" onChange={mockOnChange} options={defaultOptions} />);

      const trigger = screen.getByTestId('select-trigger');

      // Should be focusable
      expect(trigger).toBeInTheDocument();

      // Test keyboard interaction
      fireEvent.keyDown(trigger, { key: 'Enter' });
      fireEvent.keyDown(trigger, { key: 'ArrowDown' });
      fireEvent.keyDown(trigger, { key: 'Escape' });

      // Should not throw errors
      expect(trigger).toBeInTheDocument();
    });

    it('should be focusable when not disabled', () => {
      render(<DropdownButton value="" onChange={mockOnChange} options={defaultOptions} />);

      const trigger = screen.getByTestId('select-trigger');
      trigger.focus();
      expect(trigger).toHaveFocus();
    });

    it('should not be focusable when disabled', () => {
      render(<DropdownButton value="" onChange={mockOnChange} options={defaultOptions} disabled />);

      expect(screen.getByTestId('select-root')).toHaveAttribute('data-disabled', 'true');
    });
  });

  describe('CSS Classes', () => {
    it('should apply default CSS classes to wrapper', () => {
      render(<DropdownButton value="" onChange={mockOnChange} options={defaultOptions} />);

      const wrapper = screen.getByTestId('select-root').parentElement;
      expect(wrapper).toHaveClass('relative');
    });

    it('should apply default CSS classes to trigger', () => {
      render(<DropdownButton value="" onChange={mockOnChange} options={defaultOptions} />);

      const trigger = screen.getByTestId('select-trigger');
      expect(trigger).toHaveClass(
        'px-4',
        'py-1.5',
        'border-none',
        'rounded-lg',
        'text-sm',
        'min-w-24',
        'shadow-none',
        'focus:ring-0',
        'focus:ring-offset-0',
        'bg-tertiary-100',
        'focus:outline-none',
        'gap-1'
      );
    });

    it('should apply flex gap class to items with icons', () => {
      render(<DropdownButton value="" onChange={mockOnChange} options={optionsWithIcons} />);

      const items = screen.getAllByTestId('select-item');
      items.forEach(item => {
        expect(item).toHaveClass('flex', 'gap-1');
      });
    });
  });

  describe('Translation Integration', () => {
    it('should use translation for default placeholder', () => {
      render(<DropdownButton value="" onChange={mockOnChange} options={defaultOptions} />);

      expect(screen.getByTestId('select-value')).toHaveAttribute(
        'data-placeholder',
        'Select an option'
      );
    });

    it('should prefer custom placeholder over translation', () => {
      const customPlaceholder = 'Custom placeholder';
      render(
        <DropdownButton
          value=""
          onChange={mockOnChange}
          options={defaultOptions}
          placeholder={customPlaceholder}
        />
      );

      expect(screen.getByTestId('select-value')).toHaveAttribute(
        'data-placeholder',
        customPlaceholder
      );
    });
  });

  describe('Component Structure', () => {
    it('should render complete component structure', () => {
      render(<DropdownButton value="" onChange={mockOnChange} options={defaultOptions} />);

      // Check for all major structural elements
      expect(screen.getByTestId('select-root')).toBeInTheDocument();
      expect(screen.getByTestId('select-trigger')).toBeInTheDocument();
      expect(screen.getByTestId('select-value')).toBeInTheDocument();
      expect(screen.getByTestId('select-content')).toBeInTheDocument();
      expect(screen.getAllByTestId('select-item')).toHaveLength(3);
    });

    it('should maintain proper component hierarchy', () => {
      render(<DropdownButton value="" onChange={mockOnChange} options={defaultOptions} />);

      const root = screen.getByTestId('select-root');
      const trigger = screen.getByTestId('select-trigger');

      expect(root).toContainElement(trigger);
    });
  });
});
