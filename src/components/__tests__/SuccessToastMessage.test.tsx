import React from 'react';
import { render, screen } from '@testing-library/react';
import { describe, it, expect, vi } from 'vitest';
import SuccessToastMessage from '../SuccessToastMessage';

// Mock lucide-react
vi.mock('lucide-react', () => ({
  CircleCheck: ({ className }: any) => <div data-testid="check-icon" className={className} />,
}));

describe('SuccessToastMessage', () => {
  describe('Basic Rendering', () => {
    it('should render message with check icon', () => {
      render(<SuccessToastMessage message="Operation completed successfully!" />);

      expect(screen.getByText('Operation completed successfully!')).toBeInTheDocument();
      expect(screen.getByTestId('check-icon')).toBeInTheDocument();
    });

    it('should apply correct CSS classes', () => {
      const { container } = render(<SuccessToastMessage message="Test message" />);

      const wrapper = container.firstChild;
      expect(wrapper).toHaveClass('flex', 'items-center', 'gap-2');

      const checkIcon = screen.getByTestId('check-icon');
      expect(checkIcon).toHaveClass('h-5', 'w-5');

      const messageSpan = screen.getByText('Test message');
      expect(messageSpan).toHaveClass('font-normal');
    });

    it('should handle different message content', () => {
      const { rerender } = render(<SuccessToastMessage message="First message" />);
      expect(screen.getByText('First message')).toBeInTheDocument();

      rerender(<SuccessToastMessage message="Updated message" />);
      expect(screen.getByText('Updated message')).toBeInTheDocument();
      expect(screen.queryByText('First message')).not.toBeInTheDocument();
    });

    it('should handle empty message', () => {
      render(<SuccessToastMessage message="" />);

      expect(screen.getByTestId('check-icon')).toBeInTheDocument();

      // Check that the span exists with empty content
      const messageSpan = document.querySelector('span.font-normal');
      expect(messageSpan).toBeInTheDocument();
      expect(messageSpan).toHaveTextContent('');
    });
  });
});
