import React from 'react';
import { render, screen, fireEvent } from '@testing-library/react';
import { describe, it, expect, vi } from 'vitest';
import TagBox from '../TagBox';

// Mock cn utility
vi.mock('@/lib/utils', () => ({
  cn: (...classes: string[]) => classes.filter(Boolean).join(' '),
}));

describe('TagBox', () => {
  describe('Basic Rendering', () => {
    it('should render text content', () => {
      render(<TagBox text="Test Tag" />);

      expect(screen.getByText('Test Tag')).toBeInTheDocument();
    });

    it('should apply default CSS classes', () => {
      render(<TagBox text="Test Tag" />);

      const tagBox = screen.getByText('Test Tag');
      expect(tagBox).toHaveClass(
        'text-xs',
        'text-tertiary-500',
        'py-1',
        'px-2',
        'rounded',
        'bg-tertiary-100',
        'cursor-default'
      );
    });

    it('should apply custom className alongside defaults', () => {
      render(<TagBox text="Test Tag" className="custom-class" />);

      const tagBox = screen.getByText('Test Tag');
      expect(tagBox).toHaveClass(
        'text-xs',
        'text-tertiary-500',
        'py-1',
        'px-2',
        'rounded',
        'bg-tertiary-100',
        'cursor-default',
        'custom-class'
      );
    });

    it('should handle empty text', () => {
      render(<TagBox text="" />);

      const tagBox = document.querySelector('.text-xs');
      expect(tagBox).toBeInTheDocument();
      expect(tagBox).toHaveTextContent('');
    });

    it('should handle long text content', () => {
      const longText = 'This is a very long tag text that might wrap or truncate';
      render(<TagBox text={longText} />);

      expect(screen.getByText(longText)).toBeInTheDocument();
    });
  });

  describe('forwardRef Functionality', () => {
    it('should forward ref correctly', () => {
      const ref = React.createRef<HTMLDivElement>();
      render(<TagBox ref={ref} text="Test Tag" />);

      expect(ref.current).toBeInstanceOf(HTMLDivElement);
      expect(ref.current).toHaveTextContent('Test Tag');
    });

    it('should have correct displayName', () => {
      expect(TagBox.displayName).toBe('TagBox');
    });
  });

  describe('HTML Attributes', () => {
    it('should pass through HTML attributes', () => {
      render(
        <TagBox text="Test Tag" data-testid="custom-tag" role="button" aria-label="Custom tag" />
      );

      const tagBox = screen.getByTestId('custom-tag');
      expect(tagBox).toHaveAttribute('role', 'button');
      expect(tagBox).toHaveAttribute('aria-label', 'Custom tag');
    });

    it('should handle click events', () => {
      const mockClick = vi.fn();
      render(<TagBox text="Clickable Tag" onClick={mockClick} />);

      fireEvent.click(screen.getByText('Clickable Tag'));
      expect(mockClick).toHaveBeenCalled();
    });
  });
});
