import React from 'react';
import { render, screen } from '@testing-library/react';
import { describe, it, expect, vi } from 'vitest';
import { Loader } from '../Loader';

// Mock react-i18next
vi.mock('react-i18next', () => ({
  useTranslation: () => ({
    t: (key: string) => {
      if (key === 'common.loading') return 'Loading...';
      return key;
    },
  }),
}));

// Mock cn utility
vi.mock('@/lib/utils', () => ({
  cn: (...classes: string[]) => classes.filter(Boolean).join(' '),
}));

describe('Loader', () => {
  describe('Basic Rendering', () => {
    it('should render loading spinner and text', () => {
      render(<Loader />);

      // Check for loading text
      expect(screen.getByText('Loading...')).toBeInTheDocument();

      // Check for spinner element
      const spinner = document.querySelector('.animate-spin');
      expect(spinner).toBeInTheDocument();
      expect(spinner).toHaveClass('rounded-full', 'h-8', 'w-8', 'border-b-2', 'border-primary-500');
    });

    it('should apply correct CSS classes', () => {
      const { container } = render(<Loader />);

      const loaderContainer = container.firstChild;
      expect(loaderContainer).toHaveClass(
        'flex',
        'justify-center',
        'items-center',
        'py-8',
        'w-full',
        'h-full'
      );

      const loadingText = screen.getByText('Loading...');
      expect(loadingText).toHaveClass('ml-2', 'text-tertiary-600');
    });

    it('should use translated loading text', () => {
      render(<Loader />);

      expect(screen.getByText('Loading...')).toBeInTheDocument();
    });
  });
});
