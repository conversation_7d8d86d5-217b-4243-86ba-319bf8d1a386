import React from 'react';
import { render, screen, fireEvent } from '@testing-library/react';
import { describe, it, expect, vi, beforeEach } from 'vitest';
import FlowVersionModal from '../FlowVersionModal';
import { FlowVersion } from '@/types';

// Mock react-i18next
vi.mock('react-i18next', () => ({
  useTranslation: () => ({
    t: (key: string, params?: any) => {
      const translations: Record<string, string> = {
        'versionControl.title': 'Version Control',
        'versionControl.description': 'Manage and preview different versions of your flow',
        'versionControl.historyTitle': `Version History (${params?.count || 0})`,
        'versionControl.previewPlaceholder': 'Select a version to preview',
        'versionControl.noVersions': 'No versions available',
        'versionControl.version': `Version ${params?.version || ''}`,
        'versionControl.loadVersion': 'Load Version',
        'versionControl.revertToVersion': 'Revert to Version',
        'common.loading': 'Loading...',
        'common.errorLoadingData': 'Error loading data',
      };
      return translations[key] || key;
    },
  }),
}));

// Mock date-fns
vi.mock('date-fns', () => ({
  format: vi.fn(() => '15 Oct 2023 14:30'),
}));

// Mock RTK Query hooks
vi.mock('@/store/api/studioApi', () => ({
  useGetFlowVersionsQuery: vi.fn(),
  useGetFlowVersionDataQuery: vi.fn(),
}));

// Mock UI components
vi.mock('@/components/ui/dialog', () => ({
  Dialog: ({ children, open, onOpenChange }: any) =>
    open ? (
      <div data-testid="dialog" onClick={onOpenChange}>
        {children}
      </div>
    ) : null,
  DialogContent: ({ children, className }: any) => (
    <div data-testid="dialog-content" className={className}>
      {children}
    </div>
  ),
  DialogHeader: ({ children }: any) => <div data-testid="dialog-header">{children}</div>,
  DialogTitle: ({ children, className }: any) => (
    <h2 data-testid="dialog-title" className={className}>
      {children}
    </h2>
  ),
  DialogDescription: ({ children }: any) => <p data-testid="dialog-description">{children}</p>,
}));

vi.mock('@/components/ui/button', () => ({
  Button: ({ children, onClick, className, variant, size, asChild, ...props }: any) => {
    const Component = asChild ? 'div' : 'button';
    return (
      <Component
        data-testid="button"
        onClick={onClick}
        className={className}
        data-variant={variant}
        data-size={size}
        {...props}
      >
        {children}
      </Component>
    );
  },
}));

vi.mock('@/components/ui/scroll-area', () => ({
  ScrollArea: ({ children, className }: any) => (
    <div data-testid="scroll-area" className={className}>
      {children}
    </div>
  ),
}));

vi.mock('@/components/ui/dropdown-menu', () => ({
  DropdownMenu: ({ children }: any) => <div data-testid="dropdown-menu">{children}</div>,
  DropdownMenuTrigger: ({ children, asChild }: any) => {
    const Component = asChild ? 'div' : 'button';
    return <Component data-testid="dropdown-trigger">{children}</Component>;
  },
  DropdownMenuContent: ({ children, align }: any) => (
    <div data-testid="dropdown-content" data-align={align}>
      {children}
    </div>
  ),
  DropdownMenuItem: ({ children, onClick }: any) => (
    <div data-testid="dropdown-item" onClick={onClick}>
      {children}
    </div>
  ),
}));

// Mock lucide-react icons
vi.mock('lucide-react', () => ({
  MoreVertical: ({ className }: any) => (
    <div data-testid="more-vertical-icon" className={className} />
  ),
  Eye: ({ className }: any) => <div data-testid="eye-icon" className={className} />,
}));

// Mock NeuraTalkEditor
vi.mock('@/modules/editor/neuratalk-editor', () => ({
  default: ({ id, jsonDetails, readOnly }: any) => (
    <div
      data-testid="neuratalk-editor"
      data-id={id}
      data-readonly={readOnly}
      data-has-json={!!jsonDetails}
    >
      NeuraTalk Editor
    </div>
  ),
}));

// Import the mocked hooks
import * as studioApi from '@/store/api/studioApi';
const mockUseGetFlowVersionsQuery = vi.mocked(studioApi.useGetFlowVersionsQuery);
const mockUseGetFlowVersionDataQuery = vi.mocked(studioApi.useGetFlowVersionDataQuery);

describe('FlowVersionModal', () => {
  const defaultProps = {
    isOpen: true,
    onClose: vi.fn(),
    appId: 'test-app-id',
  };

  const mockFlowVersions: FlowVersion[] = [
    {
      appid: 'test-app-id',
      version: 'v1.0.0',
      comment: 'Initial version',
      owner: 1,
      createdBy: 1,
      createdAt: '2023-10-15T14:30:00Z',
      type: 'major',
      version_name: null,
    },
    {
      appid: 'test-app-id',
      version: 'v1.1.0',
      comment: 'Bug fixes and improvements',
      owner: 1,
      createdBy: 1,
      createdAt: '2023-10-16T10:15:00Z',
      type: 'minor',
      version_name: null,
    },
  ];

  // Helper to create complete RTK Query return values
  const createMockQueryResult = (overrides: any = {}) =>
    ({
      data: null,
      isLoading: false,
      error: null,
      refetch: vi.fn(),
      isFetching: false,
      isSuccess: false,
      isError: false,
      isUninitialized: false,
      currentData: undefined,
      originalArgs: undefined,
      requestId: '',
      startedTimeStamp: 0,
      fulfilledTimeStamp: 0,
      endpointName: '',
      ...overrides,
    }) as any;

  beforeEach(() => {
    vi.clearAllMocks();
  });

  describe('Basic Rendering', () => {
    it('should render modal when open', () => {
      mockUseGetFlowVersionsQuery.mockReturnValue(
        createMockQueryResult({
          data: { data: [] },
        })
      );
      mockUseGetFlowVersionDataQuery.mockReturnValue(createMockQueryResult());

      render(<FlowVersionModal {...defaultProps} />);

      expect(screen.getByTestId('dialog')).toBeInTheDocument();
      expect(screen.getByTestId('dialog-title')).toHaveTextContent('Version Control');
      expect(screen.getByTestId('dialog-description')).toHaveTextContent(
        'Manage and preview different versions of your flow'
      );
    });

    it('should not render modal when closed', () => {
      mockUseGetFlowVersionsQuery.mockReturnValue(
        createMockQueryResult({
          data: { data: [] },
        })
      );
      mockUseGetFlowVersionDataQuery.mockReturnValue(createMockQueryResult());

      render(<FlowVersionModal {...defaultProps} isOpen={false} />);

      expect(screen.queryByTestId('dialog')).not.toBeInTheDocument();
    });

    it('should call onClose when dialog is closed', () => {
      const mockOnClose = vi.fn();
      mockUseGetFlowVersionsQuery.mockReturnValue(
        createMockQueryResult({
          data: { data: [] },
        })
      );
      mockUseGetFlowVersionDataQuery.mockReturnValue(createMockQueryResult());

      render(<FlowVersionModal {...defaultProps} onClose={mockOnClose} />);

      fireEvent.click(screen.getByTestId('dialog'));
      expect(mockOnClose).toHaveBeenCalled();
    });
  });

  describe('Loading States', () => {
    it('should show loading state for versions', () => {
      mockUseGetFlowVersionsQuery.mockReturnValue(
        createMockQueryResult({
          isLoading: true,
        })
      );
      mockUseGetFlowVersionDataQuery.mockReturnValue(createMockQueryResult());

      render(<FlowVersionModal {...defaultProps} />);

      expect(screen.getByText('Loading...')).toBeInTheDocument();
    });

    it('should show loading state for selected version data', () => {
      mockUseGetFlowVersionsQuery.mockReturnValue(
        createMockQueryResult({
          data: { data: mockFlowVersions },
        })
      );
      mockUseGetFlowVersionDataQuery.mockReturnValue(
        createMockQueryResult({
          isLoading: true,
        })
      );

      render(<FlowVersionModal {...defaultProps} />);

      // Should show loading in the preview pane
      const loadingTexts = screen.getAllByText('Loading...');
      expect(loadingTexts.length).toBeGreaterThan(0);
    });
  });

  describe('Error States', () => {
    it('should show error state for versions', () => {
      mockUseGetFlowVersionsQuery.mockReturnValue(
        createMockQueryResult({
          error: { message: 'Failed to fetch versions' },
        })
      );
      mockUseGetFlowVersionDataQuery.mockReturnValue(createMockQueryResult());

      render(<FlowVersionModal {...defaultProps} />);

      expect(screen.getByText('Error loading data')).toBeInTheDocument();
    });

    it('should show error state for selected version data', () => {
      mockUseGetFlowVersionsQuery.mockReturnValue(
        createMockQueryResult({
          data: { data: mockFlowVersions },
        })
      );
      mockUseGetFlowVersionDataQuery.mockReturnValue(
        createMockQueryResult({
          error: { message: 'Failed to fetch version data' },
        })
      );

      render(<FlowVersionModal {...defaultProps} />);

      // Should show error in the preview pane
      const errorTexts = screen.getAllByText('Error loading data');
      expect(errorTexts.length).toBeGreaterThan(0);
    });
  });

  describe('Version List Display', () => {
    it('should display list of versions', () => {
      mockUseGetFlowVersionsQuery.mockReturnValue(
        createMockQueryResult({
          data: { data: mockFlowVersions },
        })
      );
      mockUseGetFlowVersionDataQuery.mockReturnValue(createMockQueryResult());

      render(<FlowVersionModal {...defaultProps} />);

      expect(screen.getByText('Version History (2)')).toBeInTheDocument();
      expect(screen.getByText('Version v1.0.0')).toBeInTheDocument();
      expect(screen.getByText('Version v1.1.0')).toBeInTheDocument();
      expect(screen.getByText('Initial version - major')).toBeInTheDocument();
      expect(screen.getByText('Bug fixes and improvements - minor')).toBeInTheDocument();
    });

    it('should show no versions message when list is empty', () => {
      mockUseGetFlowVersionsQuery.mockReturnValue(
        createMockQueryResult({
          data: { data: [] },
        })
      );
      mockUseGetFlowVersionDataQuery.mockReturnValue(createMockQueryResult());

      render(<FlowVersionModal {...defaultProps} />);

      expect(screen.getByText('Version History (0)')).toBeInTheDocument();
      expect(screen.getByText('No versions available')).toBeInTheDocument();
    });

    it('should format dates correctly', () => {
      mockUseGetFlowVersionsQuery.mockReturnValue(
        createMockQueryResult({
          data: { data: mockFlowVersions },
        })
      );
      mockUseGetFlowVersionDataQuery.mockReturnValue(createMockQueryResult());

      render(<FlowVersionModal {...defaultProps} />);

      // Should show formatted dates
      const formattedDates = screen.getAllByText('15 Oct 2023 14:30');
      expect(formattedDates.length).toBe(2); // Both versions should have the mocked date
    });
  });

  describe('Version Selection', () => {
    it('should display versions when loaded', () => {
      mockUseGetFlowVersionsQuery.mockReturnValue(
        createMockQueryResult({
          data: { data: mockFlowVersions },
        })
      );
      mockUseGetFlowVersionDataQuery.mockReturnValue(createMockQueryResult());

      render(<FlowVersionModal {...defaultProps} />);

      expect(screen.getByText('Version v1.0.0')).toBeInTheDocument();
    });

    it('should handle version click for preview', () => {
      mockUseGetFlowVersionsQuery.mockReturnValue(
        createMockQueryResult({
          data: { data: mockFlowVersions },
        })
      );
      mockUseGetFlowVersionDataQuery.mockReturnValue(createMockQueryResult());

      render(<FlowVersionModal {...defaultProps} />);

      // Click on second version
      const versionElements = screen.getAllByText(/Version v/);
      fireEvent.click(versionElements[1].closest('div')!);

      // Should update the selected version (this would trigger a re-render in real usage)
      expect(screen.getByText('Version v1.1.0')).toBeInTheDocument();
    });
  });

  describe('Preview Pane', () => {
    it('should show placeholder when no version is selected', () => {
      mockUseGetFlowVersionsQuery.mockReturnValue(
        createMockQueryResult({
          data: { data: [] },
        })
      );
      mockUseGetFlowVersionDataQuery.mockReturnValue(createMockQueryResult());

      render(<FlowVersionModal {...defaultProps} />);

      expect(screen.getByText('Select a version to preview')).toBeInTheDocument();
    });

    it('should render NeuraTalkEditor with version data', () => {
      const mockVersionData = {
        data: { flowData: 'complete flow data' },
      };

      mockUseGetFlowVersionsQuery.mockReturnValue(
        createMockQueryResult({
          data: { data: mockFlowVersions },
        })
      );
      mockUseGetFlowVersionDataQuery.mockReturnValue(
        createMockQueryResult({
          data: mockVersionData,
        })
      );

      render(<FlowVersionModal {...defaultProps} />);

      const editor = screen.getByTestId('neuratalk-editor');
      expect(editor).toBeInTheDocument();
      expect(editor).toHaveAttribute('data-id', 'test-app-id');
      expect(editor).toHaveAttribute('data-readonly', 'true');
      expect(editor).toHaveAttribute('data-has-json', 'true');
    });
  });

  describe('Dropdown Menu Functionality', () => {
    it('should render dropdown menus for each version', () => {
      mockUseGetFlowVersionsQuery.mockReturnValue(
        createMockQueryResult({
          data: { data: mockFlowVersions },
        })
      );
      mockUseGetFlowVersionDataQuery.mockReturnValue(createMockQueryResult());

      render(<FlowVersionModal {...defaultProps} />);

      const dropdownMenus = screen.getAllByTestId('dropdown-menu');
      expect(dropdownMenus).toHaveLength(2);

      const moreVerticalIcons = screen.getAllByTestId('more-vertical-icon');
      expect(moreVerticalIcons).toHaveLength(2);
    });

    it('should render dropdown menu items', () => {
      mockUseGetFlowVersionsQuery.mockReturnValue(
        createMockQueryResult({
          data: { data: mockFlowVersions },
        })
      );
      mockUseGetFlowVersionDataQuery.mockReturnValue(createMockQueryResult());

      render(<FlowVersionModal {...defaultProps} />);

      expect(screen.getAllByText('Load Version')).toHaveLength(2);
      expect(screen.getAllByText('Revert to Version')).toHaveLength(2);
    });

    it('should handle dropdown item clicks', () => {
      mockUseGetFlowVersionsQuery.mockReturnValue(
        createMockQueryResult({
          data: { data: mockFlowVersions },
        })
      );
      mockUseGetFlowVersionDataQuery.mockReturnValue(createMockQueryResult());

      render(<FlowVersionModal {...defaultProps} />);

      const loadVersionItems = screen.getAllByText('Load Version');
      fireEvent.click(loadVersionItems[0]);

      const revertVersionItems = screen.getAllByText('Revert to Version');
      fireEvent.click(revertVersionItems[0]);

      // These should not throw errors (TODO items in the component)
      expect(true).toBe(true);
    });
  });

  describe('Layout and Styling', () => {
    it('should apply correct CSS classes to main container', () => {
      mockUseGetFlowVersionsQuery.mockReturnValue(
        createMockQueryResult({
          data: { data: [] },
        })
      );
      mockUseGetFlowVersionDataQuery.mockReturnValue(createMockQueryResult());

      render(<FlowVersionModal {...defaultProps} />);

      const dialogContent = screen.getByTestId('dialog-content');
      expect(dialogContent).toHaveClass('sm:max-w-[1200px]', 'h-[90vh]', 'flex', 'flex-col');
    });

    it('should render scroll area for version list', () => {
      mockUseGetFlowVersionsQuery.mockReturnValue(
        createMockQueryResult({
          data: { data: mockFlowVersions },
        })
      );
      mockUseGetFlowVersionDataQuery.mockReturnValue(createMockQueryResult());

      render(<FlowVersionModal {...defaultProps} />);

      expect(screen.getByTestId('scroll-area')).toBeInTheDocument();
    });

    it('should have proper button styling', () => {
      mockUseGetFlowVersionsQuery.mockReturnValue(
        createMockQueryResult({
          data: { data: mockFlowVersions },
        })
      );
      mockUseGetFlowVersionDataQuery.mockReturnValue(createMockQueryResult());

      render(<FlowVersionModal {...defaultProps} />);

      const buttons = screen.getAllByTestId('button');
      const dropdownButtons = buttons.filter(
        btn =>
          btn.classList.contains('text-tertiary-500') &&
          btn.classList.contains('hover:bg-tertiary-50')
      );
      expect(dropdownButtons.length).toBeGreaterThan(0);
    });
  });

  describe('Accessibility', () => {
    it('should have proper heading structure', () => {
      mockUseGetFlowVersionsQuery.mockReturnValue(
        createMockQueryResult({
          data: { data: mockFlowVersions },
        })
      );
      mockUseGetFlowVersionDataQuery.mockReturnValue(createMockQueryResult());

      render(<FlowVersionModal {...defaultProps} />);

      const dialogTitle = screen.getByTestId('dialog-title');
      expect(dialogTitle.tagName).toBe('H2');

      const historyTitle = screen.getByText('Version History (2)');
      expect(historyTitle.tagName).toBe('H3');
    });

    it('should have clickable version items', () => {
      mockUseGetFlowVersionsQuery.mockReturnValue(
        createMockQueryResult({
          data: { data: mockFlowVersions },
        })
      );
      mockUseGetFlowVersionDataQuery.mockReturnValue(createMockQueryResult());

      render(<FlowVersionModal {...defaultProps} />);

      const versionItems = screen.getAllByText(/Version v/);
      versionItems.forEach(item => {
        // Find the version container div that should have cursor-pointer
        const versionContainer = item.closest('.cursor-pointer');
        expect(versionContainer).toBeInTheDocument();
        expect(versionContainer).toHaveClass('cursor-pointer');
      });
    });

    it('should have proper dropdown menu structure', () => {
      mockUseGetFlowVersionsQuery.mockReturnValue(
        createMockQueryResult({
          data: { data: mockFlowVersions },
        })
      );
      mockUseGetFlowVersionDataQuery.mockReturnValue(createMockQueryResult());

      render(<FlowVersionModal {...defaultProps} />);

      const dropdownTriggers = screen.getAllByTestId('dropdown-trigger');
      expect(dropdownTriggers).toHaveLength(2);

      const dropdownContents = screen.getAllByTestId('dropdown-content');
      dropdownContents.forEach(content => {
        expect(content).toHaveAttribute('data-align', 'end');
      });
    });
  });

  describe('Edge Cases', () => {
    it('should handle undefined data gracefully', () => {
      mockUseGetFlowVersionsQuery.mockReturnValue(
        createMockQueryResult({
          data: undefined,
        })
      );
      mockUseGetFlowVersionDataQuery.mockReturnValue(
        createMockQueryResult({
          data: undefined,
        })
      );

      render(<FlowVersionModal {...defaultProps} />);

      expect(screen.getByText('Version History (0)')).toBeInTheDocument();
      expect(screen.getByText('No versions available')).toBeInTheDocument();
    });

    it('should handle missing version data', () => {
      mockUseGetFlowVersionsQuery.mockReturnValue(
        createMockQueryResult({
          data: { data: mockFlowVersions },
        })
      );
      mockUseGetFlowVersionDataQuery.mockReturnValue(createMockQueryResult());

      render(<FlowVersionModal {...defaultProps} />);

      // Should not render the NeuraTalkEditor when no data
      expect(screen.queryByTestId('neuratalk-editor')).not.toBeInTheDocument();
    });

    it('should handle versions with missing properties', () => {
      const incompleteVersion = {
        appid: 'test-app-id',
        version: 'v2.0.0',
        comment: '',
        owner: 1,
        createdBy: 1,
        createdAt: '2023-10-17T12:00:00Z',
        type: '',
        version_name: null,
      };

      mockUseGetFlowVersionsQuery.mockReturnValue(
        createMockQueryResult({
          data: { data: [incompleteVersion] },
        })
      );
      mockUseGetFlowVersionDataQuery.mockReturnValue(createMockQueryResult());

      render(<FlowVersionModal {...defaultProps} />);

      expect(screen.getByText('Version v2.0.0')).toBeInTheDocument();
      // Check that the component renders without errors even with empty comment and type
      expect(screen.getByText('Version History (1)')).toBeInTheDocument();
    });
  });

  describe('Integration Tests', () => {
    it('should work with full data flow', () => {
      const mockVersionData = {
        data: { flowData: 'complete flow data' },
      };

      mockUseGetFlowVersionsQuery.mockReturnValue(
        createMockQueryResult({
          data: { data: mockFlowVersions },
        })
      );
      mockUseGetFlowVersionDataQuery.mockReturnValue(
        createMockQueryResult({
          data: mockVersionData,
        })
      );

      render(<FlowVersionModal {...defaultProps} />);

      // Should render all components
      expect(screen.getByText('Version Control')).toBeInTheDocument();
      expect(screen.getByText('Version History (2)')).toBeInTheDocument();
      expect(screen.getByTestId('neuratalk-editor')).toBeInTheDocument();
      expect(screen.getAllByTestId('dropdown-menu')).toHaveLength(2);
      expect(screen.getAllByText('Load Version')).toHaveLength(2);
      expect(screen.getAllByText('Revert to Version')).toHaveLength(2);
    });
  });
});
