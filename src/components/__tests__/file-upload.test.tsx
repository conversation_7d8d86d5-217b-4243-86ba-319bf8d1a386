import React from 'react';
import { render, screen, fireEvent, waitFor, act } from '@testing-library/react';
import { describe, it, expect, vi, beforeEach, afterEach } from 'vitest';
import { FileUpload, FileType } from '../file-upload';

// Mock react-i18next
let currentMaxSize = 5;
vi.mock('react-i18next', () => ({
  useTranslation: () => ({
    t: (key: string, params?: any) => {
      const translations: Record<string, string> = {
        'common.clickOrDragFiles': `Click or drag file${params?.plural || ''} here`,
        'common.maxSize': `Max size: ${params?.size}MB`,
        'common.addViaUrl': 'Add via URL',
        'common.enterFileUrl': 'Enter file URL',
        'fileUpload.someFilesRejected': `Some files were rejected. Max size is ${params?.size}MB`,
        'editor.uploadFormat': `Max size: ${currentMaxSize}MB`,
      };
      return translations[key] || key;
    },
  }),
}));

// Mock react-dropzone
let mockGetRootProps: any;
let mockGetInputProps: any;
let mockOnDrop: any;

vi.mock('react-dropzone', () => ({
  useDropzone: vi.fn(options => {
    mockOnDrop = options.onDrop;
    mockGetRootProps = vi.fn().mockImplementation((additionalProps = {}) => ({
      onClick: vi.fn(),
      onDrop: vi.fn(),
      'data-testid': 'dropzone-root',
      ...additionalProps,
    }));
    mockGetInputProps = vi.fn().mockReturnValue({
      type: 'file',
      'data-testid': 'dropzone-input',
    });
    return {
      getRootProps: mockGetRootProps,
      getInputProps: mockGetInputProps,
      isDragActive: false,
    };
  }),
}));

// Mock lucide-react icons
vi.mock('lucide-react', () => ({
  X: ({ className, ...props }: any) => (
    <div data-testid="x-icon" className={className} {...props} />
  ),
  FileImage: ({ className, ...props }: any) => (
    <div data-testid="file-image-icon" className={className} {...props} />
  ),
  FileUp: ({ className, ...props }: any) => (
    <div data-testid="file-up-icon" className={className} {...props} />
  ),
  Check: ({ className, ...props }: any) => (
    <div data-testid="check-icon" className={className} {...props} />
  ),
}));

// Mock UI components
vi.mock('./ui/progress', () => ({
  Progress: ({ value, ...props }: any) => (
    <div data-testid="progress" data-value={value} {...props}>
      Progress: {value}%
    </div>
  ),
}));

vi.mock('./ui/checkbox', () => ({
  Checkbox: ({ checked, onCheckedChange, id, ...props }: any) => (
    <input
      type="checkbox"
      checked={checked}
      onChange={() => onCheckedChange?.(!checked)}
      data-testid="checkbox"
      id={id}
      {...props}
    />
  ),
}));

// Mock cn utility
vi.mock('@/lib/utils', () => ({
  cn: (...classes: string[]) => classes.filter(Boolean).join(' '),
}));

// Mock URL methods
const mockCreateObjectURL = vi.fn();
const mockRevokeObjectURL = vi.fn();

Object.defineProperty(global.URL, 'createObjectURL', {
  writable: true,
  value: mockCreateObjectURL,
});

Object.defineProperty(global.URL, 'revokeObjectURL', {
  writable: true,
  value: mockRevokeObjectURL,
});

// Helper function to create mock files
const createMockFile = (name: string, type: string, size: number = 1024): File => {
  const file = new File(['test content'], name, { type });
  Object.defineProperty(file, 'size', { value: size });
  return file;
};

describe('FileUpload', () => {
  const mockOnChange = vi.fn();
  const mockOnUrlChange = vi.fn();
  const mockOnUpload = vi.fn();

  beforeEach(() => {
    vi.clearAllMocks();
    mockCreateObjectURL.mockReturnValue('blob:mock-url');
    vi.useFakeTimers();
    currentMaxSize = 5;
  });

  afterEach(() => {
    vi.useRealTimers();
  });

  describe('Basic Rendering', () => {
    it('should render with default props', () => {
      render(<FileUpload />);

      expect(screen.getByTestId('dropzone-root')).toBeInTheDocument();
      expect(screen.getByTestId('dropzone-input')).toBeInTheDocument();
      expect(screen.getByTestId('file-up-icon')).toBeInTheDocument();
      expect(screen.getByText('Click or drag file here')).toBeInTheDocument();
      expect(screen.getByText('Max size: 5MB')).toBeInTheDocument();
    });

    it('should render with custom upload label', () => {
      const customLabel = 'Custom upload label';
      render(<FileUpload uploadLabel={customLabel} />);

      expect(screen.getByText(customLabel)).toBeInTheDocument();
      expect(screen.queryByText('Click or drag file here')).not.toBeInTheDocument();
    });

    it('should render with custom upload icon', () => {
      const customIcon = <div data-testid="custom-upload-icon">📁</div>;
      render(<FileUpload uploadIcon={customIcon} />);

      expect(screen.getByTestId('custom-upload-icon')).toBeInTheDocument();
      expect(screen.queryByTestId('file-up-icon')).not.toBeInTheDocument();
    });

    it('should render with custom max size', () => {
      currentMaxSize = 10;
      render(<FileUpload maxSize={10} />);

      expect(screen.getByText('Max size: 10MB')).toBeInTheDocument();
    });

    it('should render for multiple files', () => {
      render(<FileUpload multiple />);

      expect(screen.getByText('Click or drag files here')).toBeInTheDocument();
    });

    it('should apply custom className', () => {
      const { container } = render(<FileUpload className="custom-class" />);

      expect(container.firstChild).toHaveClass('w-full', 'custom-class');
    });
  });

  describe('File Display and Preview', () => {
    it('should display uploaded files', () => {
      const mockFiles = [
        createMockFile('test.txt', 'text/plain'),
        createMockFile('image.jpg', 'image/jpeg'),
      ];

      render(<FileUpload value={mockFiles} />);

      expect(screen.getByText('test.txt')).toBeInTheDocument();
      expect(screen.getByText('image.jpg')).toBeInTheDocument();
    });

    it('should show image preview for image files', () => {
      const imageFile = createMockFile('image.jpg', 'image/jpeg');
      render(<FileUpload value={[imageFile]} />);

      const preview = screen.getByRole('img', { name: 'Preview' });
      expect(preview).toBeInTheDocument();
      expect(preview).toHaveAttribute('src', 'blob:mock-url');
    });

    it('should show file icon for non-image files', () => {
      const textFile = createMockFile('document.txt', 'text/plain');
      render(<FileUpload value={[textFile]} />);

      expect(screen.getByTestId('file-image-icon')).toBeInTheDocument();
      expect(screen.queryByRole('img', { name: 'Preview' })).not.toBeInTheDocument();
    });

    it('should show remove button for each file', () => {
      const mockFiles = [
        createMockFile('test1.txt', 'text/plain'),
        createMockFile('test2.txt', 'text/plain'),
      ];

      render(<FileUpload value={mockFiles} onChange={mockOnChange} />);

      const removeButtons = screen.getAllByLabelText('Remove file');
      expect(removeButtons).toHaveLength(2);
    });

    it('should call onChange when removing a file', () => {
      const mockFiles = [
        createMockFile('test1.txt', 'text/plain'),
        createMockFile('test2.txt', 'text/plain'),
      ];

      render(<FileUpload value={mockFiles} onChange={mockOnChange} />);

      const removeButtons = screen.getAllByLabelText('Remove file');
      fireEvent.click(removeButtons[0]);

      expect(mockOnChange).toHaveBeenCalledWith([mockFiles[1]]);
    });

    it('should revoke object URL when removing blob URLs', () => {
      const blobUrl = 'blob:mock-url-123';
      render(<FileUpload value={[blobUrl]} onChange={mockOnChange} />);

      const removeButton = screen.getByLabelText('Remove file');
      fireEvent.click(removeButton);

      expect(mockRevokeObjectURL).toHaveBeenCalledWith(blobUrl);
    });
  });

  describe('Drag and Drop Functionality', () => {
    it('should handle file drop', async () => {
      render(<FileUpload onChange={mockOnChange} />);

      const files = [createMockFile('test.txt', 'text/plain')];

      await act(async () => {
        mockOnDrop(files, []);
      });

      expect(mockOnChange).toHaveBeenCalledWith(files);
    });

    it('should handle multiple file drop when multiple is enabled', async () => {
      render(<FileUpload multiple onChange={mockOnChange} />);

      const files = [
        createMockFile('test1.txt', 'text/plain'),
        createMockFile('test2.txt', 'text/plain'),
      ];

      await act(async () => {
        mockOnDrop(files, []);
      });

      expect(mockOnChange).toHaveBeenCalledWith(files);
    });

    it('should only accept first file when multiple is disabled', async () => {
      render(<FileUpload multiple={false} onChange={mockOnChange} />);

      const files = [
        createMockFile('test1.txt', 'text/plain'),
        createMockFile('test2.txt', 'text/plain'),
      ];

      await act(async () => {
        mockOnDrop(files, []);
      });

      expect(mockOnChange).toHaveBeenCalledWith([files[0]]);
    });

    it('should handle file rejections', async () => {
      render(<FileUpload maxSize={1} onChange={mockOnChange} />);

      const rejectedFiles = [{ file: createMockFile('large.txt', 'text/plain', 2048) }];

      await act(async () => {
        mockOnDrop([], rejectedFiles);
      });

      expect(screen.getByText('Some files were rejected. Max size is 1MB')).toBeInTheDocument();
      expect(mockOnChange).not.toHaveBeenCalled();
    });
  });

  describe('File Upload with onUpload', () => {
    it('should call onUpload when provided', async () => {
      mockOnUpload.mockResolvedValue(undefined);
      render(<FileUpload onUpload={mockOnUpload} onChange={mockOnChange} />);

      const file = createMockFile('test.txt', 'text/plain');

      await act(async () => {
        mockOnDrop([file], []);
      });

      expect(mockOnUpload).toHaveBeenCalledWith(file);
      expect(mockOnChange).toHaveBeenCalledWith([file]);
    });

    it('should handle upload errors', async () => {
      const errorMessage = 'Upload failed';
      mockOnUpload.mockRejectedValue(new Error(errorMessage));

      render(<FileUpload onUpload={mockOnUpload} onChange={mockOnChange} />);

      const file = createMockFile('test.txt', 'text/plain');

      await act(async () => {
        mockOnDrop([file], []);
      });

      expect(
        screen.getByText(`Failed to upload ${file.name}: ${errorMessage}`)
      ).toBeInTheDocument();
    });

    it('should show progress when showProgress is enabled', async () => {
      render(<FileUpload showProgress onChange={mockOnChange} />);

      const file = createMockFile('test.txt', 'text/plain');

      await act(async () => {
        mockOnDrop([file], []);
      });

      // Advance timers to simulate progress
      act(() => {
        vi.advanceTimersByTime(150);
      });

      expect(mockOnChange).toHaveBeenCalledWith([file]);
    });
  });

  describe('URL Input Functionality', () => {
    it('should show URL option when enabled', () => {
      render(<FileUpload showUrlOption />);

      expect(screen.getByRole('checkbox')).toBeInTheDocument();
      expect(screen.getByText('Add via URL')).toBeInTheDocument();
    });

    it('should toggle URL input when checkbox is clicked', () => {
      render(<FileUpload showUrlOption />);

      const checkbox = screen.getByRole('checkbox');
      expect(checkbox).toHaveAttribute('aria-checked', 'false');

      fireEvent.click(checkbox);

      expect(screen.getByLabelText('URL input')).toBeInTheDocument();
      expect(screen.getByPlaceholderText('Enter file URL')).toBeInTheDocument();
    });

    it('should call onUrlChange when URL input changes', () => {
      render(<FileUpload showUrlOption onUrlChange={mockOnUrlChange} />);

      const checkbox = screen.getByRole('checkbox');
      fireEvent.click(checkbox);

      const urlInput = screen.getByLabelText('URL input');
      fireEvent.change(urlInput, { target: { value: 'https://example.com/file.jpg' } });

      expect(mockOnUrlChange).toHaveBeenCalledWith('https://example.com/file.jpg');
    });

    it('should not show URL option when disabled', () => {
      render(<FileUpload showUrlOption={false} />);

      expect(screen.queryByRole('checkbox')).not.toBeInTheDocument();
      expect(screen.queryByText('Add via URL')).not.toBeInTheDocument();
    });
  });

  describe('Read-only Mode', () => {
    it('should not show dropzone when readOnly is true', () => {
      render(<FileUpload readOnly />);

      expect(screen.queryByTestId('dropzone-root')).not.toBeInTheDocument();
      expect(screen.queryByTestId('dropzone-input')).not.toBeInTheDocument();
    });

    it('should not show remove buttons when readOnly is true', () => {
      const mockFiles = [createMockFile('test.txt', 'text/plain')];
      render(<FileUpload value={mockFiles} readOnly />);

      expect(screen.queryByLabelText('Remove file')).not.toBeInTheDocument();
    });

    it('should not show URL option when readOnly is true', () => {
      render(<FileUpload showUrlOption readOnly />);

      expect(screen.queryByTestId('checkbox')).not.toBeInTheDocument();
    });

    it('should not process drops when readOnly is true', async () => {
      render(<FileUpload readOnly onChange={mockOnChange} />);

      const file = createMockFile('test.txt', 'text/plain');

      await act(async () => {
        mockOnDrop([file], []);
      });

      expect(mockOnChange).not.toHaveBeenCalled();
    });

    it('should not allow file removal when readOnly is true', () => {
      const mockFiles = [createMockFile('test.txt', 'text/plain')];
      render(<FileUpload value={mockFiles} readOnly onChange={mockOnChange} />);

      // Should not have remove buttons
      expect(screen.queryByLabelText('Remove file')).not.toBeInTheDocument();
    });
  });

  describe('Progress Tracking', () => {
    it('should show progress bar when uploading', () => {
      const file = createMockFile('test.txt', 'text/plain');
      render(<FileUpload showProgress onChange={mockOnChange} value={[file]} />);

      // Should show the file
      expect(screen.getByText('test.txt')).toBeInTheDocument();
    });

    it('should simulate progress when no onUpload provided', async () => {
      render(<FileUpload showProgress onChange={mockOnChange} />);

      const file = createMockFile('test.txt', 'text/plain');

      await act(async () => {
        mockOnDrop([file], []);
      });

      // Fast-forward time to see progress
      act(() => {
        vi.advanceTimersByTime(300); // 2 intervals
      });

      // Progress should be simulated
      expect(mockOnChange).toHaveBeenCalled();
    });
  });

  describe('File Type Validation', () => {
    it('should accept specified file types', () => {
      const accept = { 'image/*': ['.jpg', '.png'] };
      render(<FileUpload accept={accept} />);

      // Check that component renders (implicit validation that useDropzone was called)
      expect(screen.getByTestId('dropzone-root')).toBeInTheDocument();
    });

    it('should handle string accept format', () => {
      const accept = 'image/*';
      render(<FileUpload accept={accept} />);

      // Check that component renders (implicit validation that useDropzone was called)
      expect(screen.getByTestId('dropzone-root')).toBeInTheDocument();
    });
  });

  describe('Custom Preview Icon', () => {
    it('should use custom file preview icon', () => {
      const customIcon = <div data-testid="custom-preview-icon">📄</div>;
      const textFile = createMockFile('document.txt', 'text/plain');

      render(<FileUpload value={[textFile]} filePreviewIcon={customIcon} />);

      expect(screen.getByTestId('custom-preview-icon')).toBeInTheDocument();
      expect(screen.queryByTestId('file-image-icon')).not.toBeInTheDocument();
    });
  });

  describe('Error Handling', () => {
    it('should clear error when new files are successfully dropped', async () => {
      render(<FileUpload maxSize={1} onChange={mockOnChange} />);

      // First, trigger an error
      const largeFile = createMockFile('large.txt', 'text/plain', 2048);
      await act(async () => {
        mockOnDrop([], [{ file: largeFile }]);
      });

      expect(screen.getByText('Some files were rejected. Max size is 1MB')).toBeInTheDocument();

      // Then drop a valid file
      const validFile = createMockFile('small.txt', 'text/plain', 512);
      await act(async () => {
        mockOnDrop([validFile], []);
      });

      expect(
        screen.queryByText('Some files were rejected. Max size is 1MB')
      ).not.toBeInTheDocument();
    });

    it('should show error message with proper accessibility', () => {
      render(<FileUpload maxSize={1} />);

      act(() => {
        mockOnDrop([], [{ file: createMockFile('large.txt', 'text/plain', 2048) }]);
      });

      const errorElement = screen.getByText('Some files were rejected. Max size is 1MB');
      expect(errorElement).toHaveAttribute('id', 'file-upload-error');
    });
  });

  describe('Accessibility', () => {
    it('should have proper ARIA attributes', () => {
      render(<FileUpload />);

      const dropzone = screen.getByTestId('dropzone-root');
      expect(dropzone).toBeInTheDocument();
    });

    it('should link error message with dropzone', async () => {
      render(<FileUpload maxSize={1} />);

      await act(async () => {
        mockOnDrop([], [{ file: createMockFile('large.txt', 'text/plain', 2048) }]);
      });

      const errorElement = screen.getByText('Some files were rejected. Max size is 1MB');
      expect(errorElement).toHaveAttribute('id', 'file-upload-error');
    });

    it('should have proper labels for form elements', () => {
      render(<FileUpload showUrlOption />);

      expect(screen.getByText('Add via URL')).toBeInTheDocument();
      expect(screen.getByRole('checkbox')).toBeInTheDocument();
    });
  });

  describe('Integration Tests', () => {
    it('should work with all features enabled', async () => {
      const onUpload = vi.fn().mockResolvedValue(undefined);
      currentMaxSize = 10;

      render(
        <FileUpload
          multiple
          showUrlOption
          showProgress
          maxSize={10}
          onChange={mockOnChange}
          onUpload={onUpload}
          onUrlChange={mockOnUrlChange}
          uploadLabel="Custom upload text"
          className="custom-class"
        />
      );

      // Should render all elements
      expect(screen.getByText('Custom upload text')).toBeInTheDocument();
      expect(screen.getByText('Max size: 10MB')).toBeInTheDocument();
      expect(screen.getByRole('checkbox')).toBeInTheDocument();

      // Should handle file upload
      const files = [
        createMockFile('test1.txt', 'text/plain'),
        createMockFile('test2.txt', 'text/plain'),
      ];
      await act(async () => {
        mockOnDrop(files, []);
      });

      expect(onUpload).toHaveBeenCalledTimes(2);
      expect(mockOnChange).toHaveBeenCalledWith(files);
    });

    it('should handle existing files with new uploads', async () => {
      const existingFiles = [createMockFile('existing.txt', 'text/plain')];

      render(<FileUpload value={existingFiles} onChange={mockOnChange} />);

      const newFile = createMockFile('new.txt', 'text/plain');
      await act(async () => {
        mockOnDrop([newFile], []);
      });

      expect(mockOnChange).toHaveBeenCalledWith([...existingFiles, newFile]);
    });
  });

  describe('File Type Detection', () => {
    it('should detect image files correctly', () => {
      const imageFile = createMockFile('test.jpg', 'image/jpeg');
      render(<FileUpload value={[imageFile]} />);

      expect(screen.getByRole('img', { name: 'Preview' })).toBeInTheDocument();
    });

    it('should detect image URLs correctly', () => {
      const imageUrl = 'https://example.com/image.png';
      render(<FileUpload value={[imageUrl]} />);

      const preview = screen.getByRole('img', { name: 'Preview' });
      expect(preview).toHaveAttribute('src', imageUrl);
    });

    it('should handle non-image files', () => {
      const textFile = createMockFile('document.txt', 'text/plain');
      render(<FileUpload value={[textFile]} />);

      expect(screen.getByTestId('file-image-icon')).toBeInTheDocument();
      expect(screen.queryByRole('img', { name: 'Preview' })).not.toBeInTheDocument();
    });
  });
});
