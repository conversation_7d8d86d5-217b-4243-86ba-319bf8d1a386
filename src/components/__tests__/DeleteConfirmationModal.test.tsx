import React from 'react';
import { render, screen, fireEvent } from '@testing-library/react';
import { describe, it, expect, vi } from 'vitest';
import DeleteConfirmationModal from '../DeleteConfirmationModal';

// Mock react-i18next
vi.mock('react-i18next', () => ({
  useTranslation: () => ({
    t: (key: string) => {
      if (key === 'chatbot.noCancel') return 'No, Cancel';
      if (key === 'chatbot.yesDelete') return 'Yes, Delete';
      return key;
    },
  }),
}));

describe('DeleteConfirmationModal', () => {
  const mockOnConfirm = vi.fn();
  const mockOnClose = vi.fn();
  const title = 'Delete Item';
  const description = 'Are you sure you want to delete this item?';

  // Test case for basic rendering in uncontrolled mode
  it('should render correctly in uncontrolled mode', () => {
    render(
      <DeleteConfirmationModal onConfirm={mockOnConfirm} title={title} description={description}>
        <button>Open Modal</button>
      </DeleteConfirmationModal>
    );

    expect(screen.getByText('Open Modal')).toBeInTheDocument();
  });

  // Test case for opening the modal and checking content
  it('should open the modal and display title, description, and actions', () => {
    render(
      <DeleteConfirmationModal onConfirm={mockOnConfirm} title={title} description={description}>
        <button>Open Modal</button>
      </DeleteConfirmationModal>
    );

    const triggerButton = screen.getByText('Open Modal');
    fireEvent.click(triggerButton);

    expect(screen.getByText(title)).toBeInTheDocument();
    expect(screen.getByText(description)).toBeInTheDocument();
    expect(screen.getByText('No, Cancel')).toBeInTheDocument();
    expect(screen.getByText('Yes, Delete')).toBeInTheDocument();
  });

  // Test case for confirm action in uncontrolled mode
  it('should call onConfirm when the confirm button is clicked', () => {
    render(
      <DeleteConfirmationModal onConfirm={mockOnConfirm} title={title} description={description}>
        <button>Open Modal</button>
      </DeleteConfirmationModal>
    );

    fireEvent.click(screen.getByText('Open Modal'));
    fireEvent.click(screen.getByText('Yes, Delete'));

    expect(mockOnConfirm).toHaveBeenCalledTimes(1);
  });

  // Test case for cancel action in uncontrolled mode
  it('should close the modal when the cancel button is clicked', () => {
    render(
      <DeleteConfirmationModal onConfirm={mockOnConfirm} title={title} description={description}>
        <button>Open Modal</button>
      </DeleteConfirmationModal>
    );

    const triggerButton = screen.getByText('Open Modal');
    fireEvent.click(triggerButton);

    // Modal is open
    expect(screen.getByText(title)).toBeInTheDocument();

    const cancelButton = screen.getByText('No, Cancel');
    fireEvent.click(cancelButton);

    // Modal should be closed
    expect(screen.queryByText(title)).not.toBeInTheDocument();
  });

  // Test cases for controlled mode
  describe('controlled mode', () => {
    it('should be visible when isOpen is true', () => {
      render(
        <DeleteConfirmationModal
          isOpen={true}
          onClose={mockOnClose}
          onConfirm={mockOnConfirm}
          title={title}
          description={description}
        />
      );

      expect(screen.getByText(title)).toBeInTheDocument();
      expect(screen.getByText(description)).toBeInTheDocument();
    });

    it('should be hidden when isOpen is false', () => {
      render(
        <DeleteConfirmationModal
          isOpen={false}
          onClose={mockOnClose}
          onConfirm={mockOnConfirm}
          title={title}
          description={description}
        />
      );

      expect(screen.queryByText(title)).not.toBeInTheDocument();
    });

    it('should call onClose when the cancel button is clicked', () => {
      render(
        <DeleteConfirmationModal
          isOpen={true}
          onClose={mockOnClose}
          onConfirm={mockOnConfirm}
          title={title}
          description={description}
        />
      );

      fireEvent.click(screen.getByText('No, Cancel'));
      expect(mockOnClose).toHaveBeenCalledTimes(1);
    });

    it('should call onConfirm when the confirm button is clicked', () => {
      const onConfirmControlled = vi.fn();
      render(
        <DeleteConfirmationModal
          isOpen={true}
          onClose={mockOnClose}
          onConfirm={onConfirmControlled}
          title={title}
          description={description}
        />
      );

      fireEvent.click(screen.getByText('Yes, Delete'));
      expect(onConfirmControlled).toHaveBeenCalledTimes(1);
    });
  });
});
