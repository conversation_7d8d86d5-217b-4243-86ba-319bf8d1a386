import React from 'react';
import { render, screen, fireEvent } from '@testing-library/react';
import { describe, it, expect, vi, beforeEach } from 'vitest';
import { MultiSelect } from '../multi-select';

// Mock react-select
vi.mock('react-select', () => ({
  default: ({
    options,
    value,
    onChange,
    placeholder,
    isDisabled,
    className,
    onMenuOpen,
    onMenuClose,
    components,
    ...props
  }: any) => (
    <div
      data-testid="react-select"
      className={className}
      data-disabled={isDisabled}
      data-placeholder={placeholder}
      data-multi={props.isMulti}
    >
      {/* Render selected values */}
      {value?.map((item: any, index: number) => (
        <div key={index} data-testid="selected-value">
          {item.label}
          {/* Render custom remove component */}
          {components?.MultiValueRemove && (
            <div data-testid="custom-remove">
              {React.createElement(components.MultiValueRemove, {
                innerProps: { onClick: () => {} },
              })}
            </div>
          )}
        </div>
      ))}

      {/* Render options */}
      {options?.map((option: any) => (
        <div key={option.id} data-testid="option" onClick={() => onChange([...value, option])}>
          {option.label}
        </div>
      ))}

      {/* Menu callbacks */}
      <button data-testid="menu-open" onClick={onMenuOpen}>
        Open
      </button>
      <button data-testid="menu-close" onClick={onMenuClose}>
        Close
      </button>
    </div>
  ),
  components: {
    MultiValueRemove: ({ children }: any) => <div data-testid="multi-value-remove">{children}</div>,
  },
}));

// Mock lucide-react
vi.mock('lucide-react', () => ({
  X: ({ className }: any) => <div data-testid="x-icon" className={className} />,
}));

// Mock cn utility
vi.mock('@/lib/utils', () => ({
  cn: (...classes: string[]) => classes.filter(Boolean).join(' '),
}));

describe('MultiSelect', () => {
  const mockOnChange = vi.fn();
  const mockOnMenuOpen = vi.fn();
  const mockOnMenuClose = vi.fn();

  const mockOptions = [
    { id: '1', value: 'option1', label: 'Option 1' },
    { id: '2', value: 'option2', label: 'Option 2' },
    { id: '3', value: 'option3', label: 'Option 3' },
  ];

  const defaultProps = {
    options: mockOptions,
    value: [],
    onChange: mockOnChange,
  };

  beforeEach(() => {
    vi.clearAllMocks();
  });

  describe('Basic Rendering', () => {
    it('should render with default props', () => {
      render(<MultiSelect {...defaultProps} />);

      expect(screen.getByTestId('react-select')).toBeInTheDocument();
      expect(screen.getByTestId('react-select')).toHaveAttribute('data-multi', 'true');
    });

    it('should render options', () => {
      render(<MultiSelect {...defaultProps} />);

      expect(screen.getByText('Option 1')).toBeInTheDocument();
      expect(screen.getByText('Option 2')).toBeInTheDocument();
      expect(screen.getByText('Option 3')).toBeInTheDocument();
    });

    it('should render selected values', () => {
      const selectedValues = [mockOptions[0], mockOptions[1]];

      render(<MultiSelect {...defaultProps} value={selectedValues} />);

      const selectedValueElements = screen.getAllByTestId('selected-value');
      expect(selectedValueElements).toHaveLength(2);
      expect(selectedValueElements[0]).toHaveTextContent('Option 1');
      expect(selectedValueElements[1]).toHaveTextContent('Option 2');
    });
  });

  describe('Props Handling', () => {
    it('should handle placeholder prop', () => {
      render(<MultiSelect {...defaultProps} placeholder="Select options..." />);

      expect(screen.getByTestId('react-select')).toHaveAttribute(
        'data-placeholder',
        'Select options...'
      );
    });

    it('should handle disabled state', () => {
      render(<MultiSelect {...defaultProps} disabled />);

      expect(screen.getByTestId('react-select')).toHaveAttribute('data-disabled', 'true');
    });

    it('should apply custom className', () => {
      render(<MultiSelect {...defaultProps} className="custom-class" />);

      expect(screen.getByTestId('react-select')).toHaveClass('w-full', 'text-base', 'custom-class');
    });
  });

  describe('Interaction Handling', () => {
    it('should call onChange when option is selected', () => {
      render(<MultiSelect {...defaultProps} />);

      fireEvent.click(screen.getByText('Option 1'));

      expect(mockOnChange).toHaveBeenCalledWith([mockOptions[0]]);
    });

    it('should call onMenuOpen when menu opens', () => {
      render(<MultiSelect {...defaultProps} onMenuOpen={mockOnMenuOpen} />);

      fireEvent.click(screen.getByTestId('menu-open'));

      expect(mockOnMenuOpen).toHaveBeenCalled();
    });

    it('should call onMenuClose when menu closes', () => {
      render(<MultiSelect {...defaultProps} onMenuClose={mockOnMenuClose} />);

      fireEvent.click(screen.getByTestId('menu-close'));

      expect(mockOnMenuClose).toHaveBeenCalled();
    });
  });

  describe('Custom Remove Component', () => {
    it('should render custom remove button with X icon', () => {
      const selectedValues = [mockOptions[0]];

      render(<MultiSelect {...defaultProps} value={selectedValues} />);

      expect(screen.getByTestId('custom-remove')).toBeInTheDocument();
      expect(screen.getByTestId('x-icon')).toBeInTheDocument();
      expect(screen.getByTestId('x-icon')).toHaveClass('h-3', 'w-3', 'cursor-pointer');
    });
  });
});
