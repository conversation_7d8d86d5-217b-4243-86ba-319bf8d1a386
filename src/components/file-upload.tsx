import React, { useState, useCallback } from 'react';
import { useDropzone } from 'react-dropzone';
import { X, FileImage, FileUp } from 'lucide-react';
import { Progress } from './ui/progress';
import { Checkbox } from './ui/checkbox';
import { cn } from '@/lib/utils';
import { useTranslation } from 'react-i18next';

export enum FileType {
  Image = 'image',
  File = 'file',
  Video = 'video',
}

export interface FileUploadProps {
  value?: (File | string)[]; // Can be File objects or URLs
  onChange?: (files: (File | string)[]) => void;
  onUrlChange?: (url: string) => void;
  onUpload?: (file: File) => Promise<void>;
  maxSize?: number; // in MB
  showUrlOption?: boolean;
  className?: string;
  multiple?: boolean;
  accept?: Record<string, string[]> | string; // MIME types or string like 'image/*'
  uploadIcon?: React.ReactNode;
  uploadLabel?: string;
  filePreviewIcon?: React.ReactNode;
  showProgress?: boolean;
  readOnly?: boolean;
}

export const FileUpload: React.FC<FileUploadProps> = ({
  value = [],
  onChange,
  onUrlChange,
  onUpload,
  maxSize = 5,
  showUrlOption = false,
  className,
  multiple = false,
  accept = { 'image/*': [], 'video/*': [] }, // Allow images + videos by default
  uploadIcon = <FileUp className="w-8 h-8 mb-1" />,
  uploadLabel,
  filePreviewIcon = <FileImage className="w-6 h-6 text-tertiary-400" />,
  showProgress = false,
  readOnly = false,
}) => {
  const { t } = useTranslation();
  const [error, setError] = useState<string | null>(null);
  const [showUrlInput, setShowUrlInput] = useState(false);
  const [urlValue, setUrlValue] = useState('');
  const [uploadProgress, setUploadProgress] = useState<Record<string, number>>({});
  const [isUploading, setIsUploading] = useState<Record<string, boolean>>({});

  const defaultUploadLabel =
    uploadLabel || t('common.clickOrDragFiles', { plural: multiple ? 's' : '' });

  const onDrop = useCallback(
    async (acceptedFiles: File[], fileRejections: any[]) => {
      if (readOnly) return;

      if (fileRejections.length > 0) {
        setError(t('fileUpload.someFilesRejected', { size: maxSize }));
        return;
      }

      setError(null);
      const filesToProcess = multiple ? acceptedFiles : [acceptedFiles[0]];

      const newFiles: (File | string)[] = [...value];

      for (const file of filesToProcess) {
        newFiles.push(file);
        if (onUpload) {
          setIsUploading(prev => ({ ...prev, [file.name]: true }));
          try {
            await onUpload(file);
            setUploadProgress(prev => ({ ...prev, [file.name]: 100 }));
          } catch (e: any) {
            setError(`Failed to upload ${file.name}: ${e.message}`);
            setUploadProgress(prev => ({ ...prev, [file.name]: 0 }));
          } finally {
            setIsUploading(prev => ({ ...prev, [file.name]: false }));
          }
        } else if (showProgress) {
          // Simulate progress if no onUpload is provided but progress is requested
          let progress = 0;
          const interval = setInterval(() => {
            progress += 10;
            setUploadProgress(prev => ({ ...prev, [file.name]: progress }));
            if (progress >= 100) clearInterval(interval);
          }, 150);
        }
      }
      onChange?.(newFiles);
    },
    [value, onChange, onUpload, maxSize, multiple, showProgress, readOnly, t]
  );

  const removeFile = useCallback(
    (fileToRemove: File | string) => {
      if (readOnly) return;
      const updatedFiles = value.filter(file => file !== fileToRemove);
      onChange?.(updatedFiles);
      if (typeof fileToRemove === 'string' && fileToRemove.startsWith('blob:')) {
        URL.revokeObjectURL(fileToRemove);
      }
    },
    [value, onChange, readOnly]
  );

  const handleUrlInputChange = useCallback(
    (e: React.ChangeEvent<HTMLInputElement>) => {
      const url = e.target.value;
      setUrlValue(url);
      onUrlChange?.(url);
    },
    [onUrlChange]
  );

  const { getRootProps, getInputProps, isDragActive } = useDropzone({
    onDrop,
    accept: typeof accept === 'string' ? { [accept]: [] } : accept,
    maxSize: maxSize * 1024 * 1024,
    multiple,
    disabled: readOnly || Object.values(isUploading).some(Boolean),
  });

  return (
    <div className={cn('w-full', className)}>
      <div className="relative bg-tertiary-50 w-full rounded-md overflow-hidden">
        {!readOnly && (
          <div
            {...getRootProps({
              className: cn(
                'py-4 border-dashed border rounded-md flex flex-col items-center justify-center transition',
                isDragActive
                  ? 'bg-tertiary-100 border-tertiary-400'
                  : 'hover:bg-tertiary-100 text-tertiary-400'
              ),
              role: 'button',
              'aria-describedby': error ? 'file-upload-error' : undefined,
            })}
          >
            <input {...getInputProps()} />
            {uploadIcon}
            <p className="text-sm">{defaultUploadLabel}</p>
            <p className="text-xs text-tertiary-400 mt-1">{t('editor.uploadFormat')}</p>
          </div>
        )}

        {value.length > 0 && (
          <div className="mt-3 space-y-2">
            {value.map(fileOrUrl => {
              const isFile = fileOrUrl instanceof File;
              const fileUrl = isFile ? URL.createObjectURL(fileOrUrl) : fileOrUrl;
              const fileName = isFile ? fileOrUrl.name : fileOrUrl;
              const isImage = isFile
                ? fileOrUrl.type.startsWith('image/')
                : /\.(jpeg|jpg|gif|png|svg)$/.exec(fileUrl) !== null;
              const currentProgress = uploadProgress[fileName] || 0;
              const currentlyUploading = isUploading[fileName] || false;

              return (
                <div
                  key={`${fileName}-${crypto.randomUUID()}`}
                  className="flex items-center gap-2 p-2 bg-tertiary-50 rounded-md border"
                >
                  {isImage ? (
                    <img src={fileUrl} alt="Preview" className="w-8 h-8 object-cover" />
                  ) : (
                    filePreviewIcon
                  )}
                  <span className="flex-1 truncate text-sm">{fileName}</span>
                  {showProgress && currentlyUploading && currentProgress < 100 && (
                    <div className="flex-1">
                      <Progress value={currentProgress} />
                    </div>
                  )}
                  {!readOnly && (
                    <button
                      onClick={() => removeFile(fileOrUrl)}
                      className="ml-2 p-1 rounded-full hover:bg-tertiary-100"
                      aria-label="Remove file"
                    >
                      <X className="w-5 h-5" />
                    </button>
                  )}
                </div>
              );
            })}
          </div>
        )}
      </div>

      {error && (
        <p id="file-upload-error" className="mt-1 text-sm text-error-500">
          {error}
        </p>
      )}

      {showUrlOption && !readOnly && (
        <div className="mt-2">
          <label htmlFor="url-checkbox" className="flex items-center gap-2">
            <Checkbox
              id="url-checkbox"
              checked={showUrlInput}
              onCheckedChange={() => setShowUrlInput(!showUrlInput)}
            />
            <span className="text-sm">{t('common.addViaUrl')}</span>
          </label>

          {showUrlInput && (
            <input
              type="url"
              placeholder={t('common.enterFileUrl')}
              value={urlValue}
              onChange={handleUrlInputChange}
              className="mt-2 w-full border rounded-md p-2 text-sm"
              aria-label="URL input"
            />
          )}
        </div>
      )}
    </div>
  );
};
