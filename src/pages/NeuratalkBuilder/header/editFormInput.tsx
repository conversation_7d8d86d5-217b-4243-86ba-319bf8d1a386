import React, { FC } from 'react';
import { useFormContext } from 'react-hook-form';
import { FormField, FormItem, FormControl, FormMessage } from '@/components/ui/form';
import { FloatingField, FloatingType } from '@/components/ui/floating-label';
import { DomainOption } from '@/types';
import { FileUpload } from '@/components/file-upload';
import { ImageUp } from 'lucide-react';
import { TFunction } from 'i18next';

interface EditFormProps {
  DOMAIN_OPTIONS: DomainOption[];
  t: TFunction;
}

const EditForm: FC<EditFormProps> = ({ DOMAIN_OPTIONS, t }) => {
  const { control } = useFormContext();
  return (
    <div className="space-y-4">
      {/* Bot Name */}
      <FormField
        control={control}
        name="name"
        render={({ field }) => (
          <FormItem className="flex flex-col">
            <FormControl>
              <FloatingField
                maxLengthAdjacentEle={<FormMessage />}
                maxLength={50}
                label={t('editor.chatbotName')}
                type="text"
                className="rounded-sm"
                {...field}
              />
            </FormControl>
          </FormItem>
        )}
      />
      {/* Domain */}
      <FormField
        control={control}
        name="domain"
        render={({ field }) => (
          <FormItem>
            <FormControl>
              <FloatingField
                maxLengthAdjacentEle={<FormMessage />}
                label={t('editor.domain')}
                as={FloatingType.SELECT}
                options={DOMAIN_OPTIONS}
                {...field}
              />
            </FormControl>
          </FormItem>
        )}
      />
      {/* Description */}
      <FormField
        control={control}
        name="description"
        render={({ field }) => (
          <FormItem className="flex flex-col items-end">
            <FormControl>
              <FloatingField
                maxLength={150}
                maxLengthAdjacentEle={<FormMessage />}
                label={t('editor.description')}
                as={FloatingType.TEXTAREA}
                className="rounded-sm"
                {...field}
              />
            </FormControl>
          </FormItem>
        )}
      />
      <FormField
        control={control}
        name="image"
        disabled
        render={({ field }) => (
          <FormItem className="flex flex-col items-end">
            <FormControl>
              <FileUpload
                uploadLabel={t('editor.uploadImage')}
                maxSize={5}
                uploadIcon={<ImageUp />}
              />
            </FormControl>
            <FormMessage />
          </FormItem>
        )}
      />
    </div>
  );
};

export default EditForm;
