import React, { useState, useEffect } from 'react';
import { MonitorUp, TvMinimalPlay, Wrench, Loader2 } from 'lucide-react';
import { neuraTalkBuilderTabs } from './config';
import { TabsList, TabsTrigger } from '@/components/ui/tabs';
import { cn } from '@/lib/utils';
import { Button } from '@/components/ui/button';
import { useAppDispatch } from '@/hooks/useRedux';
import { togglePreview } from '@/store/slices/uiSlice';
import { useTranslation } from 'react-i18next';
import {
  useBuildBotMutation,
  usePublishBotMutation,
  useGetBuildStatusQuery,
} from '@/store/api/chatBotApi';
import { useToast } from '@/hooks/use-toast';
import SuccessToastMessage from '@/components/SuccessToastMessage';
import { useBotIdParam } from '@/hooks/useRouterParam';
import { BuildStatus } from '@/types/build.type';
import DeleteConfirmationModal from '@/components/DeleteConfirmationModal';
import ErrorModal from '@/components/ErrorModal';
import { getErrorMessage } from '@/lib/utils/errorUtils';

const POLLING_INTERVAL = 5000;

const TabBar = () => {
  const { t } = useTranslation();
  const dispatch = useAppDispatch();
  const [buildBot] = useBuildBotMutation();
  const [publishBot, { isLoading: isPublishing }] = usePublishBotMutation();
  const { toast } = useToast();
  const { botId } = useBotIdParam();
  const [isBuilding, setIsBuilding] = useState(false);
  const [isConfirmationModalOpen, setIsConfirmationModalOpen] = useState(false);
  const [publishError, setPublishError] = useState('');

  const { data: buildStatusData } = useGetBuildStatusQuery(
    { botId },
    { skip: !botId || !isBuilding, pollingInterval: POLLING_INTERVAL }
  );

  useEffect(() => {
    if (buildStatusData) {
      if (buildStatusData.data?.status === BuildStatus.COMPLETED) {
        toast({
          title: <SuccessToastMessage message={t('chatbot.buildSuccess')} />,
        });
        setIsBuilding(false);
      } else if (buildStatusData.data?.status === BuildStatus.FAILED) {
        toast({
          title: t('common.error'),
          description: buildStatusData.data?.errorMessage ?? t('chatbot.buildFailed'),
          variant: 'destructive',
        });
        setIsBuilding(false);
      }
    }
  }, [buildStatusData, t, toast]);

  const confirmPublishBot = async () => {
    try {
      await publishBot({ botId }).unwrap();
      toast({
        title: <SuccessToastMessage message={t('chatbot.botPublishedSuccessfully')} />,
      });
    } catch (error: any) {
      setPublishError(getErrorMessage(error.data));
    }
    setIsConfirmationModalOpen(false);
  };

  const handleBuildBot = async () => {
    if (!botId) return;

    setIsBuilding(true);

    try {
      // Trigger the build POST endpoint
      await buildBot({ botId }).unwrap();
    } catch (buildError: any) {
      setIsBuilding(false);
      toast({
        title: t('common.error'),
        description: buildError.data?.message || t('chatbot.buildFailed'),
        variant: 'destructive',
      });
    }
  };

  return (
    <div className="px-6 border-b bg-white">
      <div className="flex items-start justify-between">
        <TabsList className="flex flex-1 p-0 h-full items-stretch">
          {neuraTalkBuilderTabs.map(tab => {
            const Icon = tab.icon;
            return (
              <TabsTrigger
                key={tab.id}
                value={tab.id}
                className={cn(
                  'p-3 flex-1 border border-b-0 rounded-lg rounded-br-none rounded-bl-none bg-primary-50 text-sm transition-colors flex items-center justify-center',
                  'data-[state=active]:text-primary-500 data-[state=active]:bg-primary-100 data-[state=active]:shadow-none',
                  'data-[state=inactive]:text-tertiary-600 data-[state=inactive]:hover:text-secondary-700 data-[state=inactive]:hover:bg-secondary-100',
                  'overflow-hidden'
                )}
              >
                {Icon}
                <span>{t(tab.labelKey)}</span>
              </TabsTrigger>
            );
          })}
        </TabsList>

        <div className="flex items-center space-x-3 flex-[0.5] justify-end">
          <Button variant={'ghost'} className="uppercase" onClick={() => dispatch(togglePreview())}>
            <TvMinimalPlay className="w-4 h-4 text-primary-500" />
            <span className="text-primary-500">{t('common.preview')}</span>
          </Button>
          <Button variant={'outline'} className="uppercase" onClick={handleBuildBot}>
            {isBuilding ? (
              <Loader2 className="h-4 w-4 animate-spin text-primary-500" />
            ) : (
              <Wrench className="w-4 h-4 text-primary-500" />
            )}

            <span className="text-primary-500">{t('common.build')}</span>
          </Button>
          <Button
            disabled={isPublishing}
            onClick={() => setIsConfirmationModalOpen(true)}
            className="bg-primary-500"
          >
            <MonitorUp className="w-4 h-4" />

            <span>{t('common.publish')}</span>
          </Button>
        </div>
      </div>
      <DeleteConfirmationModal
        isOpen={isConfirmationModalOpen}
        onClose={() => setIsConfirmationModalOpen(false)}
        onConfirm={confirmPublishBot}
        title={t('chatbot.publishConfirmationTitle')}
        description={t('chatbot.publishConfirmationMessage')}
        isDeleting={isPublishing}
        confirmText={t('chatbot.yesPublish')}
      />
      <ErrorModal
        isOpen={!!publishError}
        onClose={() => setPublishError('')}
        title={t('common.error')}
        description={publishError}
      />
    </div>
  );
};

export default TabBar;
