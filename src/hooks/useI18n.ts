import { useTranslation } from 'react-i18next';

export const useI18n = () => {
  const { t, i18n } = useTranslation();

  const changeLanguage = (language: string) => {
    i18n.changeLanguage(language);
  };

  const getCurrentLanguage = () => i18n.language;

  const isRTL = () => {
    // Add RTL language codes here if needed
    const rtlLanguages = ['ar', 'he', 'fa'];
    return rtlLanguages.includes(i18n.language);
  };

  return {
    t,
    changeLanguage,
    getCurrentLanguage,
    isRTL,
    i18n,
  };
};
