import { useEffect, useRef } from 'react';
import { io, Socket } from 'socket.io-client';
import { selectCurrentAccessToken } from '@/store/auth/authSlice';
import { useAppSelector } from '@/hooks/useRedux';

export const useWebSocket = (url: string) => {
  const socketRef = useRef<Socket | null>(null);
  const accessToken = useAppSelector(selectCurrentAccessToken);
  const userId = useRef<string>(`user-${Date.now()}`);

  useEffect(() => {
    const socket = io(url, {
      transports: ['websocket'],
      query: {
        token: accessToken,
        userId: userId.current,
        departmentId: `department-${Date.now()}`,
      },
    });

    socketRef.current = socket;

    socket.on('connect', () => {
      console.log('WebSocket connected:', socket.id);
    });

    socket.on('disconnect', () => {
      console.log('WebSocket disconnected');
    });

    socket.on('error', (error: Error) => {
      console.error('WebSocket error:', error);
    });

    return () => {
      if (socket) {
        socket.disconnect();
        socketRef.current = null;
      }
    };
  }, [url]);

  const emit = (event: string, data: any) => {
    socketRef.current?.emit(event, data);
  };

  const on = (event: string, handler: (...args: any[]) => void) => {
    socketRef.current?.on(event, handler);
    return () => socketRef.current?.off(event, handler);
  };

  return { socket: socketRef.current, emit, on, userId: userId.current };
};
