/**
 * CACHE PATTERNS - Real-world implementations for your specific APIs
 * 
 * This file provides ready-to-use cache configurations for all the patterns
 * found in your store APIs, addressing the specific gaps you mentioned.
 */

import { createRobustCacheSystem, createMultiParamEntityCache } from './robustCacheSystem';
import { apiSlice } from '../../store/apiSlice';
import {
  IntentUtteranceTranslation,
  IntentUtteranceGetAllParams,
  FaqTranslation,
  Entity,
  Bot,
  FlowNode,
  Channel,
  Language,
  BotLanguage,
} from '@/types';

// Pattern 1: Standard Entity with Simple ID (Entity, Bot, Flow)
export const createStandardEntityPattern = <T extends { id: string }>(
  entityType: string,
  listEndpoint?: string,
  itemEndpoint?: string
) => {
  return createRobustCacheSystem<T, any, { id: string }, { id: string }>({
    entityType: entityType as any,
    apiInstance: apiSlice,
    argumentMapper: {
      entityToQueryArgs: (entity) => ({ id: entity.id }),
      mutationArgToId: (arg) => arg.id,
      getEntityId: (entity) => entity.id,
    },
    endpoints: {
      listEndpoint: listEndpoint || `get${entityType}s`,
      itemEndpoint: itemEndpoint || `get${entityType}`,
    },
    getSerializeQueryArgs: ({ queryArgs }) => 
      `${entityType.toLowerCase()}_${JSON.stringify(queryArgs)}`,
  });
};

// Pattern 2: Multi-Parameter Queries (Utterance Translations)
export const createUtteranceTranslationCache = () => {
  return createRobustCacheSystem<
    IntentUtteranceTranslation,
    'IntentUtteranceTranslation',
    IntentUtteranceGetAllParams,
    any
  >({
    entityType: 'IntentUtteranceTranslation',
    apiInstance: apiSlice,
    argumentMapper: {
      // Map entity back to query args for cache updates
      entityToQueryArgs: (entity) => ({
        intentId: entity.intentId,
        langId: entity.langId,
        query: { filter: { id: { eq: entity.utteranceId } } },
      }),
      mutationArgToId: (arg) => arg.id,
      getEntityId: (entity) => entity.id,
      getGroupId: (entity) => entity.utteranceId, // Group by utteranceId
    },
    endpoints: {
      listEndpoint: 'getIntentUtteranceTranslations',
      itemEndpoint: 'getIntentUtteranceTranslation',
      // Handle the special translation endpoint
      customEndpoints: {
        getTranslationByUtteranceIdAndLangId: {
          argMapper: (entity) => ({
            utteranceId: entity.utteranceId,
            langId: entity.langId,
          }),
          shouldUpdate: (queryArgs, entity) => 
            queryArgs.utteranceId === entity.utteranceId && 
            queryArgs.langId === entity.langId,
        },
      },
    },
    getSerializeQueryArgs: ({ queryArgs, endpointName }) => {
      if (endpointName === 'getTranslationByUtteranceIdAndLangId') {
        const { utteranceId, langId } = queryArgs as any;
        return `utterance-translation_${utteranceId}_${langId}`;
      }
      const { intentId, langId, query = {} } = queryArgs;
      return `utterance-translations_${intentId}_${langId}_${JSON.stringify(query)}`;
    },
    cacheUpdateStrategy: {
      updateLists: true,
      updateItems: true,
      updateRelated: true,
      shouldUpdate: (queryArgs, entity) => 
        queryArgs.intentId === entity.intentId && queryArgs.langId === entity.langId,
    },
    invalidationRules: [
      {
        strategy: 'individual',
        getTargets: (entity) => [entity.id],
      },
      {
        strategy: 'group',
        getTargets: (entity) => [entity.utteranceId],
        condition: (entity) => Boolean(entity.utteranceId),
      },
    ],
    debugMode: process.env.NODE_ENV === 'development',
  });
};

// Pattern 3: FAQ Translations (Similar to Utterances but different structure)
export const createFaqTranslationCache = () => {
  return createRobustCacheSystem<
    FaqTranslation,
    'FaqTranslation',
    { categoryId: string; langId: string },
    any
  >({
    entityType: 'FaqTranslation',
    apiInstance: apiSlice,
    argumentMapper: {
      entityToQueryArgs: (entity) => ({
        categoryId: entity.categoryId,
        langId: entity.langId,
      }),
      mutationArgToId: (arg) => arg.id,
      getEntityId: (entity) => entity.id,
      getGroupId: (entity) => entity.faqId, // Group by faqId
    },
    endpoints: {
      listEndpoint: 'getFaqsByCategoryAndLanguage',
      customEndpoints: {
        getTranslationByFaqIdAndLangId: {
          argMapper: (entity) => ({
            faqId: entity.faqId,
            langId: entity.langId,
          }),
          shouldUpdate: (queryArgs, entity) => 
            queryArgs.faqId === entity.faqId && queryArgs.langId === entity.langId,
        },
      },
    },
    getSerializeQueryArgs: ({ queryArgs }) => 
      `faq-translations_${queryArgs.categoryId}_${queryArgs.langId}`,
    cacheUpdateStrategy: {
      shouldUpdate: (queryArgs, entity) => 
        queryArgs.categoryId === entity.categoryId && queryArgs.langId === entity.langId,
    },
    invalidationRules: [
      {
        strategy: 'individual',
        getTargets: (entity) => [entity.id],
      },
      {
        strategy: 'group',
        getTargets: (entity) => [entity.faqId],
        condition: (entity) => Boolean(entity.faqId),
      },
    ],
  });
};

// Pattern 4: Bot-scoped Entities (Entities that belong to a bot)
export const createBotScopedEntityCache = <T extends { id: string; botId: string }>(
  entityType: string
) => {
  return createRobustCacheSystem<T, any, { botId: string }, any>({
    entityType: entityType as any,
    apiInstance: apiSlice,
    argumentMapper: {
      entityToQueryArgs: (entity) => ({ botId: entity.botId }),
      mutationArgToId: (arg) => arg.id,
      getEntityId: (entity) => entity.id,
      getGroupId: (entity) => entity.botId, // Group by botId
    },
    endpoints: {
      listEndpoint: `get${entityType}s`,
      itemEndpoint: `get${entityType}`,
    },
    getSerializeQueryArgs: ({ queryArgs }) => 
      `${entityType.toLowerCase()}_${queryArgs.botId}`,
    cacheUpdateStrategy: {
      shouldUpdate: (queryArgs, entity) => queryArgs.botId === entity.botId,
    },
    invalidationRules: [
      {
        strategy: 'individual',
        getTargets: (entity) => [entity.id],
      },
      {
        strategy: 'group',
        getTargets: (entity) => [entity.botId],
      },
    ],
  });
};

// Pattern 5: No-ID Queries (Languages, Channels - read-only lists)
export const createReadOnlyListCache = <T>(entityType: string) => {
  return createRobustCacheSystem<T, any, Record<string, any>, never>({
    entityType: entityType as any,
    apiInstance: apiSlice,
    argumentMapper: {
      // No mutations for read-only entities
    },
    endpoints: {
      listEndpoint: `get${entityType}s`,
    },
    getSerializeQueryArgs: ({ queryArgs }) => 
      `${entityType.toLowerCase()}_${JSON.stringify(queryArgs)}`,
    cacheUpdateStrategy: {
      updateLists: false, // Read-only, no updates needed
    },
  });
};

// Pattern 6: Complex Composite Keys (Bot Channels)
export const createBotChannelCache = () => {
  return createRobustCacheSystem<
    any,
    'BotChannel',
    { botId: string; channelId?: string },
    any
  >({
    entityType: 'BotChannel',
    apiInstance: apiSlice,
    argumentMapper: {
      entityToQueryArgs: (entity) => ({
        botId: entity.botId,
        channelId: entity.channelId,
      }),
      getEntityId: (entity) => `${entity.botId}-${entity.channelId}`,
      getGroupId: (entity) => entity.botId,
    },
    endpoints: {
      listEndpoint: 'getAllBotChannels',
      customEndpoints: {
        getBotChannelDetails: {
          argMapper: (entity) => ({
            botId: entity.botId,
            channelId: entity.channelId,
          }),
        },
      },
    },
    getSerializeQueryArgs: ({ queryArgs }) => {
      if (queryArgs.channelId) {
        return `bot-channel_${queryArgs.botId}_${queryArgs.channelId}`;
      }
      return `bot-channels_${queryArgs.botId}`;
    },
    invalidationRules: [
      {
        strategy: 'individual',
        getTargets: (entity) => [`${entity.botId}-${entity.channelId}`],
      },
      {
        strategy: 'group',
        getTargets: (entity) => [entity.botId],
      },
    ],
  });
};

// Pattern 7: Flow with App Context
export const createFlowCache = () => {
  return createRobustCacheSystem<
    FlowNode,
    'Flow',
    { botId?: string; appId?: string; id?: string },
    any
  >({
    entityType: 'Flow',
    apiInstance: apiSlice,
    argumentMapper: {
      entityToQueryArgs: (entity) => ({
        appId: entity.appId,
        id: entity.id,
      }),
      mutationArgToId: (arg) => arg.id,
      getEntityId: (entity) => entity.id,
      getGroupId: (entity) => entity.appId,
    },
    endpoints: {
      listEndpoint: 'getFlows',
      itemEndpoint: 'getFlowById',
    },
    getSerializeQueryArgs: ({ queryArgs }) => {
      if (queryArgs.botId) {
        return `flows_${queryArgs.botId}`;
      }
      if (queryArgs.appId && queryArgs.id) {
        return `flow_${queryArgs.appId}_${queryArgs.id}`;
      }
      return `flows_${JSON.stringify(queryArgs)}`;
    },
    cacheUpdateStrategy: {
      shouldUpdate: (queryArgs, entity) => {
        if (queryArgs.botId && entity.appId) {
          return queryArgs.botId === entity.appId;
        }
        return true;
      },
    },
    invalidationRules: [
      {
        strategy: 'individual',
        getTargets: (entity) => [entity.id],
      },
      {
        strategy: 'group',
        getTargets: (entity) => [entity.appId],
        condition: (entity) => Boolean(entity.appId),
      },
    ],
  });
};

// Ready-to-use cache instances
export const cacheInstances = {
  // Standard entities
  entity: createBotScopedEntityCache<Entity>('Entity'),
  bot: createStandardEntityPattern<Bot>('Bot'),
  flow: createFlowCache(),
  
  // Complex multi-parameter entities
  utteranceTranslation: createUtteranceTranslationCache(),
  faqTranslation: createFaqTranslationCache(),
  
  // Read-only lists
  language: createReadOnlyListCache<Language>('Language'),
  channel: createReadOnlyListCache<Channel>('Channel'),
  
  // Composite key entities
  botChannel: createBotChannelCache(),
  botLanguage: createStandardEntityPattern<BotLanguage>('BotLanguage'),
};

// Migration helper - converts old optimizedTags usage to new system
export const migrateToRobustCache = (
  entityType: string,
  pattern: 'standard' | 'multiParam' | 'botScoped' | 'readOnly' | 'composite'
) => {
  switch (pattern) {
    case 'standard':
      return createStandardEntityPattern(entityType);
    case 'multiParam':
      return createUtteranceTranslationCache(); // Use as template
    case 'botScoped':
      return createBotScopedEntityCache(entityType);
    case 'readOnly':
      return createReadOnlyListCache(entityType);
    case 'composite':
      return createBotChannelCache(); // Use as template
    default:
      return createStandardEntityPattern(entityType);
  }
};

/**
 * USAGE EXAMPLES:
 * 
 * // Replace your existing mixins with:
 * const utteranceMixins = cacheInstances.utteranceTranslation;
 * const entityMixins = cacheInstances.entity;
 * const botMixins = cacheInstances.bot;
 * 
 * // Then use in your API:
 * updateUtteranceTranslation: builder.mutation({
 *   query: ({ id, ...body }) => ({ url: `/utterances/${id}`, method: 'PUT', body }),
 *   ...utteranceMixins.update(),
 * }),
 * 
 * // The system automatically handles:
 * ✅ Different argument structures between endpoints
 * ✅ Complex multi-parameter queries
 * ✅ Group-based invalidation
 * ✅ Custom endpoint patterns
 * ✅ Composite keys and nested parameters
 * ✅ Read-only vs mutable entities
 */