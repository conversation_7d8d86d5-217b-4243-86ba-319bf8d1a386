/**
 * Tests for the enhanced optimizedTags system
 * Validates all the new patterns and edge cases
 */

import {
  createOptimizedTags,
  createEntityMixins,
  createSimpleEntityMixins,
  createListOnlyMixins,
  createArgumentMappers,
  createEndpointConfigs,
} from '../optimizedTags';

// Mock API instance
const mockApiInstance = {
  util: {
    updateQueryData: jest.fn(),
    prefetch: jest.fn(),
    invalidateTags: jest.fn(),
  },
  endpoints: {
    getEntities: {
      select: jest.fn(),
      initiate: jest.fn(() => ({ data: { data: { id: '1', name: 'test' } } })),
    },
    getEntity: {
      select: jest.fn(),
      initiate: jest.fn(() => ({ data: { data: { id: '1', name: 'test' } } })),
    },
  },
};

describe('Enhanced OptimizedTags System', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('createOptimizedTags', () => {
    it('should create tags with enhanced invalidation rules', () => {
      const tags = createOptimizedTags({
        entityType: 'TestEntity',
        invalidationRules: [
          {
            strategy: 'individual',
            getTargets: (entity: any) => [entity.id],
          },
        ],
        debugMode: true,
      });

      expect(tags.providesList()).toEqual([{ type: 'TestEntity', id: 'LIST' }]);
      expect(tags.providesItem({}, {}, { id: '123' })).toEqual([
        { type: 'TestEntity', id: '123' },
      ]);
    });

    it('should handle group invalidation', () => {
      const tags = createOptimizedTags({
        entityType: 'TestEntity',
        groupConfigs: [
          {
            groupKey: 'categoryId',
            getGroupId: (entity: any) => entity.categoryId,
          },
        ],
      });

      const groupTags = tags.invalidateByGroup('categoryId', 'cat-123');
      expect(groupTags).toEqual([{ type: 'TestEntity', id: 'categoryId:cat-123' }]);
    });
  });

  describe('Argument Mappers', () => {
    it('should create ID extraction mapper', () => {
      const mapper = createArgumentMappers.withIdExtraction(
        (arg: any) => arg.customId,
        (arg: any) => ({ id: arg.customId })
      );

      expect(mapper.extractId!({ customId: '123' })).toBe('123');
      expect(mapper.mapToQueryArgs!({ customId: '123' })).toEqual({ id: '123' });
    });

    it('should create multi-parameter mapper', () => {
      const mapper = createArgumentMappers.multiParameter((arg: any) => ({
        param1: arg.value1,
        param2: arg.value2,
      }));

      const result = mapper.mapToQueryArgs!({ value1: 'a', value2: 'b' });
      expect(result).toEqual({ param1: 'a', param2: 'b' });
    });

    it('should create list-only mapper', () => {
      const mapper = createArgumentMappers.listOnly();
      expect(mapper.shouldUpdateCache!()).toBe(true);
    });

    it('should create custom ID field mapper', () => {
      const mapper = createArgumentMappers.customIdField('entityId');
      expect(mapper.extractId!({ entityId: '456' })).toBe('456');
    });
  });

  describe('Endpoint Configs', () => {
    it('should create standard config', () => {
      const config = createEndpointConfigs.standard('Entity');
      expect(config).toEqual({
        listEndpointName: 'getEntitys',
        itemEndpointName: 'getEntity',
        hasItemEndpoint: true,
        itemPrefetch: false,
      });
    });

    it('should create list-only config', () => {
      const config = createEndpointConfigs.listOnly('Language');
      expect(config).toEqual({
        listEndpointName: 'getLanguages',
        hasItemEndpoint: false,
      });
    });

    it('should create custom config', () => {
      const config = createEndpointConfigs.custom('getCustomList', 'getCustomItem');
      expect(config).toEqual({
        listEndpointName: 'getCustomList',
        itemEndpointName: 'getCustomItem',
        hasItemEndpoint: true,
      });
    });
  });

  describe('createSimpleEntityMixins', () => {
    it('should create mixins for standard CRUD pattern', () => {
      const mixins = createSimpleEntityMixins(
        'Entity',
        mockApiInstance,
        ({ queryArgs }) => `entities_${JSON.stringify(queryArgs)}`
      );

      expect(mixins.paginated).toBeDefined();
      expect(mixins.item).toBeDefined();
      expect(mixins.create).toBeDefined();
      expect(mixins.update).toBeDefined();
      expect(mixins.delete).toBeDefined();
      expect(mixins.config.hasItemEndpoint).toBe(true);
    });
  });

  describe('createListOnlyMixins', () => {
    it('should create mixins for list-only endpoints', () => {
      const mixins = createListOnlyMixins(
        'Language',
        mockApiInstance,
        ({ queryArgs }) => `languages_${JSON.stringify(queryArgs)}`
      );

      expect(mixins.paginated).toBeDefined();
      expect(mixins.item).toBeUndefined(); // No item endpoint
      expect(mixins.create).toBeDefined();
      expect(mixins.update).toBeDefined();
      expect(mixins.delete).toBeDefined();
      expect(mixins.config.hasItemEndpoint).toBe(false);
    });
  });

  describe('createEntityMixins - Complex Scenarios', () => {
    it('should handle multi-parameter endpoints', () => {
      const mixins = createEntityMixins({
        entityType: 'Translation',
        apiInstance: mockApiInstance,
        getSerializeQueryArgs: ({ queryArgs }) => `translation_${JSON.stringify(queryArgs)}`,
        options: {
          listEndpointName: 'getTranslations',
          itemEndpointName: 'getTranslationByParams',
        },
        argumentMapper: createArgumentMappers.multiParameter((entity: any) => ({
          entityId: entity.entityId,
          langId: entity.langId,
        })),
      });

      expect(mixins.config.itemEndpointName).toBe('getTranslationByParams');
      expect(mixins.config.hasItemEndpoint).toBe(true);
    });

    it('should handle custom ID extraction', () => {
      const mixins = createEntityMixins({
        entityType: 'Flow',
        apiInstance: mockApiInstance,
        getSerializeQueryArgs: ({ queryArgs }) => `flows_${JSON.stringify(queryArgs)}`,
        argumentMapper: {
          extractId: (arg: any) => arg.flowId || arg.id,
          mapToQueryArgs: (arg: any) => ({
            appId: arg.appId,
            id: arg.flowId || arg.id,
          }),
        },
      });

      expect(mixins.config.entityType).toBe('Flow');
    });

    it('should handle group configurations', () => {
      const mixins = createEntityMixins({
        entityType: 'Intent',
        apiInstance: mockApiInstance,
        getSerializeQueryArgs: ({ queryArgs }) => `intents_${JSON.stringify(queryArgs)}`,
        groupConfigs: [
          {
            groupKey: 'botId',
            getGroupId: (entity: any) => entity.botId,
            relatedEntityTypes: ['Intent'],
          },
        ],
      });

      const groupTags = mixins.invalidateByGroup('botId', 'bot-123');
      expect(groupTags).toEqual([{ type: 'Intent', id: 'botId:bot-123' }]);
    });
  });

  describe('Mutation Mixins', () => {
    let mixins: any;

    beforeEach(() => {
      mixins = createEntityMixins({
        entityType: 'TestEntity',
        apiInstance: mockApiInstance,
        getSerializeQueryArgs: ({ queryArgs }) => `test_${JSON.stringify(queryArgs)}`,
        options: {
          itemPrefetch: true,
        },
      });
    });

    it('should handle create mutations with prefetch', async () => {
      const createMixin = mixins.create();
      const mockApi = {
        dispatch: jest.fn(),
        queryFulfilled: Promise.resolve({
          data: { success: true, data: { id: '123', name: 'test' } },
        }),
        getState: jest.fn(() => ({
          api: {
            queries: {
              'test-query': {
                endpointName: 'getTestEntitys',
                originalArgs: { filter: {} },
              },
            },
          },
        })),
      };

      await createMixin.onQueryStarted({ name: 'test' }, mockApi);

      expect(mockApi.dispatch).toHaveBeenCalled();
    });

    it('should handle update mutations with error recovery', async () => {
      const updateMixin = mixins.update();
      const mockApi = {
        dispatch: jest.fn(() => ({ undo: jest.fn() })),
        queryFulfilled: Promise.reject(new Error('Update failed')),
      };

      await updateMixin.onQueryStarted({ id: '123', name: 'updated' }, mockApi);

      expect(mockApi.dispatch).toHaveBeenCalled();
    });

    it('should handle delete mutations with cache cleanup', async () => {
      const deleteMixin = mixins.delete();
      const mockApi = {
        dispatch: jest.fn(() => ({ undo: jest.fn() })),
        queryFulfilled: Promise.resolve(),
        getState: jest.fn(() => ({
          api: {
            queries: {
              'test-query': {
                endpointName: 'getTestEntitys',
                originalArgs: { filter: {} },
              },
            },
          },
        })),
      };

      await deleteMixin.onQueryStarted({ id: '123' }, mockApi);

      expect(mockApi.dispatch).toHaveBeenCalled();
    });
  });

  describe('Edge Cases', () => {
    it('should handle entities without ID field', () => {
      const mixins = createEntityMixins({
        entityType: 'Config',
        apiInstance: mockApiInstance,
        getSerializeQueryArgs: ({ queryArgs }) => `config_${JSON.stringify(queryArgs)}`,
        argumentMapper: {
          extractId: (arg: any) => arg.configKey || 'default',
        },
      });

      expect(mixins.config.entityType).toBe('Config');
    });

    it('should handle endpoints without item queries', () => {
      const mixins = createEntityMixins({
        entityType: 'Analytics',
        apiInstance: mockApiInstance,
        getSerializeQueryArgs: ({ queryArgs }) => `analytics_${JSON.stringify(queryArgs)}`,
        options: {
          hasItemEndpoint: false,
        },
      });

      expect(mixins.item).toBeUndefined();
      expect(mixins.config.hasItemEndpoint).toBe(false);
    });

    it('should handle bulk operations', () => {
      const mixins = createEntityMixins({
        entityType: 'BulkEntity',
        apiInstance: mockApiInstance,
        getSerializeQueryArgs: ({ queryArgs }) => `bulk_${JSON.stringify(queryArgs)}`,
        argumentMapper: {
          extractId: (arg: any) => {
            if (arg.ids && Array.isArray(arg.ids)) {
              return `bulk_${arg.ids.join(',')}`;
            }
            return arg.id;
          },
        },
      });

      expect(mixins.config.entityType).toBe('BulkEntity');
    });
  });
});