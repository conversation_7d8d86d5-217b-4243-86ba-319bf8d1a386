/**
 * ROBUST CACHE SYSTEM - Handles All Edge Cases
 * 
 * This system addresses the gaps in the current solution:
 * 1. Different argument structures between get-item and update endpoints
 * 2. Get-item endpoints without ID in arguments
 * 3. Multiple query parameters and complex argument structures
 * 4. Composite keys and nested parameters
 * 5. Flexible invalidation strategies
 */

import { ApiResponse, PaginatedResponse } from '@/types';
import { TagDescription, SerializeQueryArgs } from '@reduxjs/toolkit/query';

export type FlexibleApiInstance = {
  util: { updateQueryData: any; prefetch: any; invalidateTags: any };
  endpoints: {
    [key: string]: { select: (args: any) => (state: any) => any; initiate: (args: any) => any };
  };
};

// Enhanced argument mapping system
export interface ArgumentMapper<Entity, QueryArg, MutationArg> {
  // Maps entity to query arguments for get-item endpoint
  entityToQueryArgs?: (entity: Entity) => QueryArg;
  // Maps mutation arguments to entity ID
  mutationArgToId?: (arg: MutationArg) => string;
  // Maps mutation arguments to query arguments for cache updates
  mutationArgToQueryArgs?: (arg: MutationArg) => QueryArg;
  // Custom ID extractor for complex scenarios
  getEntityId?: (entity: Entity) => string;
  // Group ID extractor for related entities
  getGroupId?: (entity: Entity) => string | null;
}

// Flexible endpoint configuration
export interface EndpointConfig {
  listEndpoint?: string;
  itemEndpoint?: string;
  // For endpoints that don't follow standard naming
  customEndpoints?: {
    [key: string]: {
      argMapper: (entity: any) => any;
      shouldUpdate?: (queryArgs: any, entity: any) => boolean;
    };
  };
}

// Cache update strategy for different scenarios
export interface CacheUpdateStrategy<Entity, QueryArg> {
  // Standard list updates (most common)
  updateLists?: boolean;
  // Update specific item caches
  updateItems?: boolean;
  // Update related caches (group-based)
  updateRelated?: boolean;
  // Custom update logic
  customUpdate?: (entity: Entity, queryArgs: QueryArg, api: any) => void;
  // Conditional update logic
  shouldUpdate?: (queryArgs: QueryArg, entity: Entity) => boolean;
}

// Comprehensive invalidation rules
export interface InvalidationRule<Entity = any> {
  strategy: 'individual' | 'group' | 'conditional' | 'cascade' | 'selective' | 'custom';
  getTargets: (entity: Entity, context?: any) => string[];
  condition?: (entity: Entity, context?: any) => boolean;
  cascadeRules?: InvalidationRule<Entity>[];
  customInvalidation?: (entity: Entity, api: any) => TagDescription<any>[];
}

// Main configuration interface
export interface RobustCacheConfig<Entity, TagType extends string, QueryArg, MutationArg> {
  entityType: TagType;
  apiInstance: FlexibleApiInstance;
  
  // Argument mapping for complex scenarios
  argumentMapper: ArgumentMapper<Entity, QueryArg, MutationArg>;
  
  // Endpoint configuration
  endpoints: EndpointConfig;
  
  // Cache serialization
  getSerializeQueryArgs: SerializeQueryArgs<QueryArg>;
  
  // Update strategies
  cacheUpdateStrategy?: CacheUpdateStrategy<Entity, QueryArg>;
  
  // Invalidation rules
  invalidationRules?: InvalidationRule<Entity>[];
  
  // Debug mode
  debugMode?: boolean;
}

export const createRobustCacheSystem = <
  Entity extends Record<string, any>,
  TagType extends string,
  QueryArg extends Record<string, any>,
  MutationArg extends Record<string, any>
>(
  config: RobustCacheConfig<Entity, TagType, QueryArg, MutationArg>
) => {
  const {
    entityType,
    apiInstance,
    argumentMapper,
    endpoints,
    getSerializeQueryArgs,
    cacheUpdateStrategy = {},
    invalidationRules = [],
    debugMode = false,
  } = config;

  const debugLog = (message: string, data?: any) => {
    if (debugMode) {
      console.log(`[RobustCache:${entityType}] ${message}`, data);
    }
  };

  // Enhanced tag creation
  const createListTag = (): TagDescription<TagType> => ({ type: entityType, id: 'LIST' });
  const createEntityTag = (id: string): TagDescription<TagType> => ({ type: entityType, id });
  const createGroupTag = (groupId: string): TagDescription<TagType> => ({ 
    type: entityType, 
    id: `GROUP:${groupId}` 
  });
  const createCompositeTag = (key: string, value: string): TagDescription<TagType> => ({ 
    type: entityType, 
    id: `${key}:${value}` 
  });

  // Flexible ID extraction
  const extractId = (entity: Entity, fallback?: string): string => {
    if (argumentMapper.getEntityId) {
      return argumentMapper.getEntityId(entity);
    }
    return entity.id || entity.uuid || entity._id || fallback || 'unknown';
  };

  // Enhanced cache update system
  const updateCacheData = async (
    entity: Entity,
    operation: 'create' | 'update' | 'delete',
    api: any
  ) => {
    const { dispatch, getState } = api;
    const state = getState();
    const apiState = state.api;

    debugLog(`Updating cache for ${operation}`, { entity, entityId: extractId(entity) });

    // Update list caches
    if (cacheUpdateStrategy.updateLists !== false) {
      Object.keys(apiState.queries).forEach(key => {
        const query = apiState.queries[key];
        const listEndpoint = endpoints.listEndpoint || `get${entityType}s`;
        
        if (query?.endpointName === listEndpoint) {
          const queryArgs = query.originalArgs;
          
          // Check if this cache should be updated
          const shouldUpdate = cacheUpdateStrategy.shouldUpdate?.(queryArgs, entity) ?? true;
          
          if (shouldUpdate) {
            dispatch(
              apiInstance.util.updateQueryData(
                listEndpoint,
                queryArgs,
                (draft: ApiResponse<PaginatedResponse<Entity>>) => {
                  updateListCache(draft, entity, operation, queryArgs);
                }
              )
            );
          }
        }
      });
    }

    // Update item caches
    if (cacheUpdateStrategy.updateItems !== false && operation !== 'delete') {
      const itemEndpoint = endpoints.itemEndpoint || `get${entityType}`;
      
      if (argumentMapper.entityToQueryArgs) {
        const itemQueryArgs = argumentMapper.entityToQueryArgs(entity);
        
        dispatch(
          apiInstance.util.updateQueryData(
            itemEndpoint,
            itemQueryArgs,
            (draft: ApiResponse<Entity>) => {
              if (draft.data) {
                Object.assign(draft.data, entity);
              }
            }
          )
        );
      }
    }

    // Update custom endpoints
    if (endpoints.customEndpoints) {
      Object.entries(endpoints.customEndpoints).forEach(([endpointName, endpointConfig]) => {
        const customArgs = endpointConfig.argMapper(entity);
        const shouldUpdate = endpointConfig.shouldUpdate?.(customArgs, entity) ?? true;
        
        if (shouldUpdate) {
          dispatch(
            apiInstance.util.updateQueryData(
              endpointName,
              customArgs,
              (draft: any) => {
                // Custom update logic based on endpoint type
                if (draft.data?.items) {
                  updateListCache(draft, entity, operation, customArgs);
                } else if (draft.data) {
                  Object.assign(draft.data, entity);
                }
              }
            )
          );
        }
      });
    }

    // Custom update logic
    if (cacheUpdateStrategy.customUpdate) {
      // Find all relevant query args and apply custom updates
      Object.keys(apiState.queries).forEach(key => {
        const query = apiState.queries[key];
        if (query) {
          cacheUpdateStrategy.customUpdate!(entity, query.originalArgs, api);
        }
      });
    }
  };

  // Enhanced list cache update
  const updateListCache = (
    draft: ApiResponse<PaginatedResponse<Entity>>,
    entity: Entity,
    operation: 'create' | 'update' | 'delete',
    queryArgs: QueryArg
  ) => {
    if (!draft?.data?.items) return;

    const entityId = extractId(entity);
    const existingIndex = draft.data.items.findIndex(item => extractId(item) === entityId);

    switch (operation) {
      case 'create':
        if (existingIndex === -1) {
          draft.data.items.unshift(entity);
          if (draft.data.pagination) {
            draft.data.pagination.total += 1;
          }
        }
        break;
        
      case 'update':
        if (existingIndex !== -1) {
          draft.data.items[existingIndex] = entity;
        }
        break;
        
      case 'delete':
        if (existingIndex !== -1) {
          draft.data.items.splice(existingIndex, 1);
          if (draft.data.pagination) {
            draft.data.pagination.total = Math.max(0, draft.data.pagination.total - 1);
          }
        }
        break;
    }
  };

  // Enhanced invalidation system
  const executeInvalidation = (entity: Entity, operation: 'create' | 'update' | 'delete') => {
    const tags: TagDescription<TagType>[] = [];

    // Standard invalidation
    tags.push(createListTag());
    tags.push(createEntityTag(extractId(entity)));

    // Group-based invalidation
    if (argumentMapper.getGroupId) {
      const groupId = argumentMapper.getGroupId(entity);
      if (groupId) {
        tags.push(createGroupTag(groupId));
      }
    }

    // Rule-based invalidation
    invalidationRules.forEach(rule => {
      if (rule.condition && !rule.condition(entity)) return;

      const targets = rule.getTargets(entity, { operation });
      
      switch (rule.strategy) {
        case 'individual':
          targets.forEach(id => tags.push(createEntityTag(id)));
          break;
        case 'group':
          targets.forEach(groupId => tags.push(createGroupTag(groupId)));
          break;
        case 'selective':
          targets.forEach(target => {
            const [key, value] = target.split(':');
            tags.push(createCompositeTag(key, value));
          });
          break;
        case 'custom':
          if (rule.customInvalidation) {
            tags.push(...rule.customInvalidation(entity, apiInstance));
          }
          break;
      }

      // Cascade invalidation
      if (rule.cascadeRules) {
        rule.cascadeRules.forEach(cascadeRule => {
          const cascadeTargets = cascadeRule.getTargets(entity, { operation });
          cascadeTargets.forEach(target => tags.push(createEntityTag(target)));
        });
      }
    });

    return tags;
  };

  // Mixin generators
  const createMutation = <T extends MutationArg>(
    operation: 'create' | 'update' | 'delete',
    options: {
      getId?: (arg: T) => string;
      getGroupId?: (arg: T) => string;
      customInvalidation?: (arg: T) => TagDescription<TagType>[];
    } = {}
  ) => ({
    invalidatesTags: (_result: any, _error: any, arg: T) => {
      const entityId = options.getId?.(arg) || argumentMapper.mutationArgToId?.(arg) || (arg as any).id;
      const tags = [createEntityTag(entityId)];
      
      if (options.getGroupId) {
        const groupId = options.getGroupId(arg);
        if (groupId) tags.push(createGroupTag(groupId));
      }
      
      if (options.customInvalidation) {
        tags.push(...options.customInvalidation(arg));
      }
      
      return tags;
    },

    async onQueryStarted(arg: T, api: any) {
      const { dispatch, queryFulfilled } = api;
      
      try {
        const { data: response } = await queryFulfilled;
        
        if (response?.success && response?.data) {
          const entity = response.data;
          await updateCacheData(entity, operation, api);
          
          // Execute additional invalidation
          const invalidationTags = executeInvalidation(entity, operation);
          if (invalidationTags.length > 0) {
            dispatch(apiInstance.util.invalidateTags(invalidationTags));
          }
        }
      } catch (error) {
        debugLog(`Error in ${operation} mutation`, error);
      }
    },
  });

  return {
    // Paginated list mixin
    paginated: {
      providesTags: () => [createListTag()],
      serializeQueryArgs: getSerializeQueryArgs,
    },

    // Item mixin
    item: {
      providesTags: (_result: any, _error: any, arg: QueryArg) => {
        const tags = [createListTag()];
        
        // Try to extract ID from various argument structures
        const id = (arg as any).id || (arg as any).uuid || (arg as any).entityId;
        if (id) {
          tags.push(createEntityTag(id));
        }
        
        return tags;
      },
    },

    // Mutation mixins
    create: <T extends MutationArg>(options?: Parameters<typeof createMutation>[1]) => 
      createMutation<T>('create', options),
    
    update: <T extends MutationArg>(options?: Parameters<typeof createMutation>[1]) => 
      createMutation<T>('update', options),
    
    delete: <T extends MutationArg>(options?: Parameters<typeof createMutation>[1]) => 
      createMutation<T>('delete', options),

    // Utility functions
    invalidateEntity: (id: string) => [createEntityTag(id)],
    invalidateGroup: (groupId: string) => [createGroupTag(groupId)],
    invalidateAll: () => [createListTag()],
    
    // Debug utilities
    debugCache: () => {
      debugLog('Cache configuration', {
        entityType,
        endpoints,
        argumentMapper,
        cacheUpdateStrategy,
        invalidationRules,
      });
    },
  };
};

// Preset configurations for common patterns
export const createStandardEntityCache = <Entity extends { id: string }, TagType extends string>(
  entityType: TagType,
  apiInstance: FlexibleApiInstance
) => {
  return createRobustCacheSystem<Entity, TagType, { id: string }, { id: string }>({
    entityType,
    apiInstance,
    argumentMapper: {
      entityToQueryArgs: (entity) => ({ id: entity.id }),
      mutationArgToId: (arg) => arg.id,
      getEntityId: (entity) => entity.id,
    },
    endpoints: {
      listEndpoint: `get${entityType}s`,
      itemEndpoint: `get${entityType}`,
    },
    getSerializeQueryArgs: ({ queryArgs }) => `${entityType.toLowerCase()}_${JSON.stringify(queryArgs)}`,
  });
};

// Preset for complex multi-parameter entities (like utterance translations)
export const createMultiParamEntityCache = <
  Entity extends Record<string, any>,
  TagType extends string,
  QueryArg extends Record<string, any>
>(
  entityType: TagType,
  apiInstance: FlexibleApiInstance,
  config: {
    queryArgMapper: (entity: Entity) => QueryArg;
    serializeArgs: SerializeQueryArgs<QueryArg>;
    groupIdExtractor?: (entity: Entity) => string | null;
  }
) => {
  return createRobustCacheSystem<Entity, TagType, QueryArg, any>({
    entityType,
    apiInstance,
    argumentMapper: {
      entityToQueryArgs: config.queryArgMapper,
      getEntityId: (entity) => entity.id || entity.uuid,
      getGroupId: config.groupIdExtractor,
    },
    endpoints: {
      listEndpoint: `get${entityType}s`,
      itemEndpoint: `get${entityType}`,
    },
    getSerializeQueryArgs: config.serializeArgs,
    invalidationRules: [
      {
        strategy: 'individual',
        getTargets: (entity) => [entity.id || entity.uuid],
      },
      ...(config.groupIdExtractor ? [{
        strategy: 'group' as const,
        getTargets: (entity: Entity) => {
          const groupId = config.groupIdExtractor!(entity);
          return groupId ? [groupId] : [];
        },
        condition: (entity: Entity) => Boolean(config.groupIdExtractor!(entity)),
      }] : []),
    ],
  });
};