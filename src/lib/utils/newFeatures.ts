// NEW FEATURES: How to use targeted and group-based invalidation

import { useDispatch } from 'react-redux';
import { 
  useAssignFlowToIntentMutation,
  useUpdateUtteranceTranslationMutation 
} from '@/store/api';

// FEATURE 1: Targeted Invalidation
// Use case: When assignFlow is called, invalidate only that specific item

export const useTargetedInvalidationExample = () => {
  const dispatch = useDispatch();
  const [assignFlow] = useAssignFlowToIntentMutation();

  const handleAssignFlow = async (intentId: string, flowId: string) => {
    // The assignFlow mutation in intentApi.ts now uses:
    // intentItemMixins.invalidate.item(dispatch, arg.intentId);
    // This will invalidate ONLY the specific intent item, not the entire list
    await assignFlow({ intentId, flowId });
  };

  return { handleAssignFlow };
};

// FEATURE 2: Group-Based Invalidation  
// Use case: When updating utterance translation, invalidate all items with same utteranceId

export const useGroupInvalidationExample = () => {
  const dispatch = useDispatch();
  const [updateUtterance] = useUpdateUtteranceTranslationMutation();

  const handleUpdateUtterance = async (id: string, data: any) => {
    // The utteranceMixins now has getGroupIds: (utterance) => [utterance.utteranceId]
    // When this mutation runs, it will automatically invalidate:
    // 1. The specific item (normal behavior)
    // 2. ALL items that share the same utteranceId (new group behavior)
    await updateUtterance({ id, ...data });
  };

  // Manual group invalidation example
  const handleBulkUpdateByUtteranceId = async (utteranceId: string) => {
    // You can also manually invalidate a group:
    // utteranceMixins.invalidate.group(dispatch, utteranceId);
    // This will re-fetch all translations with that utteranceId
  };

  return { handleUpdateUtterance, handleBulkUpdateByUtteranceId };
};

// How it works:
// 1. TARGETED: Only specific items get invalidated and re-fetched
// 2. GROUP: All items sharing a group identifier get invalidated together
// 3. The existing functionality (pagination, caching, etc.) remains unchanged