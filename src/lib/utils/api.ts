import { PaginationParams } from '@/types';

interface SerializedPagination {
  serialized: Record<string, string>;
  searchParams: string;
}

export const serializePaginationParams = (params: PaginationParams): SerializedPagination => {
  const serialized: Record<string, string> = {};

  for (const key in params) {
    if (Object.prototype.hasOwnProperty.call(params, key)) {
      const value = params[key as keyof PaginationParams];
      if (value != null) {
        if (key === 'filter' || key === 'order') {
          serialized[key] = JSON.stringify(value);
        } else {
          serialized[key] = String(value);
        }
      }
    }
  }
  const searchParams = new URLSearchParams(serialized).toString();
  return { serialized, searchParams };
};
