import { TagDescription } from '@reduxjs/toolkit/query';
import { 
  InvalidationRule, 
  GroupInvalidationConfig, 
  createOptimizedTags,
  InvalidationContext 
} from './optimizedTags';

// Utterance-specific types
export interface UtteranceTranslationEntity {
  id: string;
  utteranceId: string;
  langId: string;
  text: string;
  entities?: Record<string, unknown>;
}

export interface UtteranceInvalidationConfig {
  debugMode?: boolean;
}

// Specialized invalidation rules for utterance translations
export const createUtteranceInvalidationRules = (): InvalidationRule<UtteranceTranslationEntity>[] => [
  // Individual item invalidation
  {
    strategy: 'individual',
    getTargets: (entity) => [entity.id],
  },
  
  // Group invalidation by utteranceId - this is the key feature you requested
  {
    strategy: 'group',
    getTargets: (entity) => [entity.utteranceId],
    condition: (entity) => Boolean(entity.utteranceId),
  },
  
  // Language-specific invalidation
  {
    strategy: 'selective',
    getTargets: (entity) => [`lang:${entity.langId}`],
    condition: (entity) => Boolean(entity.langId),
  },
  
  // Cascade invalidation for related translations
  {
    strategy: 'cascade',
    getTargets: (entity) => [entity.utteranceId],
    condition: (entity, context) => context?.operation === 'update',
    cascadeRules: [
      {
        strategy: 'group',
        getTargets: (entity) => [`utterance-group:${entity.utteranceId}`],
      }
    ],
  },
];

// Group configurations for utterance translations
export const createUtteranceGroupConfigs = (): GroupInvalidationConfig[] => [
  {
    groupKey: 'utteranceId',
    getGroupId: (entity: UtteranceTranslationEntity) => entity.utteranceId,
    relatedEntityTypes: ['IntentUtteranceTranslation'],
  },
  {
    groupKey: 'language',
    getGroupId: (entity: UtteranceTranslationEntity) => entity.langId,
    relatedEntityTypes: ['IntentUtteranceTranslation'],
  },
];

// Specialized utterance invalidation utilities
export const createUtteranceInvalidationUtils = (config: UtteranceInvalidationConfig = {}) => {
  const { debugMode = false } = config;
  
  const invalidationRules = createUtteranceInvalidationRules();
  const groupConfigs = createUtteranceGroupConfigs();
  
  const tags = createOptimizedTags<'IntentUtteranceTranslation'>({
    entityType: 'IntentUtteranceTranslation',
    invalidationRules,
    groupConfigs,
    debugMode,
  });

  return {
    // Invalidate all translations for a specific utterance
    invalidateByUtteranceId: (utteranceId: string): TagDescription<'IntentUtteranceTranslation'>[] => {
      return tags.invalidateByGroup('utteranceId', utteranceId);
    },

    // Invalidate all translations for a specific language
    invalidateByLanguage: (langId: string): TagDescription<'IntentUtteranceTranslation'>[] => {
      return tags.invalidateByGroup('language', langId);
    },

    // Invalidate specific translation and related utterance group
    invalidateTranslationAndGroup: (
      entity: UtteranceTranslationEntity
    ): TagDescription<'IntentUtteranceTranslation'>[] => {
      const individualTags = tags.invalidatesItem(entity.id);
      const groupTags = tags.invalidateByGroup('utteranceId', entity.utteranceId);
      return [...individualTags, ...groupTags];
    },

    // Smart invalidation based on operation type
    smartInvalidate: (
      entity: UtteranceTranslationEntity,
      operation: 'create' | 'update' | 'delete'
    ): TagDescription<'IntentUtteranceTranslation'>[] => {
      return tags.invalidateByRules(entity, operation);
    },

    // Batch invalidation for multiple utterances
    batchInvalidateUtterances: (
      utteranceIds: string[]
    ): TagDescription<'IntentUtteranceTranslation'>[] => {
      return utteranceIds.flatMap(utteranceId => 
        tags.invalidateByGroup('utteranceId', utteranceId)
      );
    },

    // Get all invalidation tags for comprehensive refresh
    invalidateAll: (): TagDescription<'IntentUtteranceTranslation'>[] => {
      return tags.invalidatesList();
    },

    // Utility methods
    tags,
    invalidationRules,
    groupConfigs,
  };
};

// Pre-configured utterance invalidation for common use cases
export const utteranceInvalidation = createUtteranceInvalidationUtils({
  debugMode: process.env.NODE_ENV === 'development',
});

// Helper function for assignFlow use case
export const createAssignFlowInvalidation = () => {
  return {
    // When assignFlow is called, invalidate the specific intent item
    invalidateAssignedFlow: (intentId: string): TagDescription<'IntentItem'>[] => {
      return [{ type: 'IntentItem', id: intentId }];
    },
    
    // Also invalidate related utterances if needed
    invalidateIntentAndUtterances: (
      intentId: string, 
      utteranceIds: string[] = []
    ): Array<TagDescription<'IntentItem' | 'IntentUtteranceTranslation'>> => {
      const intentTags: TagDescription<'IntentItem'>[] = [{ type: 'IntentItem', id: intentId }];
      const utteranceTags: TagDescription<'IntentUtteranceTranslation'>[] = 
        utteranceInvalidation.batchInvalidateUtterances(utteranceIds);
      
      return [...intentTags, ...utteranceTags];
    },
  };
};

export const assignFlowInvalidation = createAssignFlowInvalidation();
