# Optimized RTK Query Pagination & Cache System

A comprehensive, flexible, and high-performance caching solution for RTK Query with advanced invalidation strategies.

## 🚀 Key Features

### 1. Targeted Invalidation
Invalidate specific items without affecting the entire cache:
```typescript
// Only invalidates the specific intent item
assignFlowInvalidation.invalidateAssignedFlow(intentId);
```

### 2. Group-Based Invalidation
Invalidate related items by shared identifiers (e.g., utteranceId):
```typescript
// Invalidates all translations sharing the same utteranceId
utteranceInvalidation.invalidateByUtteranceId(utteranceId);
```

### 3. Flexible Invalidation Strategies
- **Individual**: Target specific items by ID
- **Group**: Target items by shared properties
- **Conditional**: Invalidate based on custom conditions
- **Cascade**: Invalidate related entities automatically
- **Selective**: Fine-grained control over what gets invalidated

### 4. Performance Monitoring
Built-in performance tracking and debugging utilities:
```typescript
// Monitor cache performance in real-time
cacheMonitor.generateReport();

// Debug cache operations with timing
cacheDebugUtils.logOperation('Update', 'Entity', () => operation());
```

## 📋 Quick Start

### Basic Setup

```typescript
import { createEntityMixins, InvalidationRule } from '@/lib/utils/optimizedTags';

// Define invalidation rules for your entity
const invalidationRules: InvalidationRule<YourEntity>[] = [
  {
    strategy: 'individual',
    getTargets: (entity) => [entity.id],
  },
  {
    strategy: 'group',
    getTargets: (entity) => [entity.groupId],
    condition: (entity) => Boolean(entity.groupId),
  },
];

// Create enhanced entity mixins
const entityMixins = createEntityMixins({
  entityType: 'YourEntity',
  apiInstance: apiSlice,
  getSerializeQueryArgs: ({ queryArgs }) => `your-entity_${JSON.stringify(queryArgs)}`,
  invalidationRules,
});
```

### Using with RTK Query

```typescript
export const yourApi = apiSlice.injectEndpoints({
  endpoints: (builder) => ({
    getItems: builder.query({
      query: (params) => ({ url: '/items', params }),
      ...entityMixins.paginated,
    }),
    
    updateItem: builder.mutation({
      query: ({ id, ...body }) => ({
        url: `/items/${id}`,
        method: 'PUT',
        body,
      }),
      ...entityMixins.update(),
    }),
  }),
});
```

## 🎯 Use Cases

### Utterance Translation Invalidation

Perfect for your utterance translation feature:

```typescript
// When updating a translation, invalidate all related translations
const handleUpdateTranslation = async (translationId, utteranceId, updates) => {
  // This automatically invalidates:
  // 1. The specific translation
  // 2. All translations with the same utteranceId
  const result = await updateTranslation({ id: translationId, ...updates });
  
  // The system handles group invalidation automatically
  return result;
};
```

### AssignFlow Optimization

Optimized for your assignFlow use case:

```typescript
// Only invalidates the specific intent, not the entire list
const handleAssignFlow = async (intentId, flowId) => {
  const result = await assignFlowToIntent({ intentId, flowId });
  // Targeted invalidation - no unnecessary re-fetches
  return result;
};
```

## 🔧 Advanced Configuration

### Custom Invalidation Rules

```typescript
const customRules: InvalidationRule<Entity>[] = [
  {
    strategy: 'cascade',
    getTargets: (entity) => [entity.id],
    condition: (entity, context) => context.operation === 'delete',
    cascadeRules: [
      {
        strategy: 'group',
        getTargets: (entity) => [`related:${entity.parentId}`],
      },
    ],
  },
];
```

### Group Configurations

```typescript
const groupConfigs: GroupInvalidationConfig[] = [
  {
    groupKey: 'utteranceId',
    getGroupId: (entity) => entity.utteranceId,
    relatedEntityTypes: ['Translation'],
  },
];
```

## 📊 Performance Monitoring

### Real-time Monitoring

```typescript
// Add event listeners for cache operations
cacheMonitor.addEventListener({
  eventTypes: ['invalidation', 'hit', 'miss'],
  handler: (event) => console.log('Cache event:', event),
});
```

### Performance Analysis

```typescript
// Analyze cache efficiency
const analysis = cacheDebugUtils.analyzeInvalidationEfficiency('EntityType');
console.log('Efficiency:', analysis.efficiency);
console.log('Recommendations:', analysis.recommendations);
```

### Debug Mode

Enable debug mode in development:

```typescript
const entityMixins = createEntityMixins({
  // ... other config
  debugMode: process.env.NODE_ENV === 'development',
});
```

## 🛠️ Migration Guide

### From Old System

1. **Replace imports**:
   ```typescript
   // Old
   import { createOptimizedTags } from './optimizedTags';
   
   // New
   import { createEntityMixins } from './optimizedTags';
   ```

2. **Update configuration**:
   ```typescript
   // Old
   const tags = createOptimizedTags({ entityType: 'Entity' });
   
   // New
   const mixins = createEntityMixins({
     entityType: 'Entity',
     apiInstance: apiSlice,
     getSerializeQueryArgs: ({ queryArgs }) => `entity_${JSON.stringify(queryArgs)}`,
     invalidationRules: [...],
   });
   ```

3. **Use enhanced methods**:
   ```typescript
   // Old
   invalidatesTags: () => tags.invalidatesList(),
   
   // New
   invalidatesTags: (result, error, arg) => mixins.invalidateByRules(arg, 'update'),
   ```

## 🔍 Debugging

### Browser DevTools

In development, access debugging utilities via:

```javascript
// In browser console
__cacheDebug.report(); // Get performance report
__cacheDebug.utils.visualizeCacheState(); // View cache state
__cacheDebug.monitor.getRecentEvents(); // See recent events
```

### Performance Profiling

```typescript
// Profile cache operations
const result = cacheDebugUtils.logOperation(
  'Complex Operation',
  'EntityType',
  () => performComplexCacheOperation()
);
```

## 📈 Best Practices

1. **Use group invalidation** for related data
2. **Implement conditional rules** to avoid unnecessary invalidations
3. **Monitor performance** in development
4. **Batch operations** when possible
5. **Use targeted invalidation** over broad invalidation

## 🤝 Contributing

When adding new invalidation strategies:

1. Define clear invalidation rules
2. Add performance monitoring
3. Include debug utilities
4. Write comprehensive tests
5. Update documentation

## 📚 Examples

See `src/lib/utils/examples/optimizedCacheExamples.ts` for comprehensive usage examples.
