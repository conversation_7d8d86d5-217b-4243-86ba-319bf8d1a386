import { TagDescription } from '@reduxjs/toolkit/query';
import { ApiResponse, PaginatedResponse } from '@/types';

// Core cache operation types
export type CacheOperation = 'create' | 'update' | 'delete' | 'read';
export type InvalidationTrigger = 'mutation' | 'manual' | 'timeout' | 'dependency';

// Enhanced entity interface with flexible properties
export interface CacheableEntity {
  id: string;
  [key: string]: unknown;
}

// Invalidation strategy configuration
export interface InvalidationStrategyConfig<TEntity = CacheableEntity> {
  name: string;
  description?: string;
  priority: number; // Higher priority strategies execute first
  condition?: (entity: TEntity, context: CacheOperationContext) => boolean;
  getTargetIds: (entity: TEntity, context: CacheOperationContext) => string[];
  getTargetTags?: (entity: TEntity, context: CacheOperationContext) => TagDescription<string>[];
}

// Context for cache operations
export interface CacheOperationContext {
  operation: CacheOperation;
  trigger: InvalidationTrigger;
  entityType: string;
  timestamp: number;
  metadata?: Record<string, unknown>;
  relatedEntities?: CacheableEntity[];
}

// Cache relationship definitions
export interface CacheRelationshipDefinition<TEntity = CacheableEntity> {
  relationshipType: 'one-to-one' | 'one-to-many' | 'many-to-many';
  sourceEntityType: string;
  targetEntityType: string;
  getRelatedIds: (entity: TEntity) => string[];
  bidirectional?: boolean;
}

// Group-based invalidation configuration
export interface GroupInvalidationDefinition<TEntity = CacheableEntity> {
  groupName: string;
  groupKey: string;
  getGroupId: (entity: TEntity) => string;
  includeRelated?: boolean;
  relatedEntityTypes?: string[];
  cascadeDepth?: number; // How deep to cascade invalidation
}

// Performance monitoring types
export interface CachePerformanceMetrics {
  entityType: string;
  totalInvalidations: number;
  averageInvalidationTime: number;
  lastInvalidationTimestamp: number;
  hitRate: number;
  missRate: number;
  cacheSize: number;
  memoryUsage?: number;
}

// Cache configuration for different entity types
export interface EntityCacheConfig<TEntity = CacheableEntity> {
  entityType: string;
  ttl?: number; // Time to live in milliseconds
  maxSize?: number; // Maximum number of cached items
  invalidationStrategies: InvalidationStrategyConfig<TEntity>[];
  groupDefinitions?: GroupInvalidationDefinition<TEntity>[];
  relationships?: CacheRelationshipDefinition<TEntity>[];
  enableMetrics?: boolean;
  debugMode?: boolean;
}

// Batch operation types
export interface BatchCacheOperation<TEntity = CacheableEntity> {
  operation: CacheOperation;
  entities: TEntity[];
  context?: Partial<CacheOperationContext>;
}

// Cache update result
export interface CacheUpdateResult {
  success: boolean;
  invalidatedTags: TagDescription<string>[];
  affectedEntities: string[];
  executionTime: number;
  errors?: Error[];
}

// Advanced cache selector types
export interface CacheSelector<TEntity = CacheableEntity, TResult = unknown> {
  name: string;
  selector: (entities: TEntity[]) => TResult;
  dependencies?: string[]; // Entity IDs this selector depends on
  memoize?: boolean;
}

// Cache middleware types
export interface CacheMiddleware<TEntity = CacheableEntity> {
  name: string;
  priority: number;
  beforeInvalidation?: (
    entities: TEntity[], 
    context: CacheOperationContext
  ) => Promise<void> | void;
  afterInvalidation?: (
    result: CacheUpdateResult, 
    context: CacheOperationContext
  ) => Promise<void> | void;
}

// Optimistic update configuration
export interface OptimisticUpdateConfig<TEntity = CacheableEntity> {
  enabled: boolean;
  rollbackOnError: boolean;
  updateFn: (draft: ApiResponse<PaginatedResponse<TEntity>>, entity: TEntity) => void;
  rollbackFn?: (draft: ApiResponse<PaginatedResponse<TEntity>>, entity: TEntity) => void;
}

// Cache synchronization types for multi-tab/window scenarios
export interface CacheSyncConfig {
  enabled: boolean;
  channel?: string; // BroadcastChannel name
  syncStrategies: ('invalidate' | 'update' | 'merge')[];
  conflictResolution?: 'last-write-wins' | 'merge' | 'manual';
}

// Query parameter types for flexible cache keys
export interface FlexibleQueryParams {
  [key: string]: unknown;
  page?: number;
  limit?: number;
  search?: string;
  filters?: Record<string, unknown>;
  sort?: Array<[string, 'asc' | 'desc']>;
}

// Cache key generation strategy
export interface CacheKeyStrategy {
  name: string;
  generateKey: (params: FlexibleQueryParams, entityType: string) => string;
  parseKey?: (key: string) => { params: FlexibleQueryParams; entityType: string } | null;
}

// Event types for cache operations
export interface CacheEvent<TEntity = CacheableEntity> {
  type: 'invalidation' | 'update' | 'miss' | 'hit' | 'error';
  entityType: string;
  entityId?: string;
  timestamp: number;
  data?: TEntity;
  error?: Error;
  metadata?: Record<string, unknown>;
}

// Cache event listener
export interface CacheEventListener<TEntity = CacheableEntity> {
  eventTypes: CacheEvent<TEntity>['type'][];
  handler: (event: CacheEvent<TEntity>) => void;
  once?: boolean;
}

// Export utility type for creating strongly typed cache configurations
export type CreateCacheConfig<TEntity extends CacheableEntity> = EntityCacheConfig<TEntity>;

// Helper type for extracting entity type from cache config
export type ExtractEntityType<T> = T extends EntityCacheConfig<infer U> ? U : never;

// Conditional invalidation predicate
export type InvalidationPredicate<TEntity = CacheableEntity> = (
  entity: TEntity,
  context: CacheOperationContext
) => boolean;

// Tag generation function type
export type TagGenerator<TEntity = CacheableEntity> = (
  entity: TEntity,
  context: CacheOperationContext
) => TagDescription<string>[];
