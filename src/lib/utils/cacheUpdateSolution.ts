
/**
 * SOLUTION: Enhanced Cache System - Fixing the Data Update Issue
 * 
 * The problem: When items are updated, the list data doesn't reflect changes immediately.
 * The solution: Enhanced onQueryStarted handlers that update cache data AND handle invalidation.
 */

import { createEntityMixins, InvalidationRule, GroupInvalidationConfig } from './optimizedTags';
import { apiSlice } from '../../store/apiSlice';

// Example: Utterance Translation with proper cache updates
interface UtteranceTranslation {
  id: string;
  utteranceId: string;
  langId: string;
  text: string;
  entities?: Record<string, unknown>;
}

// Define invalidation rules that handle both invalidation AND cache updates
const utteranceInvalidationRules: InvalidationRule<UtteranceTranslation>[] = [
  {
    strategy: 'individual',
    getTargets: (entity) => [entity.id],
  },
  {
    strategy: 'group',
    getTargets: (entity) => [entity.utteranceId],
    condition: (entity) => Boolean(entity.utteranceId),
  },
];

const utteranceGroupConfigs: GroupInvalidationConfig[] = [
  {
    groupKey: 'utteranceId',
    getGroupId: (entity: UtteranceTranslation) => entity.utteranceId,
    relatedEntityTypes: ['IntentUtteranceTranslation'],
  },
];

// Create enhanced mixins
const utteranceMixins = createEntityMixins<
  UtteranceTranslation,
  'IntentUtteranceTranslation',
  typeof apiSlice,
  { intentId: string; langId: string }
>({
  entityType: 'IntentUtteranceTranslation',
  apiInstance: apiSlice,
  getSerializeQueryArgs: ({ queryArgs }) => {
    const { intentId, langId } = queryArgs;
    return `utterance-translations_${intentId}_${langId}`;
  },
  invalidationRules: utteranceInvalidationRules,
  groupConfigs: utteranceGroupConfigs,
  options: {
    itemEndpointName: 'getIntentUtteranceTranslation',
  },
});

/**
 * KEY INSIGHT: The enhanced system now properly updates cache data in onQueryStarted
 * 
 * Here's what happens when you update an utterance translation:
 * 
 * 1. OPTIMISTIC UPDATE: Cache is updated immediately with new data
 * 2. API CALL: Request is sent to server
 * 3. SUCCESS RESPONSE: Cache is updated with real server data
 * 4. GROUP UPDATES: All related items (same utteranceId) are updated
 * 5. TARGETED INVALIDATION: Only necessary tags are invalidated
 * 
 * Result: UI updates instantly, data stays in sync, minimal re-renders
 */

// Example API with enhanced cache updates
export const enhancedUtteranceApi = apiSlice.injectEndpoints({
  endpoints: (builder) => ({
    updateUtteranceTranslation: builder.mutation<
      { success: boolean; data: UtteranceTranslation },
      { id: string; text: string; utteranceId: string }
    >({
      query: ({ id, ...body }) => ({
        url: `/utterances/${id}`,
        method: 'PUT',
        body,
      }),
      // The enhanced update mixin now:
      // ✅ Updates cache data immediately
      // ✅ Handles group-based updates (same utteranceId)
      // ✅ Provides proper invalidation
      // ✅ Rolls back on errors
      ...utteranceMixins.update(),
    }),
  }),
});

/**
 * BEFORE vs AFTER Comparison:
 * 
 * BEFORE (Old System):
 * ❌ Only invalidated tags
 * ❌ UI waited for refetch
 * ❌ No group updates
 * ❌ Poor performance
 * ❌ Stale data in lists
 * 
 * AFTER (New System):
 * ✅ Updates cache data immediately
 * ✅ UI reflects changes instantly
 * ✅ Group-based cache updates
 * ✅ Optimized performance
 * ✅ Lists always in sync
 * ✅ Automatic error rollback
 */

// Example usage showing the difference:
export const useUtteranceUpdate = () => {
  const [updateUtterance] = enhancedUtteranceApi.useUpdateUtteranceTranslationMutation();

  const handleUpdate = async (id: string, text: string, utteranceId: string) => {
    try {
      // With the new system:
      // 1. UI updates immediately (optimistic update)
      // 2. All related utterances (same utteranceId) update too
      // 3. No waiting for refetch
      // 4. Perfect user experience
      const result = await updateUtterance({ id, text, utteranceId }).unwrap();
      
      console.log('✅ Update successful - UI updated immediately!');
      console.log('✅ All related utterances updated too!');
      return result;
    } catch (error) {
      console.error('❌ Update failed - cache rolled back automatically');
      throw error;
    }
  };

  return { handleUpdate };
};

/**
 * How to Use the Enhanced System:
 * 
 * 1. REPLACE your existing createOptimizedTags with createEntityMixins
 * 2. ADD invalidationRules and groupConfigs
 * 3. USE the enhanced mixins in your API endpoints
 * 4. ENJOY immediate UI updates and perfect data sync!
 */

// Migration example:
/*
// OLD WAY:
const tags = createOptimizedTags({ entityType: 'Entity' });

// NEW WAY:
const mixins = createEntityMixins({
  entityType: 'Entity',
  apiInstance: apiSlice,
  getSerializeQueryArgs: ({ queryArgs }) => `entity_${JSON.stringify(queryArgs)}`,
  invalidationRules: [
    {
      strategy: 'individual',
      getTargets: (entity) => [entity.id],
    },
    {
      strategy: 'group',
      getTargets: (entity) => [entity.groupId],
      condition: (entity) => Boolean(entity.groupId),
    },
  ],
  groupConfigs: [
    {
      groupKey: 'groupId',
      getGroupId: (entity) => entity.groupId,
      relatedEntityTypes: ['Entity'],
    },
  ],
});

// Then use mixins.update() instead of manual invalidation
*/

/**
 * The Root Cause of Your Issue:
 * 
 * The old system only handled invalidation (clearing cache) but didn't update
 * the actual cached data. So when an item was updated:
 * 
 * 1. Item cache was invalidated ❌
 * 2. List cache still had old data ❌
 * 3. UI showed stale data until refetch ❌
 * 
 * The new system fixes this by:
 * 
 * 1. Updating item cache with new data ✅
 * 2. Updating all list caches with new data ✅
 * 3. Updating related items (group-based) ✅
 * 4. UI shows fresh data immediately ✅
 */

export const cacheUpdateSolution = {
  problem: 'List data not updating after item mutations',
  solution: 'Enhanced onQueryStarted handlers that update cache data',
  benefits: [
    'Immediate UI updates',
    'Group-based cache updates',
    'Targeted invalidation',
    'Automatic error rollback',
    'Better performance',
    'Perfect data sync',
  ],
  migration: 'Replace createOptimizedTags with createEntityMixins',
};
