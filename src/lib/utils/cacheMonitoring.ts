import { CachePerformanceMetrics, CacheEvent, CacheEventListener } from './cacheTypes';

// Performance monitoring and debugging utilities
export class CacheMonitor {
  private metrics: Map<string, CachePerformanceMetrics> = new Map();
  private events: CacheEvent[] = [];
  private listeners: CacheEventListener[] = [];
  private maxEventHistory = 1000;

  // Record cache operation metrics
  recordOperation(
    entityType: string,
    operation: 'invalidation' | 'hit' | 'miss',
    executionTime?: number
  ): void {
    const existing = this.metrics.get(entityType) || {
      entityType,
      totalInvalidations: 0,
      averageInvalidationTime: 0,
      lastInvalidationTimestamp: 0,
      hitRate: 0,
      missRate: 0,
      cacheSize: 0,
    };

    switch (operation) {
      case 'invalidation':
        existing.totalInvalidations++;
        existing.lastInvalidationTimestamp = Date.now();
        if (executionTime) {
          existing.averageInvalidationTime = 
            (existing.averageInvalidationTime + executionTime) / 2;
        }
        break;
      case 'hit':
        existing.hitRate = (existing.hitRate + 1) / 2;
        break;
      case 'miss':
        existing.missRate = (existing.missRate + 1) / 2;
        break;
    }

    this.metrics.set(entityType, existing);
    this.emitEvent({
      type: operation,
      entityType,
      timestamp: Date.now(),
      metadata: { executionTime },
    });
  }

  // Get performance metrics for an entity type
  getMetrics(entityType: string): CachePerformanceMetrics | undefined {
    return this.metrics.get(entityType);
  }

  // Get all metrics
  getAllMetrics(): CachePerformanceMetrics[] {
    return Array.from(this.metrics.values());
  }

  // Reset metrics for an entity type
  resetMetrics(entityType?: string): void {
    if (entityType) {
      this.metrics.delete(entityType);
    } else {
      this.metrics.clear();
    }
  }

  // Event handling
  private emitEvent(event: CacheEvent): void {
    this.events.push(event);
    
    // Maintain event history limit
    if (this.events.length > this.maxEventHistory) {
      this.events.shift();
    }

    // Notify listeners
    this.listeners.forEach(listener => {
      if (listener.eventTypes.includes(event.type)) {
        try {
          listener.handler(event);
          if (listener.once) {
            this.removeEventListener(listener);
          }
        } catch (error) {
          console.error('Cache event listener error:', error);
        }
      }
    });
  }

  // Add event listener
  addEventListener(listener: CacheEventListener): void {
    this.listeners.push(listener);
  }

  // Remove event listener
  removeEventListener(listener: CacheEventListener): void {
    const index = this.listeners.indexOf(listener);
    if (index > -1) {
      this.listeners.splice(index, 1);
    }
  }

  // Get recent events
  getRecentEvents(limit = 100): CacheEvent[] {
    return this.events.slice(-limit);
  }

  // Get events by type
  getEventsByType(type: CacheEvent['type'], limit = 100): CacheEvent[] {
    return this.events
      .filter(event => event.type === type)
      .slice(-limit);
  }

  // Generate performance report
  generateReport(): {
    summary: {
      totalEntityTypes: number;
      totalInvalidations: number;
      averageHitRate: number;
      averageMissRate: number;
    };
    entityMetrics: CachePerformanceMetrics[];
    recentEvents: CacheEvent[];
  } {
    const allMetrics = this.getAllMetrics();
    
    return {
      summary: {
        totalEntityTypes: allMetrics.length,
        totalInvalidations: allMetrics.reduce((sum, m) => sum + m.totalInvalidations, 0),
        averageHitRate: allMetrics.reduce((sum, m) => sum + m.hitRate, 0) / allMetrics.length || 0,
        averageMissRate: allMetrics.reduce((sum, m) => sum + m.missRate, 0) / allMetrics.length || 0,
      },
      entityMetrics: allMetrics,
      recentEvents: this.getRecentEvents(50),
    };
  }
}

// Global cache monitor instance
export const cacheMonitor = new CacheMonitor();

// Debug utilities
export const cacheDebugUtils = {
  // Log cache operation with timing
  logOperation: <T>(
    operation: string,
    entityType: string,
    fn: () => T
  ): T => {
    const startTime = performance.now();
    console.group(`[Cache] ${operation} - ${entityType}`);
    
    try {
      const result = fn();
      const endTime = performance.now();
      const executionTime = endTime - startTime;
      
      console.log(`✅ Completed in ${executionTime.toFixed(2)}ms`);
      cacheMonitor.recordOperation(entityType, 'invalidation', executionTime);
      
      return result;
    } catch (error) {
      console.error('❌ Operation failed:', error);
      cacheMonitor.emitEvent({
        type: 'error',
        entityType,
        timestamp: Date.now(),
        error: error as Error,
      });
      throw error;
    } finally {
      console.groupEnd();
    }
  },

  // Visualize cache state
  visualizeCacheState: (entityType?: string): void => {
    const metrics = entityType 
      ? [cacheMonitor.getMetrics(entityType)].filter(Boolean)
      : cacheMonitor.getAllMetrics();

    console.table(metrics);
  },

  // Monitor invalidation patterns
  monitorInvalidations: (duration = 60000): Promise<CacheEvent[]> => {
    return new Promise((resolve) => {
      const events: CacheEvent[] = [];
      
      const listener: CacheEventListener = {
        eventTypes: ['invalidation'],
        handler: (event) => events.push(event),
      };

      cacheMonitor.addEventListener(listener);
      
      setTimeout(() => {
        cacheMonitor.removeEventListener(listener);
        resolve(events);
      }, duration);
    });
  },

  // Analyze invalidation efficiency
  analyzeInvalidationEfficiency: (entityType: string): {
    efficiency: number;
    recommendations: string[];
  } => {
    const metrics = cacheMonitor.getMetrics(entityType);
    if (!metrics) {
      return { efficiency: 0, recommendations: ['No metrics available'] };
    }

    const efficiency = metrics.hitRate / (metrics.hitRate + metrics.missRate) || 0;
    const recommendations: string[] = [];

    if (efficiency < 0.7) {
      recommendations.push('Consider optimizing invalidation strategies');
    }
    if (metrics.averageInvalidationTime > 100) {
      recommendations.push('Invalidation operations are slow, consider batching');
    }
    if (metrics.totalInvalidations > 1000) {
      recommendations.push('High invalidation frequency, review invalidation rules');
    }

    return { efficiency, recommendations };
  },
};

// Development-only cache debugging
if (process.env.NODE_ENV === 'development') {
  // Add global cache debugging utilities
  (window as any).__cacheDebug = {
    monitor: cacheMonitor,
    utils: cacheDebugUtils,
    report: () => cacheMonitor.generateReport(),
  };
}
