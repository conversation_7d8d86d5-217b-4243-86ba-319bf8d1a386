/**
 * Comprehensive examples showing how to use the new optimized cache system
 * 
 * This file demonstrates the key features you requested:
 * 1. Targeted invalidation for specific items
 * 2. Group-based invalidation (e.g., by utteranceId)
 * 3. Flexible and extensible invalidation strategies
 */

import { createEntityMixins, InvalidationRule, GroupInvalidationConfig } from '../optimizedTags';
import { utteranceInvalidation, assignFlowInvalidation } from '../utteranceInvalidation';
import { cacheMonitor, cacheDebugUtils } from '../cacheMonitoring';
import { apiSlice } from '../../../store/apiSlice';

// Example 1: Basic usage with targeted invalidation
export const basicTargetedInvalidationExample = () => {
  // When you call assignFlow, only that specific item gets invalidated
  const handleAssignFlow = async (intentId: string, flowId: string) => {
    try {
      // The mutation will automatically invalidate only the specific intent
      const result = await apiSlice.endpoints.assignFlowToIntent.initiate({
        intentId,
        flowId,
      });
      
      console.log('✅ AssignFlow completed - only specific intent invalidated');
      return result;
    } catch (error) {
      console.error('❌ AssignFlow failed:', error);
      throw error;
    }
  };

  return { handleAssignFlow };
};

// Example 2: Group-based invalidation for utterance translations
export const groupBasedInvalidationExample = () => {
  // When you update an utterance translation, all translations with the same utteranceId get invalidated
  const handleUpdateUtteranceTranslation = async (
    translationId: string,
    utteranceId: string,
    updates: { text: string }
  ) => {
    try {
      // This will invalidate:
      // 1. The specific translation (translationId)
      // 2. All translations sharing the same utteranceId
      const invalidationTags = utteranceInvalidation.invalidateByUtteranceId(utteranceId);
      
      console.log('🔄 Invalidating utterance group:', {
        utteranceId,
        tagsToInvalidate: invalidationTags.length,
      });

      // Your mutation logic here...
      // The enhanced system will handle the group invalidation automatically
      
      return { success: true };
    } catch (error) {
      console.error('❌ Update failed:', error);
      throw error;
    }
  };

  // Batch invalidation for multiple utterances
  const handleBatchUpdateUtterances = async (utteranceIds: string[]) => {
    const invalidationTags = utteranceInvalidation.batchInvalidateUtterances(utteranceIds);
    
    console.log('🔄 Batch invalidating utterances:', {
      count: utteranceIds.length,
      tagsToInvalidate: invalidationTags.length,
    });

    // Your batch update logic here...
    return { success: true };
  };

  return { 
    handleUpdateUtteranceTranslation,
    handleBatchUpdateUtterances,
  };
};

// Example 3: Custom invalidation rules for your specific use case
export const customInvalidationRulesExample = () => {
  // Define custom invalidation rules for a hypothetical "Project" entity
  const projectInvalidationRules: InvalidationRule[] = [
    // Rule 1: Individual item invalidation
    {
      strategy: 'individual',
      getTargets: (entity: any) => [entity.id],
    },
    
    // Rule 2: Group invalidation by team
    {
      strategy: 'group',
      getTargets: (entity: any) => [entity.teamId],
      condition: (entity: any) => Boolean(entity.teamId),
    },
    
    // Rule 3: Conditional invalidation based on status
    {
      strategy: 'conditional',
      getTargets: (entity: any) => entity.status === 'published' ? [entity.categoryId] : [],
      condition: (entity: any, context: any) => 
        context?.operation === 'update' && entity.status === 'published',
    },
    
    // Rule 4: Cascade invalidation for related entities
    {
      strategy: 'cascade',
      getTargets: (entity: any) => [entity.id],
      condition: (entity: any, context: any) => context?.operation === 'delete',
      cascadeRules: [
        {
          strategy: 'group',
          getTargets: (entity: any) => [`project-tasks:${entity.id}`],
        },
      ],
    },
  ];

  // Group configurations
  const projectGroupConfigs: GroupInvalidationConfig[] = [
    {
      groupKey: 'team',
      getGroupId: (entity: any) => entity.teamId,
      relatedEntityTypes: ['Project', 'Task'],
    },
    {
      groupKey: 'category',
      getGroupId: (entity: any) => entity.categoryId,
      relatedEntityTypes: ['Project'],
    },
  ];

  // Create entity mixins with custom rules
  const projectMixins = createEntityMixins({
    entityType: 'Project',
    apiInstance: apiSlice,
    getSerializeQueryArgs: ({ queryArgs }) => `projects_${JSON.stringify(queryArgs)}`,
    invalidationRules: projectInvalidationRules,
    groupConfigs: projectGroupConfigs,
  });

  return { projectMixins };
};

// Example 4: Performance monitoring and debugging
export const performanceMonitoringExample = () => {
  // Monitor cache performance
  const monitorCachePerformance = () => {
    // Add event listener for invalidations
    cacheMonitor.addEventListener({
      eventTypes: ['invalidation'],
      handler: (event) => {
        console.log('🔄 Cache invalidation:', {
          entityType: event.entityType,
          timestamp: new Date(event.timestamp).toISOString(),
          metadata: event.metadata,
        });
      },
    });

    // Generate performance report
    const report = cacheMonitor.generateReport();
    console.log('📊 Cache Performance Report:', report);
  };

  // Debug cache operations
  const debugCacheOperation = () => {
    // Log a cache operation with timing
    const result = cacheDebugUtils.logOperation(
      'Update Utterance Translation',
      'IntentUtteranceTranslation',
      () => {
        // Your cache operation here
        return utteranceInvalidation.invalidateByUtteranceId('utterance-123');
      }
    );

    console.log('🐛 Debug result:', result);
  };

  // Analyze invalidation efficiency
  const analyzeEfficiency = () => {
    const analysis = cacheDebugUtils.analyzeInvalidationEfficiency('IntentUtteranceTranslation');
    console.log('📈 Efficiency Analysis:', analysis);
  };

  return {
    monitorCachePerformance,
    debugCacheOperation,
    analyzeEfficiency,
  };
};

// Example 5: Real-world usage patterns
export const realWorldUsageExample = () => {
  // Scenario: User updates an utterance translation
  const handleUtteranceUpdate = async (
    translationId: string,
    utteranceId: string,
    newText: string
  ) => {
    console.log('🚀 Starting utterance update...');
    
    // Use the smart invalidation that considers the operation type
    const entity = { id: translationId, utteranceId, text: newText };
    const invalidationTags = utteranceInvalidation.smartInvalidate(entity, 'update');
    
    console.log('🎯 Smart invalidation will affect:', {
      specificTranslation: translationId,
      relatedTranslations: `All with utteranceId: ${utteranceId}`,
      totalTags: invalidationTags.length,
    });

    // Your actual update logic would go here
    // The RTK Query mutation will automatically use these invalidation tags
    
    return { success: true, invalidatedTags: invalidationTags.length };
  };

  // Scenario: Bulk operations with optimized invalidation
  const handleBulkUtteranceUpdate = async (updates: Array<{
    id: string;
    utteranceId: string;
    text: string;
  }>) => {
    console.log('🔄 Starting bulk update...');
    
    // Collect all unique utteranceIds for efficient group invalidation
    const uniqueUtteranceIds = [...new Set(updates.map(u => u.utteranceId))];
    
    // Use batch invalidation instead of individual calls
    const invalidationTags = utteranceInvalidation.batchInvalidateUtterances(uniqueUtteranceIds);
    
    console.log('⚡ Optimized bulk invalidation:', {
      totalUpdates: updates.length,
      uniqueUtteranceGroups: uniqueUtteranceIds.length,
      totalInvalidationTags: invalidationTags.length,
      efficiency: `${((uniqueUtteranceIds.length / updates.length) * 100).toFixed(1)}% reduction`,
    });

    return { success: true };
  };

  return {
    handleUtteranceUpdate,
    handleBulkUtteranceUpdate,
  };
};

// Export all examples for easy access
export const optimizedCacheExamples = {
  basicTargetedInvalidation: basicTargetedInvalidationExample,
  groupBasedInvalidation: groupBasedInvalidationExample,
  customInvalidationRules: customInvalidationRulesExample,
  performanceMonitoring: performanceMonitoringExample,
  realWorldUsage: realWorldUsageExample,
};
