/**
 * Enhanced languageApi.ts - Example of migrating to the new optimizedTags system
 * This shows how to handle list-only endpoints and bulk operations
 */

import {
  ApiResponse,
  BotLanguage,
  BulkCreateDeleteBotLanguagePayload,
  CreateBotLanguageRequest,
  Language,
  PaginatedResponse,
  PaginationParams,
  UuidParams,
} from '@/types';
import { apiSlice } from '../apiSlice';
import { serializePaginationParams } from '../../lib/utils/api';
import { 
  createListOnlyMixins, 
  createEntityMixins,
  createArgumentMappers,
  createEndpointConfigs 
} from '@/lib/utils/optimizedTags';

const updatedApiSlice = apiSlice.enhanceEndpoints({
  addTagTypes: ['Language', 'BotLanguage'],
});

// Language mixins - list-only pattern since languages are mostly read-only
const languageMixins = createListOnlyMixins<
  Language,
  'Language',
  typeof updatedApiSlice,
  PaginationParams
>(
  'Language',
  updatedApiSlice,
  ({ queryArgs }) => `languages_${JSON.stringify(queryArgs)}`
);

// BotLanguage mixins - full CRUD with bulk operations support
const botLanguageMixins = createEntityMixins<
  BotLanguage,
  'BotLanguage',
  typeof updatedApiSlice,
  PaginationParams,
  any, // UpdateArg - can be single or bulk
  any  // CreateArg - can be single or bulk
>({
  entityType: 'BotLanguage',
  apiInstance: updatedApiSlice,
  getSerializeQueryArgs: ({ queryArgs }) => {
    const { filter } = queryArgs;
    // Group by botId for efficient cache management
    const botId = filter?.botId || 'all';
    return `bot-languages_${botId}_${JSON.stringify(queryArgs)}`;
  },
  options: createEndpointConfigs.standard('BotLanguage'),
  argumentMapper: {
    // Handle both single and bulk operations
    extractId: (arg: any) => {
      if (arg.ids && Array.isArray(arg.ids)) {
        return `bulk_${arg.botId}_${arg.ids.join(',')}`;
      }
      return arg.id || arg.botId;
    },
    shouldUpdateCache: (queryArgs: any, mutationArgs: any) => {
      // Update cache if botId matches
      const queryBotId = queryArgs.filter?.botId;
      const mutationBotId = mutationArgs.botId;
      return !queryBotId || queryBotId === mutationBotId;
    },
  },
  // Group by botId for related invalidations
  groupConfigs: [
    {
      groupKey: 'botId',
      getGroupId: (entity: BotLanguage) => entity.botId,
      relatedEntityTypes: ['BotLanguage'],
    },
  ],
});

export const languageApiEnhanced = updatedApiSlice.injectEndpoints({
  endpoints: builder => ({
    // Languages - using list-only mixins
    getLanguages: builder.query<ApiResponse<PaginatedResponse<Language>>, PaginationParams>({
      query: params => {
        const { searchParams } = serializePaginationParams(params);
        return {
          url: `/languages?${searchParams}`,
        };
      },
      ...languageMixins.paginated,
    }),
    
    getLanguage: builder.query<ApiResponse<Language>, UuidParams>({
      query: ({ id }) => ({ url: `/languages/${id}` }),
      // For list-only mixins, we manually provide tags since there's no item mixin
      providesTags: (_result, _error, { id }) => [
        { type: 'Language', id },
      ],
    }),

    // Bot Languages - using full CRUD mixins
    getBotLanguages: builder.query<ApiResponse<PaginatedResponse<BotLanguage>>, PaginationParams>({
      query: params => {
        const { searchParams } = serializePaginationParams(params);
        return {
          url: `/bot-languages?${searchParams}`,
        };
      },
      ...botLanguageMixins.paginated,
    }),
    
    getBotLanguage: builder.query<ApiResponse<BotLanguage>, UuidParams>({
      query: ({ id }) => ({ url: `/bot-languages/${id}` }),
      ...botLanguageMixins.item,
    }),
    
    createBotLanguage: builder.mutation<ApiResponse<BotLanguage>, CreateBotLanguageRequest>({
      query: ({ botId, langId }) => ({
        url: `/bot-languages/${botId}`,
        method: 'POST',
        body: { langId },
      }),
      ...botLanguageMixins.create(),
    }),
    
    deleteBotLanguage: builder.mutation<void, UuidParams>({
      query: ({ id }) => ({
        url: `/bot-languages/${id}`,
        method: 'DELETE',
      }),
      ...botLanguageMixins.delete(),
    }),
    
    // Bulk operations - enhanced with proper cache management
    createBulkBotLanguages: builder.mutation<
      ApiResponse<BotLanguage[]>,
      BulkCreateDeleteBotLanguagePayload
    >({
      query: ({ botId, ids }) => ({
        url: `/bot-languages/${botId}/bulk`,
        method: 'POST',
        body: { ids },
      }),
      // Use create mixin but with custom group invalidation
      ...botLanguageMixins.create({
        getGroupId: (arg: BulkCreateDeleteBotLanguagePayload) => ['botId', arg.botId],
      }),
    }),
    
    deleteBulkBotLanguages: builder.mutation<void, BulkCreateDeleteBotLanguagePayload>({
      query: ({ botId, ids }) => ({
        url: `/bot-languages/${botId}/bulk`,
        method: 'DELETE',
        body: { ids },
      }),
      // Custom invalidation for bulk delete
      invalidatesTags: (_result, _error, { botId, ids }) => [
        // Invalidate the list for this bot
        ...botLanguageMixins.invalidateByGroup('botId', botId),
        // Invalidate individual items if we know their IDs
        ...ids.map(id => ({ type: 'BotLanguage' as const, id })),
      ],
      async onQueryStarted({ botId, ids }, { dispatch, queryFulfilled, getState }) {
        // Optimistically update cache by removing items
        const patchResults: Array<{ undo: () => void }> = [];
        
        try {
          const state = getState();
          const apiState = (state as any).api;
          
          // Find all relevant list caches and remove the items
          Object.keys(apiState.queries).forEach(key => {
            const query = apiState.queries[key];
            if (query?.endpointName === 'getBotLanguages') {
              const cacheKeyArgs = query.originalArgs;
              
              // Only update if this cache is for the same bot
              if (!cacheKeyArgs.filter?.botId || cacheKeyArgs.filter.botId === botId) {
                const patchResult = dispatch(
                  updatedApiSlice.util.updateQueryData(
                    'getBotLanguages',
                    cacheKeyArgs,
                    (draft: ApiResponse<PaginatedResponse<BotLanguage>>) => {
                      if (draft?.data?.items) {
                        // Remove items with matching langIds
                        draft.data.items = draft.data.items.filter(
                          item => !ids.includes(item.langId)
                        );
                        // Update pagination count
                        if (draft.data.pagination) {
                          draft.data.pagination.total = Math.max(
                            0, 
                            draft.data.pagination.total - ids.length
                          );
                        }
                      }
                    }
                  )
                );
                patchResults.push(patchResult as { undo: () => void });
              }
            }
          });
          
          await queryFulfilled;
        } catch {
          // Undo optimistic updates on error
          patchResults.forEach(result => result.undo());
        }
      },
    }),
  }),
});

export const {
  // Languages
  useGetLanguagesQuery,
  useGetLanguageQuery,

  // Bot Languages
  useGetBotLanguagesQuery,
  useGetBotLanguageQuery,
  useCreateBotLanguageMutation,
  useDeleteBotLanguageMutation,
  useCreateBulkBotLanguagesMutation,
  useDeleteBulkBotLanguagesMutation,
} = languageApiEnhanced;