import { vi } from 'vitest';

const availableChannels = [
  {
    id: 'whatsapp',
    name: 'WhatsApp',
    channelType: 'text',
    status: 'available',
    logo: 'whatsapp.png',
    isVerified: true,
    description: 'Connect with customers on WhatsApp',
    channelKey: 'whatsapp',
  },
  {
    id: 'telegram',
    name: 'Telegram',
    channelType: 'text',
    status: 'available',
    logo: 'telegram.png',
    description: 'Reach customers on Telegram',
    channelKey: 'telegram',
  },
  {
    id: 'webhook',
    name: 'Webhook',
    channelType: 'native',
    status: 'available',
    logo: 'webhook.png',
    description: 'Custom webhook integration',
    channelKey: 'webhook',
  },
  {
    id: 'alexa',
    name: '<PERSON><PERSON>',
    channelType: 'voice',
    status: 'available',
    logo: 'amazon_alexa.png',
    description: 'Voice interactions with <PERSON><PERSON>',
    channelKey: 'alexa',
  },
];

const myChannels = [
  {
    channelId: 'whatsapp',
    channelName: 'WhatsApp',
    channelType: 'text',
    botChannelStatus: 'connected',
    logo: 'whatsapp.png',
    isVerified: true,
  },
];

export const useGetBotChannelDetailsQuery = vi.fn().mockReturnValue({
  data: null,
  isLoading: false,
});

export const useGetChannelsQuery = vi.fn().mockReturnValue({
  data: {
    data: {
      items: availableChannels,
    },
  },
  isLoading: false,
  refetch: vi.fn(),
});

export const useGetAllBotChannelsQuery = vi.fn().mockReturnValue({
  data: {
    data: myChannels,
  },
  isLoading: false,
  refetch: vi.fn(),
});
