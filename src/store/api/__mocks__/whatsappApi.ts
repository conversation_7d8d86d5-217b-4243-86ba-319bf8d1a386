import { vi } from 'vitest';

export const useGetWhatsappProfilesQuery = vi.fn().mockReturnValue({
  data: {
    data: {
      items: [
        {
          id: '1',
          phone_number_id: '1',
          wa_number: '+91 9642456783',
          name: 'Test Number 1',
        },
        {
          id: '2',
          phone_number_id: '2',
          wa_number: '+91 8456822104',
          name: 'Test Number 2',
        },
      ],
    },
  },
  isLoading: false,
  refetch: vi.fn(),
});

const mockConnectMutation = vi.fn(() => ({
  unwrap: () =>
    Promise.resolve({
      data: {
        webhookUrl: 'https://ngage.cpaas.com/v1/bot/wellness_care',
        wa_number: '+91 9642456783',
      },
    }),
}));

export const useConnectWABANumberToBotMutation = vi.fn().mockReturnValue([mockConnectMutation]);
