import { ApplicationData } from '@/modules/editor/types';
import type { FlowVersion, Template, ApiResponse } from '@/types';
import { apiSlice } from '../apiSlice';

const updatedApiSlice = apiSlice.enhanceEndpoints({
  addTagTypes: ['Application', 'Template', 'AppDetails'],
});

export const studioApi = updatedApiSlice.injectEndpoints({
  endpoints: builder => ({
    updateApplicationDetails: builder.mutation<ApiResponse, { appId: string; payload: any }>({
      query: ({ appId, payload }) => {
        return {
          url: `/apps/${appId}`,
          method: 'PUT',
          body: payload,
        };
      },
      invalidatesTags: ['Application'],
    }),

    getApplicationDetails: builder.query<ApplicationData, { appId: string }>({
      query: ({ appId }) => {
        return {
          url: `/apps/${appId}`,
          method: 'GET',
        };
      },
      providesTags: ['Application'],
    }),

    // Templates
    getTemplates: builder.query<{ data: Template[] }, void>({
      query: () => ({ url: 'leap_gw/apps/getTemplatesForStudio' }),
      providesTags: ['Template'],
    }),

    cloneTemplate: builder.mutation<
      { id: string },
      {
        name: string;
        appTemplateId: string;
        svg: string;
      }
    >({
      query: data => ({
        url: 'leap_gw/apps/cloneTemplate',
        method: 'POST',
        body: data,
      }),
      invalidatesTags: ['Application'],
    }),

    // Platform Admin
    updateOTCAndMRC: builder.mutation<
      void,
      {
        id: string;
        nodeExecCharge: number;
        freeNodeExec: number;
        OTC: number;
        MRC: number;
      }
    >({
      query: data => ({
        url: 'leap_gw/apps/updateOTCAndMRC',
        method: 'POST',
        body: data,
      }),
      invalidatesTags: ['Application'],
    }),

    // Logs
    getAppReports: builder.query<any, { appId: string }>({
      query: ({ appId }) => ({
        url: 'leap_gw/apps/getAppReports',
        method: 'POST',
        body: { appId },
      }),
    }),

    getDetailedAppReport: builder.query<
      any,
      {
        appId: string;
        page: number;
        size: number;
      }
    >({
      query: ({ appId, page, size }) => ({
        url: `leap_gw/apps/getDetailedAppReport?page=${page}&size=${size}`,
        method: 'POST',
        body: { appId, name: 'getAllData' },
      }),
    }),

    getFlowVersions: builder.query<ApiResponse<FlowVersion[]>, { appId: string }>({
      query: ({ appId }) => ({
        url: `/apps/${appId}/versions`,
        method: 'GET',
      }),
    }),
    getFlowVersionData: builder.query<
      ApiResponse<ApplicationData>,
      { appId: string; version: string }
    >({
      query: ({ appId, version }) => ({
        url: `/apps/${appId}/${version}`,
        method: 'GET',
      }),
    }),
  }),
});

export const {
  useGetApplicationDetailsQuery,
  useGetTemplatesQuery,
  useCloneTemplateMutation,
  useUpdateOTCAndMRCMutation,
  useGetAppReportsQuery,
  useGetDetailedAppReportQuery,
  useUpdateApplicationDetailsMutation,

  useGetFlowVersionsQuery,
  useGetFlowVersionDataQuery,
} = studioApi;
