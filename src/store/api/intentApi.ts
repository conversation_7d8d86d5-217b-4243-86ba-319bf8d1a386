import {
  ApiResponse,
  AssignFlowToIntentRequest,
  CreateIntentItemRequest,
  CreateUtteranceTranslationRequest,
  IntentItem,
  IntentUtteranceByUtteranceIdParams,
  IntentUtteranceGetAllParams,
  IntentUtteranceTranslation,
  PaginatedResponse,
  PaginationParams,
  UpdateIntentItemRequest,
  UpdateUtteranceTranslationRequest,
  UtteranceTranslationByLangParam,
  UuidParams,
} from '@/types';
import { apiSlice } from '../apiSlice';
import { serializePaginationParams } from '../../lib/utils/api';
import {
  createEntityMixins,
  InvalidationRule,
  GroupInvalidationConfig,
} from '../../lib/utils/optimizedTags';
import {
  utteranceInvalidation,
  assignFlowInvalidation,
} from '../../lib/utils/utteranceInvalidation';

const updatedApiSlice = apiSlice.enhanceEndpoints({
  addTagTypes: ['IntentItem', 'IntentUtteranceTranslation'],
});

const intentFlowKey = 'flowId';

// IntentItem configuration
const intentItemMixins = createEntityMixins<
  IntentItem,
  'IntentItem',
  typeof updatedApiSlice,
  PaginationParams
>({
  entityType: 'IntentItem',
  apiInstance: updatedApiSlice,
  getSerializeQueryArgs: ({ queryArgs }) => {
    const { filter } = queryArgs;

    return `intent-items_${JSON.stringify(filter)}`;
  },
  groupConfigs: [
    {
      groupKey: intentFlowKey,
      getGroupId: (arg: IntentItem) => arg.flowId,
      relatedEntityTypes: ['IntentItem'],
    },
  ],
});

// Enhanced invalidation rules for utterance translations
const utteranceInvalidationRules: InvalidationRule<IntentUtteranceTranslation>[] = [
  // Individual item invalidation
  {
    strategy: 'individual',
    getTargets: entity => [entity.id],
  },
  // Group invalidation by utteranceId - key feature for your use case
  {
    strategy: 'group',
    getTargets: entity => [entity.utteranceId],
    condition: entity => Boolean(entity.utteranceId),
  },
  // Language-specific invalidation
  {
    strategy: 'selective',
    getTargets: entity => [`lang:${entity.langId}`],
    condition: entity => Boolean(entity.langId),
  },
];

// Group configurations for utterance translations
const utteranceGroupConfigs: GroupInvalidationConfig[] = [
  {
    groupKey: 'utteranceId',
    getGroupId: (entity: IntentUtteranceTranslation) => entity.utteranceId,
    relatedEntityTypes: ['IntentUtteranceTranslation'],
  },
];

// IntentUtteranceTranslation configuration with enhanced invalidation
const utteranceMixins = createEntityMixins<
  IntentUtteranceTranslation,
  'IntentUtteranceTranslation',
  typeof updatedApiSlice,
  IntentUtteranceGetAllParams
>({
  entityType: 'IntentUtteranceTranslation',
  apiInstance: updatedApiSlice,
  getSerializeQueryArgs: ({ queryArgs, endpointName }) => {
    if (endpointName === 'getTranslationByUtteranceIdAndLangId') {
      const { utteranceId, langId } = queryArgs as UtteranceTranslationByLangParam;
      return `intent-utterance-translation_${utteranceId}_${langId}`;
    }
    const {
      intentId,
      langId,
      query: { page, ...rest } = {},
    } = queryArgs as IntentUtteranceGetAllParams;
    return `intent-utterance-translations_${intentId}_${langId}_${JSON.stringify(rest)}`;
  },
  shouldUpdateCache: (queryArgs, initialArgs) => {
    return queryArgs.intentId === initialArgs.intentId && queryArgs.langId === initialArgs.langId;
  },
  invalidationRules: utteranceInvalidationRules,
  groupConfigs: utteranceGroupConfigs,
  getItemQueryArgs: entity => ({
    intentId: entity.intentId,
    langId: entity.langId,
    utteranceId: entity.utteranceId,
  }),
  
});

export const intentApi = updatedApiSlice.injectEndpoints({
  endpoints: builder => ({
    getIntentItems: builder.query<ApiResponse<PaginatedResponse<IntentItem>>, PaginationParams>({
      query: params => {
        const { searchParams } = serializePaginationParams(params);
        return {
          url: `/intent-items?${searchParams}`,
        };
      },
      ...intentItemMixins.paginated,
    }),

    getIntentItem: builder.query<ApiResponse<IntentItem>, UuidParams>({
      query: ({ id }) => ({ url: `/intent-items/${id}` }),
      ...intentItemMixins.item,
    }),
    createIntentItem: builder.mutation<ApiResponse<IntentItem>, CreateIntentItemRequest>({
      query: body => ({
        url: '/intent-items',
        method: 'POST',
        body,
      }),
      ...intentItemMixins.create(),
    }),
    updateIntentItem: builder.mutation<
      ApiResponse<IntentItem>,
      UuidParams & UpdateIntentItemRequest
    >({
      query: ({ id, ...body }) => ({
        url: `/intent-items/${id}`,
        method: 'PUT',
        body,
      }),
      ...intentItemMixins.update(),
    }),
    deleteIntentItem: builder.mutation<void, UuidParams>({
      query: ({ id }) => ({
        url: `/intent-items/${id}`,
        method: 'DELETE',
      }),
      ...intentItemMixins.delete(),
    }),
    assignFlowToIntent: builder.mutation<ApiResponse<unknown>, AssignFlowToIntentRequest>({
      query: body => ({
        url: '/intent-items/assign-flow',
        method: 'POST',
        body,
      }),
      ...intentItemMixins.update({
        getId: (arg: AssignFlowToIntentRequest) => arg.intentId,
        getGroupId: (arg: AssignFlowToIntentRequest) => [intentFlowKey, arg.flowId],
      }),
    }),

    getUtteranceTranslationsCount: builder.query<ApiResponse<number>, { intentId: string }>({
      query: ({ intentId }) => ({
        url: `/utterance-translations/count/intent/${intentId}`,
        method: 'GET',
      }),
    }),

    // Intent Utterance Translations
    getIntentUtteranceTranslations: builder.query<
      ApiResponse<PaginatedResponse<IntentUtteranceTranslation>>,
      IntentUtteranceGetAllParams
    >({
      query: ({ intentId, langId, query = {} }) => {
        const { searchParams } = serializePaginationParams(query);
        return {
          url: `/intent/${intentId}/lang/${langId}/intent-utterance?${searchParams}`,
        };
      },
      ...utteranceMixins.paginated,
    }),

    getIntentUtteranceTranslation: builder.query<
      ApiResponse<PaginatedResponse<IntentUtteranceTranslation>>,
      IntentUtteranceByUtteranceIdParams
    >({
      query: ({ intentId, langId, utteranceId }) => {
        const { searchParams } = serializePaginationParams({
          filter: {
            id: { eq: utteranceId },
          },
        });
        return {
          url: `/intent/${intentId}/lang/${langId}/intent-utterance?${searchParams}`,
        };
      },

      ...utteranceMixins.item,
      providesTags: (result, error, { intentId, langId, utteranceId }) => [
        { type: 'IntentUtteranceTranslation', id: `${intentId}-${langId}-${utteranceId}` },
      ],
    }),

    getIntentUtteranceTranslationById: builder.query<
      ApiResponse<IntentUtteranceTranslation>,
      UuidParams
    >({
      query: ({ id }) => ({
        url: `/intent-utterance/${id}`,
      }),
    }),
    createIntentUtteranceTranslation: builder.mutation<
      ApiResponse<IntentUtteranceTranslation>,
      CreateUtteranceTranslationRequest
    >({
      query: ({ intentId, langId, ...body }) => ({
        url: `/intent/${intentId}/lang/${langId}/intent-utterance`,
        method: 'POST',
        body,
      }),
      ...utteranceMixins.create({
        getGroupId: arg => ['utteranceId', arg.utteranceId!],
      }),
    }),
    updateIntentUtteranceTranslation: builder.mutation<
      ApiResponse<IntentUtteranceTranslation>,
      UuidParams & UpdateUtteranceTranslationRequest
    >({
      query: ({ id, ...body }) => ({
        url: `/intent-utterance/${id}`,
        method: 'PUT',
        body,
      }),
      ...utteranceMixins.update(),
    }),
    deleteIntentUtteranceTranslation: builder.mutation<void, UuidParams>({
      query: ({ id }) => ({
        url: `/intent-utterance/${id}`,
        method: 'DELETE',
      }),
      ...utteranceMixins.delete(),
    }),
    getTranslationByUtteranceIdAndLangId: builder.query<
      ApiResponse<IntentUtteranceTranslation>,
      UtteranceTranslationByLangParam
    >({
      query: ({ utteranceId, langId }) => ({
        url: `/utterance/${utteranceId}/lang/${langId}/translation`,
      }),
      providesTags: (result, error, { utteranceId, langId }) => [
        { type: 'IntentUtteranceTranslation', id: `${utteranceId}-${langId}` },
      ],
      // ...utteranceMixins.item, //TODO: need to handl this properly
    }),
  }),
});

export const {
  useGetIntentItemsQuery,
  useGetIntentItemQuery,
  useCreateIntentItemMutation,
  useUpdateIntentItemMutation,
  useDeleteIntentItemMutation,
  useAssignFlowToIntentMutation,
  useGetUtteranceTranslationsCountQuery,

  useGetIntentUtteranceTranslationsQuery,
  useGetIntentUtteranceTranslationQuery,
  useCreateIntentUtteranceTranslationMutation,
  useUpdateIntentUtteranceTranslationMutation,
  useDeleteIntentUtteranceTranslationMutation,
  useGetTranslationByUtteranceIdAndLangIdQuery,
} = intentApi;
