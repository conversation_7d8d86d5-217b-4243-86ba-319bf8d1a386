import {
  ApiResponse,
  CreateFaqCategoryRequest,
  CreateFaqTranslationRequest,
  FaqCategory,
  FaqItem,
  FaqTranslation,
  FaqTranslationByLangParam,
  GetFaqsByCategoryAndLanguageParams,
  PaginatedResponse,
  PaginationParams,
  UpdateFaqCategoryRequest,
  UpdateFaqTranslationRequest,
  UuidParams,
  CreateFaqResponseData,
  UpdateFaqResponseData,
} from '@/types';
import { apiSlice } from '../apiSlice';
import { serializePaginationParams } from '../../lib/utils/api';

import { createEntityMixins } from '../../lib/utils/optimizedTags';

const updatedApiSlice = apiSlice.enhanceEndpoints({
  addTagTypes: ['FaqCategory', 'FaqTranslation'],
});

// FaqCategory configuration
const faqCategoryMixins = createEntityMixins<
  FaqCategory,
  'FaqCategory',
  typeof updatedApiSlice,
  PaginationParams
>({
  entityType: 'FaqCategory',
  apiInstance: updatedApiSlice,
  getSerializeQueryArgs: ({ queryArgs }) => {
    const {
      filter: { botId },
    } = queryArgs;

    return `faq-categories_${JSON.stringify(botId)}`;
  },
  options: {
    listEndpointName: 'getFaqCategories',
  },
});

export const faqApi = updatedApiSlice.injectEndpoints({
  endpoints: builder => ({
    // FAQ Categories
    getFaqCategories: builder.query<ApiResponse<PaginatedResponse<FaqCategory>>, PaginationParams>({
      query: params => {
        const { searchParams } = serializePaginationParams(params);
        return {
          url: `/faq-categories?${searchParams}`,
        };
      },
      ...faqCategoryMixins.paginated,
    }),
    getFaqCategory: builder.query<ApiResponse<FaqCategory>, UuidParams>({
      query: ({ id }) => ({ url: `/faq-categories/${id}` }),
      ...faqCategoryMixins.item,
    }),
    createFaqCategory: builder.mutation<ApiResponse<FaqCategory>, CreateFaqCategoryRequest>({
      query: body => ({
        url: '/faq-categories',
        method: 'POST',
        body,
      }),
      ...faqCategoryMixins.create(),
    }),
    updateFaqCategory: builder.mutation<
      ApiResponse<FaqCategory>,
      UuidParams & UpdateFaqCategoryRequest
    >({
      query: ({ id, ...body }) => ({
        url: `/faq-categories/${id}`,
        method: 'PUT',
        body,
      }),
      ...faqCategoryMixins.update(),
    }),
    deleteFaqCategory: builder.mutation<void, UuidParams>({
      query: ({ id }) => ({
        url: `/faq-categories/${id}`,
        method: 'DELETE',
      }),
      ...faqCategoryMixins.delete(),
    }),

    getFaqTranslationsCount: builder.query<ApiResponse<number>, { categoryId: string }>({
      query: ({ categoryId }) => ({
        url: `/faq-translations/count/category/${categoryId}`,
        method: 'GET',
      }),
    }),

    // FAQ Translations
    getFaqsByCategoryAndLanguage: builder.query<
      ApiResponse<PaginatedResponse<FaqItem>>,
      GetFaqsByCategoryAndLanguageParams
    >({
      query: ({ categoryId, langId, ...params }) => {
        const { searchParams } = serializePaginationParams(params);
        return {
          url: `/faqs/category/${categoryId}/language/${langId}?${searchParams}`,
        };
      },
      // ...faqTranslationMixins.paginated,
      providesTags: ['FaqTranslation'],
    }),
    getTranslationByFaqIdAndLangId: builder.query<
      ApiResponse<FaqTranslation>,
      FaqTranslationByLangParam
    >({
      query: ({ faqId, langId }) => ({
        url: `/faq/${faqId}/lang/${langId}/translation`,
      }),
      // ...faqTranslationMixins.item,
      providesTags: (_result, _error, { faqId, langId }) => [
        { type: 'FaqTranslation', id: `${faqId}-${langId}` },
      ],
    }),
    createFaqTranslation: builder.mutation<
      ApiResponse<CreateFaqResponseData>,
      CreateFaqTranslationRequest
    >({
      query: body => ({
        url: '/faq-translations',
        method: 'POST',
        body,
      }),
      // ...faqTranslationMixins.create,
      invalidatesTags: ['FaqTranslation'],
    }),
    updateFaqTranslation: builder.mutation<
      ApiResponse<UpdateFaqResponseData>,
      UuidParams & UpdateFaqTranslationRequest
    >({
      query: ({ id, ...body }) => ({
        url: `/faq-translations/${id}`,
        method: 'PUT',
        body,
      }),
      // ...faqTranslationMixins.update,
      invalidatesTags: ['FaqTranslation'],
    }),
    deleteFaqTranslation: builder.mutation<void, UuidParams>({
      query: ({ id }) => ({
        url: `/faq-translations/${id}`,
        method: 'DELETE',
      }),
      // ...faqTranslationMixins.delete,
      invalidatesTags: ['FaqTranslation'],
    }),
  }),
});

export const {
  useGetFaqCategoriesQuery,
  useGetFaqCategoryQuery,
  useCreateFaqCategoryMutation,
  useUpdateFaqCategoryMutation,
  useDeleteFaqCategoryMutation,
  useGetFaqsByCategoryAndLanguageQuery,

  useCreateFaqTranslationMutation,
  useUpdateFaqTranslationMutation,
  useDeleteFaqTranslationMutation,
  useGetTranslationByFaqIdAndLangIdQuery,
  useGetFaqTranslationsCountQuery,
} = faqApi;
