import { describe, it, expect } from 'vitest';
import { store } from '../store';

describe('Store', () => {
  it('should create store with initial state', () => {
    expect(store).toBeDefined();
    expect(store.getState()).toBeDefined();
  });

  it('should have api reducer', () => {
    const state = store.getState();
    expect(state.api).toBeDefined();
  });

  it('should have auth reducer', () => {
    const state = store.getState();
    expect(state.auth).toBeDefined();
  });

  it('should have ui reducer', () => {
    const state = store.getState();
    expect(state.ui).toBeDefined();
  });

  it('should have flows reducer', () => {
    const state = store.getState();
    expect(state.flows).toBeDefined();
  });

  it('should dispatch actions', () => {
    const action = { type: 'TEST_ACTION', payload: 'test' };
    expect(() => store.dispatch(action)).not.toThrow();
  });

  it('should subscribe to state changes', () => {
    let called = false;
    const unsubscribe = store.subscribe(() => {
      called = true;
    });

    store.dispatch({ type: 'TEST_ACTION' });
    expect(called).toBe(true);

    unsubscribe();
  });

  it('should have middleware configured', () => {
    // Test that middleware is working by dispatching an action
    const initialState = store.getState();
    store.dispatch({ type: 'TEST_MIDDLEWARE' });

    // State should be defined after dispatch
    expect(store.getState()).toBeDefined();
  });
});
